import React, { FC, useMemo, useState, useEffect, Suspense } from 'react';
import {
  Box,
  Skeleton,
  Stack,
  MenuItem,
  TextField,
  Autocomplete,
  Alert,
  Button,
  Snackbar,
  CircularProgress,
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import CancelIcon from '@mui/icons-material/Cancel';
import keyBy from 'lodash/keyBy';
import isEmpty from 'lodash/isEmpty';
import { useSnackbar } from 'notistack';
import { useNavigate } from 'react-router-dom';
import spacetime from 'spacetime';
import HTTP_STATUS from '../../constants/httpCodes';

import errors from '../../constants/errors';
import { CONFIG_LIST_PATH } from '../../constants/routes';
import {
  TabProps,
  RevisionItem,
  ApprovalRejectPayload,
  EntityPending,
} from '../../constants/types';
import {
  useGetInstanceDetails,
  useGetRevisions,
  useGetEntityPendingData,
  usePutApprovalReject,
  useGetEntitySettingsStatus,
} from '../../services/use-query';
import {
  hasApprovalRequirement,
  hasFeatureFlag,
  hasUserRole,
  toRevisionDate,
} from '../../utils/helpers';
import { schemaParser } from '../../components/DynamicEditor/utils';
import useHasPermissions from '../../hooks/useHasPermissions';
import UserRoles from '../../constants/userRoles';
import FeatureFlags from '../../constants/featureFlags';
import ScheduleDeploymentAlert from '../../components/ScheduleDeploymentAlert';
import RejectionDialog from '../../components/RejectionDialog';
import lazyWithPreload from '../../utils/lazyWithPreload';
import PropertyNavigation from '../../components/PropertyNavigation';
import RevisionDeploymentTypeDialog from '../../components/RevisionDeploymentTypeModal';
import { RevisionDeploymentTypeDialogStep } from '../../components/RevisionDeploymentTypeModal/types';
import RevisionViewer from './RevisionViewer';

const RevisionEditor = lazyWithPreload(() => import('./RevisionEditor'));

RevisionEditor.preload();

const { DRAFT, REVISION } = errors;

const draftRevisionItemId = 'draft';
const scheduleRevisionItemId = 'schedule';

const Revision: FC<TabProps> = ({ id }) => {
  const [propertyNavigationRefs, setPropertyNavigationRefs] = useState({});
  const [rejectionError, setRejectionError] = useState(false);
  const [isRevisionLoading, setIsRevisionLoading] = useState(false);
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();

  const {
    data: dataInstanceDetails,
    error: errorFetchingInstanceDetails,
    isLoading: isLoadingInstanceDetails,
    isError: isErrorInstanceDetails,
    refetch: refetchInstanceDetails,
  } = useGetInstanceDetails(id);

  const {
    data: dataRevisions,
    isLoading: isLoadingRevisions,
    isError: isErrorRevisions,
    refetch: refetchRevisions,
  } = useGetRevisions(id);

  const ENTITY_TYPE = 'config-instance-draft';
  const ENTITY_STATUS = ['pending'];

  const {
    data: dataEntityPending,
    isLoading: isLoadingEntity,
    refetch: refetchEntityPendingData,
  } = useGetEntityPendingData(
    {
      configInstanceId: id,
      entityType: ENTITY_TYPE,
      status: ENTITY_STATUS,
    },
    {
      cacheTime: 0,
      staleTime: 0,
      refetchOnMount: true,
      refetchOnWindowFocus: true,
    }
  );

  const { data: dataEntitySettingsStatus } = useGetEntitySettingsStatus(
    'config-instance-draft',
    {
      cacheTime: 0,
      staleTime: 0,
      refetchOnMount: true,
      refetchOnWindowFocus: true,
    }
  );

  // to find the isEnabled for the specific configFileId
  const isApprovalEnabled = useMemo(() => {
    if (!dataEntitySettingsStatus || !dataInstanceDetails?.configFileId)
      return false;

    const match = dataEntitySettingsStatus.find(
      item =>
        item?.condition?.configFileId === dataInstanceDetails?.configFileId
    );

    return match?.isEnabled ?? false;
  }, [dataEntitySettingsStatus, dataInstanceDetails?.configFileId]);

  const { mutate: putApprovalRejectMutate } = usePutApprovalReject();

  const [entityPendingObject, setEntityPendingObject] =
    useState<EntityPending | null>(null);

  const getObjectByCustomAttributeDefinitionId = data => {
    if (!data || !data.results) return null;
    return data.results[0];
  };

  const approvalMessage = useMemo(() => {
    if (entityPendingObject == null) {
      return 'This configuration file requires approval before any changes are deployed';
    }
    return 'This configuration file is pending approval. You cannot make any changes until its status is updated by approver.';
  }, [entityPendingObject]);

  useEffect(() => {
    if (dataEntityPending) {
      const obj = getObjectByCustomAttributeDefinitionId(dataEntityPending);
      setEntityPendingObject(obj);
    }
  }, [dataEntityPending, id, isLoadingEntity]);

  const hasCompanyAdmin = useHasPermissions({
    userRoles: [UserRoles.COMPANY_ADMIN],
  });

  const hasPowerUser = useHasPermissions({
    userRoles: [UserRoles.POWER_USER],
  });

  const isApprover =
    hasUserRole(UserRoles.CONFIG_MGMT_APPROVER) ||
    hasCompanyAdmin ||
    hasPowerUser;
  useEffect(() => {
    const error = errorFetchingInstanceDetails as {
      status?: number;
      data?: string;
    };
    if (errorFetchingInstanceDetails && error.status === HTTP_STATUS.NotFound) {
      enqueueSnackbar(error.data, { variant: 'error' });
      navigate(CONFIG_LIST_PATH);
    } else if (isErrorInstanceDetails) {
      enqueueSnackbar(DRAFT, { variant: 'error' });
      navigate(CONFIG_LIST_PATH);
    }
  }, [
    enqueueSnackbar,
    isErrorInstanceDetails,
    errorFetchingInstanceDetails,
    navigate,
  ]);

  useEffect(() => {
    if (isErrorRevisions) {
      enqueueSnackbar(REVISION, { variant: 'error' });
    }
  }, [enqueueSnackbar, isErrorRevisions]);

  const hasSchedulePermission = useHasPermissions({
    userRoles: [UserRoles.CONFIG_MGMT_DEPLOY],
    companyFeatureFlags: [FeatureFlags.SCHEDULING_PCI],
  });

  const hasMfaAccess = hasFeatureFlag(FeatureFlags.CONFIG_MGMT_DEPLOY_WITH_MFA);

  const revisions: RevisionItem[] = useMemo(() => {
    const responseRevisions = dataRevisions?.results;

    if (isEmpty(responseRevisions)) {
      return [];
    }

    return responseRevisions
      .sort(
        (a, b) =>
          new Date(b.created.date).getTime() -
          new Date(a.created.date).getTime()
      )
      .map(
        (
          { currentRevisionDisplayName, hash, created, content, $id },
          index
        ) => ({
          id: $id,
          hash,
          content,
          isDraft: false,
          isSchedule: false,
          createdDate: toRevisionDate(
            created?.date || spacetime.now().format('iso')
          ),
          title:
            currentRevisionDisplayName ||
            `Revision ${responseRevisions.length - index}`,
        })
      );
  }, [dataRevisions]);

  const scheduleRevision = useMemo(() => {
    const defaultScheduleRevision = {
      id: scheduleRevisionItemId,
      title: 'Schedule',
      content: '',
      isDraft: false,
      isSchedule: true,
      createdDate: toRevisionDate(spacetime.now().format('iso')),
    };

    const responseRevisions = dataRevisions?.results?.sort(
      (a, b) =>
        new Date(b.created.date).getTime() - new Date(a.created.date).getTime()
    );

    if (!responseRevisions) {
      return defaultScheduleRevision;
    }

    const { content } = responseRevisions?.[0] ?? { content: '' };

    return {
      ...defaultScheduleRevision,
      content,
    };
  }, [dataRevisions, toRevisionDate]);

  // Checks if the file needs approval --- TO-DO: logic might change after API integration
  // Assuming instanceDetails is defined and has a configFile property of type string
  const isValidFile: boolean = hasApprovalRequirement(
    dataInstanceDetails?.configFile
  );

  const draftRevision = useMemo(() => {
    const defaultDraftRevision = {
      id: draftRevisionItemId,
      title: 'Draft',
      content: '',
      isDraft: true,
      isSchedule: false,
      createdDate: toRevisionDate(spacetime.now().format('iso')),
    };

    if (!dataInstanceDetails) {
      return defaultDraftRevision;
    }

    const { draftContent, draftEdited } = dataInstanceDetails;

    let contentToShow = '';
    if (
      isValidFile &&
      entityPendingObject?.status === 'pending' &&
      entityPendingObject?.proposedValue
    ) {
      contentToShow = entityPendingObject.proposedValue;
    } else if (draftContent) {
      contentToShow = draftContent;
    }

    return {
      ...defaultDraftRevision,
      content: contentToShow,
      createdDate: draftEdited?.date
        ? `Edited ${toRevisionDate(draftEdited.date)}`
        : toRevisionDate(spacetime.now().format('iso')),
    };
  }, [
    dataInstanceDetails,
    isValidFile,
    isApprovalEnabled,
    entityPendingObject,
  ]);

  const revisionsWithDraft = useMemo(
    () => [
      draftRevision,
      hasSchedulePermission && scheduleRevision,
      ...revisions,
    ],
    [draftRevision, revisions, scheduleRevision]
  );

  const revisionsByIdsObject = useMemo(
    () => keyBy(revisionsWithDraft, 'id'),
    [revisionsWithDraft]
  );

  const editorToDisplay = useMemo(
    () => schemaParser(dataInstanceDetails?.schemaContent ?? ''),
    [dataInstanceDetails]
  );

  const [selectedRevisionId, setSelectedRevisionId] =
    useState(draftRevisionItemId);

  const selectedRevision = revisionsByIdsObject[selectedRevisionId]; // need to add the logic here
  const isDraftSelected = selectedRevision?.isDraft || false;
  const isScheduleSelected = selectedRevision?.isSchedule || false;

  useEffect(() => {
    if (dataEntityPending) {
      // add the draft logic here
    }
    if (!selectedRevision) {
      const firstAvailableRevision = revisionsWithDraft.find(
        revision => revision.id !== undefined
      );
      if (firstAvailableRevision) {
        setSelectedRevisionId(firstAvailableRevision.id);
      }
    }
  }, [selectedRevision, revisionsWithDraft, dataEntityPending]);

  const initialSchemaEditorView =
    (editorToDisplay.isTableView && 'table') ||
    (editorToDisplay.isFormView && 'form') ||
    'raw';

  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [open, setOpen] = useState(false);

  const handleRejectClick = () => {
    setRejectDialogOpen(true);
  };

  const handleApproveClick = ({ deploymentType }) => {
    if (!selectedRevision?.id) {
      alert('No revision selected to approve');
      return;
    }
    const approvedPayload: ApprovalRejectPayload[] = [
      {
        id: entityPendingObject.id,
        status: 'approved',
        comment: '',
        additionalData: {
          approver: {
            deploymentType,
          },
        },
      },
    ];
    setIsRevisionLoading(true);
    putApprovalRejectMutate(approvedPayload, {
      onSuccess: () => {
        enqueueSnackbar('Configuration request(s) have been approved', {
          variant: 'success',
        });
        setTimeout(() => {
          refetchInstanceDetails();
          refetchRevisions();
          refetchEntityPendingData();
          setIsRevisionLoading(false);
        }, 3000);
      },
      onError: (error: any) => {
        enqueueSnackbar(
          `Approval failed: ${error.message || 'Unknown error'}`,
          { variant: 'error' }
        );
        setIsRevisionLoading(false);
      },
    });
  };

  const handleRejectConfirm = () => {
    if (!rejectionReason.trim()) {
      setRejectionError(true);
      return;
    }
    setRejectionError(false);

    if (!selectedRevision?.id) {
      alert('No revision selected to reject');
      return;
    }

    const rejectedPayload: ApprovalRejectPayload[] = [
      {
        id: entityPendingObject.id,
        status: 'rejected',
        comment: rejectionReason.trim(),
        additionalData: {},
      },
    ];

    putApprovalRejectMutate(rejectedPayload, {
      onSuccess: () => {
        enqueueSnackbar(
          'Configuration request has been rejection. Submitter will be notified of its status.',
          { variant: 'success' }
        );
        setRejectDialogOpen(false);
        setRejectionReason('');
        refetchEntityPendingData();
        refetchInstanceDetails();
      },
      onError: (error: any) => {
        enqueueSnackbar(
          `Rejection failed: ${error.message || 'Unknown error'}`,
          { variant: 'error' }
        );
      },
    });
  };

  const handleRejectCancel = () => {
    setRejectDialogOpen(false);
    setRejectionReason('');
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <>
      {hasSchedulePermission && (
        <Box>
          <ScheduleDeploymentAlert
            entityId={id}
            sourceType='PublishConfigInstance'
          />
        </Box>
      )}
      <Box bgcolor='common.white' display='flex' height={1} minHeight={0}>
        <Box
          width='280px'
          bgcolor='#F0F0F0'
          display='flex'
          flexDirection='column'
          flexShrink={0}
          sx={{
            borderTopRightRadius: '20px',
          }}
        >
          <Stack
            justifyContent='space-between'
            alignItems='center'
            direction='row'
            flexGrow={0}
            flexShrink={0}
            spacing={2}
            sx={{
              paddingX: 2,
              paddingY: 1.5,
            }}
          >
            <Autocomplete
              id='revision-autocomplete'
              options={revisionsWithDraft.filter(item => item.id !== undefined)}
              getOptionLabel={option =>
                `${option.title} - ${option.createdDate}`
              }
              renderInput={params => (
                <TextField {...params} label='Revision' variant='outlined' />
              )}
              renderOption={(props, option) => {
                const index = revisionsWithDraft.findIndex(
                  item => item.id === option.id
                );
                const firstNonDraftNonScheduleIndex =
                  revisionsWithDraft.findIndex(
                    item => !item.isDraft && !item.isSchedule
                  );
                const isFirstValidRevision =
                  index === firstNonDraftNonScheduleIndex;

                return (
                  <MenuItem
                    {...props}
                    key={option.id}
                    selected={selectedRevisionId === option.id}
                    disabled={isLoadingRevisions}
                    onClick={() => {
                      setSelectedRevisionId(option.id);
                    }}
                    divider
                    sx={{
                      backgroundColor: isFirstValidRevision ? '#3f51b5' : '',
                      color: isFirstValidRevision ? 'white' : '',
                      ...(isFirstValidRevision && {
                        '&:hover': {
                          color: 'black',
                        },
                        '&.Mui-selected': {
                          color: 'black',
                        },
                      }),
                    }}
                  >
                    {option.title} - {option.createdDate}
                  </MenuItem>
                );
              }}
              value={revisionsWithDraft.find(
                option => option.id === selectedRevisionId
              )}
              onChange={(event, newValue) => {
                if (newValue) {
                  setSelectedRevisionId(newValue.id);
                }
              }}
              sx={{
                display: 'flex',
                flexDirection: 'column',
                minHeight: 0,
                flexGrow: 1,
                flexShrink: 1,
              }}
              disableClearable
            />
          </Stack>
          {/* {isDraftSelected && ( */}
          <PropertyNavigation
            schema={editorToDisplay.schema}
            propertyNavigationRefs={propertyNavigationRefs}
          />
          {/* )} */}
        </Box>

        <Box display='flex' flexGrow={1} flexDirection='column'>
          {(isDraftSelected || isScheduleSelected) && (
            <Suspense
              fallback={
                <Box height='100%' p={2} bgcolor='common.backgroundLight'>
                  <Box bgcolor='common.white' p='24px' borderRadius='16px'>
                    <Box width='100%' maxWidth='800px' gap={3} display='grid'>
                      <Skeleton width='100%' height='32px' />
                      <Skeleton width='100%' height='32px' />
                      <Skeleton width='40%' height='32px' />
                    </Box>
                  </Box>
                </Box>
              }
            >
              {!isApprover &&
                isValidFile &&
                (isLoadingEntity ? (
                  <Box
                    width='100%'
                    height={64}
                    display='flex'
                    justifyContent='center'
                    alignItems='center'
                  >
                    <CircularProgress />
                  </Box>
                ) : (
                  isApprovalEnabled && (
                    <Alert
                      icon={<InfoIcon color='primary' fontSize='small' />}
                      severity='info'
                      sx={{
                        flexShrink: 0,
                        backgroundColor: '#91D3EA',
                        color: '#12121A',
                        borderRadius: 3,
                      }}
                    >
                      {approvalMessage}
                    </Alert>
                  )
                ))}

              {isApprover && isValidFile && isLoadingEntity ? (
                <Box
                  width='100%'
                  height={64}
                  display='flex'
                  justifyContent='center'
                  alignItems='center'
                >
                  <CircularProgress />
                </Box>
              ) : (
                isApprover &&
                isValidFile &&
                entityPendingObject && (
                  <Alert
                    icon={<InfoIcon color='primary' fontSize='small' />}
                    severity='info'
                    sx={{
                      flexShrink: 0,
                      backgroundColor: '#91D3EA',
                      color: '#12121A',
                      borderRadius: 3,
                    }}
                    action={
                      <>
                        <Button
                          variant='text'
                          sx={{
                            my: 0,
                            fontSize: '11px',
                            mr: 1,
                            border: '1px solid #00394D',
                            borderRadius: 10,
                          }}
                          endIcon={<CancelIcon fontSize='small' />}
                          onClick={handleRejectClick}
                          disabled={isRevisionLoading || isLoadingRevisions}
                        >
                          Reject
                        </Button>

                        <RevisionDeploymentTypeDialog
                          closeDialog={() => setIsDialogOpen(false)}
                          content={selectedRevision.content}
                          customAttributes={[]}
                          initialStep={
                            RevisionDeploymentTypeDialogStep.confirmation
                          }
                          assignmentEmpty={false}
                          isDialogOpen={isDialogOpen}
                          isDisabled={isRevisionLoading || isLoadingRevisions}
                          isLoading={isRevisionLoading || isLoadingRevisions}
                          onSubmit={handleApproveClick}
                          openDialog={() => {
                            setIsDialogOpen(true);
                          }}
                          instanceId=''
                          instanceDetails={dataInstanceDetails}
                          isMfaAccess={false}
                          hasDataChanged={false}
                          isSaveClicked={false}
                          isCloseClicked={false}
                          setIsCloseClicked={() => {}}
                          buttonText='Approve'
                        />
                      </>
                    }
                  >
                    Configuration instance approval request.
                  </Alert>
                )
              )}

              <RejectionDialog
                rejectDialogOpen={rejectDialogOpen}
                rejectionReason={rejectionReason}
                setRejectionReason={setRejectionReason}
                handleRejectCancel={handleRejectCancel}
                handleRejectConfirm={handleRejectConfirm}
                setRejectionError={setRejectionError}
                rejectionError={rejectionError}
              />
              <Snackbar
                open={open}
                autoHideDuration={6000}
                onClose={handleClose}
              >
                <Alert
                  onClose={handleClose}
                  severity='success'
                  variant='filled'
                  sx={{ width: '100%' }}
                >
                  Configuration request(s) have been approved
                </Alert>
              </Snackbar>

              <RevisionEditor
                isScheduleTab={hasSchedulePermission && isScheduleSelected}
                content={selectedRevision.content}
                instanceId={id}
                isLoading={isLoadingInstanceDetails}
                schema={editorToDisplay.schema}
                title={selectedRevision.title}
                instanceDetails={dataInstanceDetails}
                propertiesUsingAttributes={
                  dataInstanceDetails?.propertiesUsingAttributes
                    ?.map(attribute => {
                      const { name, attributeValue } = attribute;
                      return {
                        name: name.split('.').pop(),
                        attributeValue,
                        path: name,
                      };
                    })
                    .sort((a, b) => a.path.localeCompare(b.path)) ?? []
                }
                initialView={initialSchemaEditorView}
                setPropertyNavigationRefs={setPropertyNavigationRefs}
                isMfaAccess={hasMfaAccess}
                entityPendingObject={entityPendingObject}
                refetchRevisions={refetchRevisions}
                refetchEntityPendingData={refetchEntityPendingData}
                isApprovalEnabled={isApprovalEnabled}
              />
            </Suspense>
          )}
          {!isDraftSelected && !isScheduleSelected && (
            <RevisionViewer
              instanceId={id}
              revisions={revisions.filter(
                revision => revision.id !== selectedRevision?.id
              )}
              selectedRevisionId={selectedRevision?.id}
              title={selectedRevision?.title}
              datarevisions={revisions}
              propertiesUsingAttributes={
                dataInstanceDetails?.propertiesUsingAttributes
                  ?.map(attribute => {
                    const { name, attributeValue } = attribute;
                    return {
                      name: name.split('.').pop(),
                      attributeValue,
                      path: name,
                    };
                  })
                  .sort((a, b) => a.path.localeCompare(b.path)) ?? []
              }
              isLoading={isLoadingInstanceDetails}
              instanceDetails={dataInstanceDetails}
              initialView={initialSchemaEditorView}
              schema={editorToDisplay.schema}
              draftContentNew={draftRevision?.content}
              isMfaAccess={hasMfaAccess}
            />
          )}
        </Box>
      </Box>
    </>
  );
};

export default Revision;
