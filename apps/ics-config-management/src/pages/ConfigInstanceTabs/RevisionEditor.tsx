/* eslint-disable no-promise-executor-return */
import React, {
  useMemo,
  createRef,
  useCallback,
  useState,
  MouseEvent,
  KeyboardEvent,
  ChangeEvent,
  useEffect,
  Suspense,
} from 'react';
import { useQueryClient } from '@tanstack/react-query';
import {
  Button,
  Box,
  InputAdornment,
  Stack,
  Typography,
  Menu,
  MenuItem,
  TextField,
  Tooltip,
  Dialog,
  DialogContent,
  DialogActions,
  Divider,
  Snackbar,
  Alert,
} from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { LoadingButton } from '@mui/lab';
import {
  ArrowDropDown,
  CheckBoxRounded,
  CheckBoxOutlineBlankRounded,
  Search,
} from '@mui/icons-material';
import type { ColumnState } from 'ag-grid-community';
import PopupState, { bindTrigger, bindMenu } from 'material-ui-popup-state';
import type { RJSFSchema } from '@rjsf/utils';
import validator from '@rjsf/validator-ajv8';
import { useSnackbar } from 'notistack';
import isEmpty from 'lodash/isEmpty';
import isEqual from 'lodash/isEqual';
import { useNavigate } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';
import Ajv from 'ajv';
import { CodeSlash, FileEarmarkText, List } from 'react-bootstrap-icons';
import { GridSchemaViewerRef } from '../../components/DynamicEditor/GridSchemaViewer';
import {
  getColumnDefs,
  safeJsonParse,
  getCustomAttributesFromFormData,
} from '../../components/DynamicEditor/utils';
import {
  generateCustomAttributesAndMutateSchema,
  ATTRIBUTE_REGEX,
} from '../../components/DynamicEditor/FormSchemaViewer/utils';
import {
  useGetAssignments,
  usePutUpdateInstance,
  usePostPublishRevisions,
  usePostScheduledRevisions,
} from '../../services/use-query';
import useGlobal from '../../hooks/useContext';
import useHasPermissions from '../../hooks/useHasPermissions';
import { UnknownObject } from '../../components/DynamicEditor/types';
import {
  CustomAttributes,
  InstanceDetailsResponse,
  RevisionPublishDeploymentType,
  EntityPending,
} from '../../constants/types';
import { FormSchemaViewerRef } from '../../components/DynamicEditor/FormSchemaViewer/FormEditor';
import UserRoles from '../../constants/userRoles';
import RevisionDeploymentTypeDialog from '../../components/RevisionDeploymentTypeModal';
import { RevisionDeploymentTypeDialogStep } from '../../components/RevisionDeploymentTypeModal/types';
import FeatureFlags from '../../constants/featureFlags';
import LoadingFallback from '../../components/DynamicEditor/LoadingFallback';
import FallbackRender from '../../components/DynamicEditor/FallbackRender';
import type { JsonEditorRef } from '../../components/JsonEditor';
import ScheduleDeploymentModal from '../../components/ScheduleDeploymentModal';
import {
  ScheduleData,
  ScheduleRevisionPayload,
} from '../../components/ScheduleDeploymentModal/types';
import lazyWithPreload from '../../utils/lazyWithPreload';
import { RevisionDeploymentTypeDialogContent } from '../../components/RevisionDeploymentTypeModal/constants';
import { hasApprovalRequirement, hasUserRole } from '../../utils/helpers';

const GridSchemaViewer = lazyWithPreload(
  () => import('../../components/DynamicEditor/GridSchemaViewer')
);
const FormEditor = lazyWithPreload(
  () => import('../../components/DynamicEditor/FormSchemaViewer/FormEditor')
);
const Editor = lazyWithPreload(() => import('../../components/Editor'));
const JsonEditor = lazyWithPreload(() => import('../../components/JsonEditor'));

GridSchemaViewer.preload();
FormEditor.preload();
Editor.preload();
JsonEditor.preload();

type RevisionEditorProps = {
  content: string;
  isLoading: boolean;
  schema: RJSFSchema;
  title: string;
  instanceId: string;
  propertiesUsingAttributes?: CustomAttributes;
  initialView: 'table' | 'form' | 'raw';
  isScheduleTab: boolean;
  setPropertyNavigationRefs: (obj: Object) => void;
  instanceDetails: InstanceDetailsResponse;
  isMfaAccess: boolean;
  entityPendingObject: EntityPending;
  refetchRevisions?: () => Promise<any>;
  isApprovalEnabled?: boolean;
  refetchEntityPendingData?: () => Promise<any>;
};

type EditorView = {
  isGridViewer: boolean;
  isRawViewer: boolean;
  isFormViewer: boolean;
};

type ValidateRawEditorProps = {
  data?:
    | UnknownObject
    | UnknownObject[]
    | { [key: string]: UnknownObject | UnknownObject[] };
  error?: string | null;
  schema: RJSFSchema;
};

const ajv = new Ajv({
  coerceTypes: 'array',
  removeAdditional: true,
  validateFormats: false,
});

const validateWithAJV = ({ data, error, schema }: ValidateRawEditorProps) => {
  let result = null;
  let isResultValid = true;
  const errors = [];
  if (error) {
    errors.push(error);
    isResultValid = false;
  }
  /**
   * If we have data and schema, return validation
   * result from parsed JSON from data
   * Otherwise return true as we don't have schema
   */
  if (data) {
    result = data;
    const validate = ajv.compile(schema);
    isResultValid = validate(data);
    if (!isResultValid) {
      validate.errors?.forEach(({ instancePath, message }) => {
        errors.push(`${instancePath} ${message}`);
      });
    }
  }
  return {
    errors,
    isResultValid,
    result,
  };
};

const RevisionEditor = ({
  isScheduleTab,
  content,
  instanceId,
  isLoading,
  schema,
  title,
  propertiesUsingAttributes,
  initialView,
  setPropertyNavigationRefs,
  instanceDetails,
  isMfaAccess,
  entityPendingObject,
  refetchRevisions,
  isApprovalEnabled,
  refetchEntityPendingData,
}: RevisionEditorProps) => {
  const queryClient = useQueryClient();
  const hasPublishAccess = useHasPermissions({
    userRoles: [UserRoles.CONFIG_MGMT_PUBLISH],
  });
  const hasImmediateDeploymentAccess = useHasPermissions({
    userRoles: [UserRoles.CONFIG_MGMT_DEPLOY],
    companyFeatureFlags: [FeatureFlags.CONFIG_MGMT],
  });

  // Checks if the file needs approval --- TO-DO: logic might change after API integration

  // Assuming instanceDetails is defined and has a configFile property of type string
  const isValidFile: boolean = hasApprovalRequirement(
    instanceDetails?.configFile
  );

  const hasCompanyAdmin = useHasPermissions({
    userRoles: [UserRoles.COMPANY_ADMIN], // Adjust roles accordingly
  });
  const hasPowerUser = useHasPermissions({
    userRoles: [UserRoles.POWER_USER], // Adjust roles accordingly
  });
  const isApprover =
    hasUserRole(UserRoles.CONFIG_MGMT_APPROVER) ||
    hasCompanyAdmin ||
    hasPowerUser;
  const { data: allAssignments, isSuccess: gotAssignments } =
    useGetAssignments(instanceId);
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const { updateDeployable } = useGlobal();
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [isSuccessSnackBaropen, setIsSuccessSnackBaropenOpen] =
    useState<boolean>(false);
  const [isSaveClicked, setIsSaveClicked] = useState<boolean>(false);
  const [mfaErrorTrigger, setMfaErrorTrigger] = useState<number>(0);
  const [currentData, setCurrentData] = useState<
    UnknownObject | UnknownObject[] | null
  >(null);
  const [currentSchema, setCurrentSchema] = useState<RJSFSchema | null>(schema);
  const [filterColumns, setFilterColumns] = useState<string>('');
  const [editorView, setEditorView] = useState<EditorView>({
    isGridViewer: false,
    isRawViewer: true,
    isFormViewer: false,
  });
  const [prevEditorView, setPrevEditorView] = useState<EditorView>({
    isGridViewer: false,
    isRawViewer: false,
    isFormViewer: false,
  });
  const [columnState, setColumState] = useState<ColumnState[]>([]);
  const [rawEditorValue, setRawEditorValue] = useState<string>(content);
  const putUpdateInstance = usePutUpdateInstance(instanceId);
  const postPublishRevisions = usePostPublishRevisions();
  const postScheduledRevisions = usePostScheduledRevisions();
  const gridSchemaViewerRef = createRef<GridSchemaViewerRef>();
  const formSchemaViewerRef = createRef<FormSchemaViewerRef>();
  const jsonViewerRef = createRef<JsonEditorRef>();
  const [currentCustomAttributes, setCurrentCustomAttributes] =
    useState<CustomAttributes>(propertiesUsingAttributes);

  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false);
  const [isCloseClicked, setIsCloseClicked] = useState(false);

  const openScheduleDialog = () => {
    setIsScheduleDialogOpen(true);
  };

  const closeScheduleDialog = () => {
    setIsScheduleDialogOpen(false);
  };

  const isAssignmentsEmpty = useMemo(() => {
    if (allAssignments) {
      return Object.entries(allAssignments).every(item => {
        if (item[1].length > 0) {
          return false;
        }
        return true;
      });
    }
    return true;
  }, [allAssignments]);

  /**
   * Memoized check if data from API
   * conforms to schema
   */
  const isValid = useMemo(() => {
    if (!currentData) {
      return true;
    }
    if (currentData && currentSchema) {
      const dataToValidate = Array.isArray(currentData)
        ? { currentData }
        : currentData;
      const validation = validator.validateFormData(
        currentSchema,
        dataToValidate
      );
      return validation.errors.length === 0;
    }
    return false;
  }, [currentData, currentSchema]);

  /**
   * Memoized get column definitions and all rows
   * data for GridSchemaViewer
   */
  const { columnDefs, rowData, primaryKey, formSchema, reason } =
    useMemo(() => {
      if (schema && isValid && initialView === 'table') {
        return getColumnDefs({
          isValid,
          schema,
          data: currentData as { [key: string]: UnknownObject[] },
        });
      }
      return {
        columnDefs: [],
        rowData: [],
        primaryKey: '',
        formSchema: {},
        reason: [],
      };
    }, [isValid, currentData, schema, initialView]);

  const handleParseData = useCallback(
    (options?: {
      formData: UnknownObject;
      updateSchema?: boolean;
      updateCustomAttributes?: boolean;
    }) => {
      const {
        formData,
        updateSchema = true,
        updateCustomAttributes = true,
      } = options;
      const extractedCustomAttributes = getCustomAttributesFromFormData({
        formData,
      });
      const { resultSchema, finalCustomAttributes } =
        generateCustomAttributesAndMutateSchema({
          customAttributesObject: extractedCustomAttributes,
          schemaObject: schema,
        });
      if (updateSchema) {
        setCurrentSchema(resultSchema);
      }
      if (updateCustomAttributes) {
        setCurrentCustomAttributes(finalCustomAttributes);
      }
      return formData;
    },
    [
      currentSchema,
      setCurrentSchema,
      currentCustomAttributes,
      setCurrentCustomAttributes,
    ]
  );

  /**
   * Reverts all changes
   * made to the data by the user
   */
  const revertAllChanges = useCallback(() => {
    setRawEditorValue(content);
    const parsedContent = safeJsonParse(content);
    if (parsedContent?.data) {
      handleParseData({
        formData: parsedContent.data as UnknownObject,
        updateSchema: true,
        updateCustomAttributes: true,
      });

      setCurrentData(parsedContent.data);
    }
  }, [content, setCurrentData, setRawEditorValue, handleParseData]);

  /**
   * Gets current data from GridSchemaViewer
   */
  const handleGetCurrentData = useCallback(
    (options?: {
      updateRawEditor?: boolean;
      updateSchema?: boolean;
      updateCustomAttributes?: boolean;
    }) => {
      const {
        updateRawEditor = true,
        updateSchema = true,
        updateCustomAttributes = true,
      } = options ?? {};
      let errors = [];
      let result = currentData;
      let isResultValid = true;
      if (
        editorView.isRawViewer &&
        (initialView === 'table' || initialView === 'form')
      ) {
        const isJsonValid = jsonViewerRef?.current?.validate();
        if (!isJsonValid?.isValid) {
          return {
            errors: isJsonValid?.errors,
            isResultValid: false,
            result,
          };
        }
        const defaultEditorValue = initialView === 'table' ? '[]' : '{}';
        const parsedContent = safeJsonParse(
          rawEditorValue ?? defaultEditorValue
        );
        if (parsedContent?.error) {
          errors.push(parsedContent.error);
          isResultValid = false;
        }
        if (parsedContent?.data) {
          result = handleParseData({
            formData: parsedContent.data as UnknownObject,
          });
          if (currentSchema) {
            const { isResultValid: isSchemaValid, errors: ajvErrors } =
              validateWithAJV({
                ...parsedContent,
                schema: currentSchema,
              });
            isResultValid = isSchemaValid;
            if (!isResultValid) {
              errors = [...errors, ...ajvErrors];
            }
          }
        }
        return {
          errors,
          isResultValid,
          result,
        };
      }
      if (gridSchemaViewerRef?.current && editorView.isGridViewer) {
        const gridCurrentData = gridSchemaViewerRef.current.getCurrentData();
        result = primaryKey
          ? { [primaryKey]: gridCurrentData }
          : gridCurrentData;
      }
      if (formSchemaViewerRef?.current && editorView.isFormViewer) {
        const formCurrentData = formSchemaViewerRef.current.getCurrentData();
        result = formCurrentData.userFormData;
        handleParseData({
          formData: formCurrentData.userFormData,
          updateSchema,
          updateCustomAttributes,
        });
      }
      if (updateRawEditor) {
        const newRawEditorValue = JSON.stringify(result, null, 2);
        if (newRawEditorValue !== rawEditorValue) {
          setRawEditorValue(newRawEditorValue);
        }
      }
      return {
        errors,
        isResultValid,
        result,
      };
    },
    [
      currentData,
      currentSchema,
      editorView,
      formSchemaViewerRef,
      gridSchemaViewerRef,
      handleParseData,
      initialView,
      jsonViewerRef,
      primaryKey,
      rawEditorValue,
    ]
  );

  const handleValidationForSubmit = useCallback(() => {
    let payload = {
      draftContent: rawEditorValue,
      isResultValid: false,
    };
    if (isEmpty(rawEditorValue)) {
      return payload;
    }
    if (!schema) {
      payload = {
        draftContent: rawEditorValue,
        isResultValid: true,
      };
      return payload;
    }
    if (editorView.isFormViewer) {
      const isFormValid = formSchemaViewerRef?.current?.validateForm();
      if (!isFormValid) {
        enqueueSnackbar('Form is not valid', {
          autoHideDuration: 5_000,
          variant: 'error',
        });
        return payload;
      }
    }
    if (currentSchema) {
      const parsedContent = safeJsonParse(rawEditorValue);
      const { isResultValid, result, errors } = validateWithAJV({
        ...parsedContent,
        schema: currentSchema,
      });
      if (errors.length) {
        errors.forEach(error => {
          enqueueSnackbar(error, {
            autoHideDuration: 5_000,
            variant: 'error',
          });
        });
      }
      if (isResultValid) {
        payload = {
          draftContent: JSON.stringify(result, null, 2),
          isResultValid,
        };
      }
    }
    return payload;
  }, [
    currentSchema,
    editorView,
    formSchemaViewerRef?.current,
    rawEditorValue,
    enqueueSnackbar,
  ]);

  /**
   * Saves changes made to the data
   * by the user to API
   */
  const handleSave = useCallback(async () => {
    let success = null;
    setIsSaveClicked(true);
    const { isResultValid, draftContent } = handleValidationForSubmit();
    if (!isResultValid) {
      return success;
    }

    /**
     * Paths for arrays in DotNet are using square brackets
     * while paths in react-jsonschema-form uses dots i.e.
     *  foo.bar[0].baz in API is
     *  foo.bar.0.baz in UI
     */
    const payload = {
      draftContent,
      propertiesUsingAttributes: currentCustomAttributes?.map(
        ({ path, attributeValue }) => ({
          name: path.replace(/\.(\d{1,})\./g, '[$1].'),
          attributeValue: attributeValue.replace(ATTRIBUTE_REGEX, ''),
        })
      ),
    };

    await new Promise((resolve, reject) =>
      putUpdateInstance.mutate(
        {
          instanceId,
          payload,
        },
        {
          onError(error: string) {
            enqueueSnackbar(error.toString(), { variant: 'error' });
            reject(error);
          },
          onSuccess() {
            enqueueSnackbar('Changes saved', { variant: 'success' });
            success = {
              content: payload.draftContent,
              propertiesUsingAttributes: payload.propertiesUsingAttributes,
            };
            resolve(true);
          },
        }
      )
    );
    if (success) setRawEditorValue(success.content);
    return success;
  }, [
    currentSchema,
    editorView,
    enqueueSnackbar,
    formSchemaViewerRef?.current,
    instanceId,
    putUpdateInstance,
    rawEditorValue,
    handleValidationForSubmit,
  ]);

  const handleSubmitForApproval = useCallback(
    async ({ deploymentType }) => {
      const { draftContent } = handleValidationForSubmit();
      const payload = {
        draftContent,
        propertiesUsingAttributes: currentCustomAttributes?.map(
          ({ path, attributeValue }) => ({
            name: path.replace(/\.(\d{1,})\./g, '[$1].'),
            attributeValue: attributeValue.replace(ATTRIBUTE_REGEX, ''),
          })
        ),
        deploymentType,
      };

      await new Promise((resolve, reject) =>
        putUpdateInstance.mutate(
          {
            instanceId,
            payload,
          },
          {
            onError(error: string) {
              const message =
                'An error occurred while submitting your request.';
              enqueueSnackbar(message, { variant: 'error' });
              reject(error);
              refetchEntityPendingData();
            },
            onSuccess() {
              setIsSuccessSnackBaropenOpen(true);
              resolve(true);
              refetchRevisions();
              refetchEntityPendingData();
            },
          }
        )
      );
    },
    [
      currentCustomAttributes,
      enqueueSnackbar,
      instanceId,
      putUpdateInstance,
      setIsSuccessSnackBaropenOpen,
      handleValidationForSubmit,
    ]
  );

  const getDeploymentMessage = useCallback((deploymentType: string) => {
    switch (deploymentType) {
      case 'immediate':
        return RevisionDeploymentTypeDialogContent.immediate;
      case 'maintenance-window':
        return RevisionDeploymentTypeDialogContent.maintenancewindow;
      default:
        return '';
    }
  }, []);

  /**
   * Publishes changes made to the data
   * by the user to API
   */
  const handlePublish = useCallback(
    result => {
      const payload = {
        ...result,
        propertiesUsingAttributes: result.propertiesUsingAttributes?.map(
          ({ path, attributeValue }) => ({
            name: path.replace(/\.(\d{1,})\./g, '[$1].'),
            attributeValue: attributeValue.replace(ATTRIBUTE_REGEX, ''),
          })
        ),
        isRevert: false,
        title: null,
        isAssignment: !isAssignmentsEmpty,
      };
      const message = getDeploymentMessage(payload.deploymentType);
      postPublishRevisions.mutate(
        {
          instanceId,
          payload,
        },
        {
          onError(error: any) {
            console.log('error', error);
           
            if (error?.response?.status === 400) {
              // Check if it's an MFA validation error
              const errorMessage = error?.response?.data?.validationFailureMessage || error?.response?.data?.message || '';
              console.log('400 Error details:', { errorMessage, fullError: error?.response?.data });

              if (errorMessage.toLowerCase().includes('mfa') ||
                  errorMessage.toLowerCase().includes('authentication') ||
                  errorMessage.toLowerCase().includes('invalid code') ||
                  errorMessage.toLowerCase().includes('two-factor') ||
                  errorMessage.toLowerCase().includes('2fa')) {
                console.log('Detected MFA error, triggering MFA error handler');
                // Trigger MFA error handling in the RevisionDeploymentTypeDialog
                setMfaErrorTrigger(prev => prev + 1);
                return;
              }

              enqueueSnackbar(error?.response?.data?.validationFailureMessage, {
                variant: 'error',
                autoHideDuration: 2000,
              });
              // navigate(`/remote/config-management/`);
            } else {
              enqueueSnackbar(error.toString(), { variant: 'error' });
            }
          },
          onSuccess() {
            if (
              !RevisionDeploymentTypeDialogStep.initialNoAssignments &&
              message
            ) {
              enqueueSnackbar(message, {
                variant: 'success',
              });
            } else {
              enqueueSnackbar('Published successfully', {
                variant: 'success',
              });
            }

            queryClient.invalidateQueries(['getInstanceDetails', instanceId]);
            queryClient.invalidateQueries(['getRevisions', instanceId]);
            queryClient.invalidateQueries(['getInstances']);

            setIsSaveClicked(false);
            setIsCloseClicked(false);

            // Close the RevisionDeploymentTypeDialog (including TwoFactor modal) on successful API call
            setIsDialogOpen(false);
            setMfaErrorTrigger(0);

            if (isAssignmentsEmpty) {
              navigate(
                `/remote/config-management/instance/${instanceId}/assignment`
              );
            } else {
              navigate(`/remote/config-management/instance/${instanceId}`);
            }
          },
        }
      );

      updateDeployable(undefined);
    },
    [instanceId, postPublishRevisions, updateDeployable, isAssignmentsEmpty]
  );

  useEffect(() => {
    if (schema) {
      setCurrentSchema(schema);
    }
  }, [schema]);

  const handleSetColumnState = useCallback((data: ColumnState[]) => {
    setColumState(data);
  }, []);

  const handleFilterColumns = useCallback(
    (inputEvent: ChangeEvent<HTMLInputElement>) => {
      setFilterColumns(inputEvent.target.value);
    },
    []
  );

  const handleRawEditorChange = useCallback(
    (inputEvent: ChangeEvent<HTMLInputElement>) => {
      if (initialView === 'table' || initialView === 'form') {
        const parsedContent = safeJsonParse(inputEvent.target.value);
        if (parsedContent?.data) {
          handleParseData({
            formData: parsedContent.data as UnknownObject,
          });
        }
      }
      setRawEditorValue(inputEvent.target.value);
    },
    [initialView, handleParseData]
  );

  const handleJSONEditorChange = useCallback(
    value => {
      handleParseData({
        formData: value as UnknownObject,
        updateSchema: initialView === 'form',
        updateCustomAttributes: initialView === 'form',
      });
      /** 
          Removes all additional properties from the object
          which are not part of the schema
       */
      const { isResultValid, result } = validateWithAJV({
        data: value,
        schema: currentSchema,
      });
      if (isResultValid) {
        setRawEditorValue(JSON.stringify(result, null, 2));
      }
    },
    [handleParseData, initialView]
  );

  const handleResetErrorBoundary = useCallback(() => {
    setEditorView({
      isGridViewer: false,
      isRawViewer: true,
      isFormViewer: false,
    });
    if (currentData) {
      setRawEditorValue(JSON.stringify(currentData, null, 2));
    }
  }, [currentData, setEditorView, setRawEditorValue]);

  const handleViewChange = useCallback(
    ({ isGridViewer, isRawViewer, isFormViewer }) => {
      const { errors, result, isResultValid } = handleGetCurrentData();
      setCurrentData(result);
      setEditorView({
        isGridViewer,
        isRawViewer,
        isFormViewer,
      });
      if (!isResultValid) {
        errors.forEach(error => {
          enqueueSnackbar(error, {
            autoHideDuration: 5_000,
            variant: 'error',
          });
        });
      }
    },
    [enqueueSnackbar, handleGetCurrentData, setCurrentData, setEditorView]
  );

  const safeParseJSON = value => {
    try {
      return typeof value === 'string' ? JSON.parse(value) : value; // as the type of content and rawEditorValue is string
    } catch (error) {
      return null;
    }
  };

  const normalizeValue = (val: string) =>
    val.replace(/{{\[\s*(.*?)\s*\]}}/, '$1').trim();

  const normalizeAttributes = (attrs: any[]) =>
    attrs.map(attr => ({
      ...attr,
      attributeValue: normalizeValue(attr.attributeValue),
    }));

  const hasDataChanged = useMemo(() => {
    const parsedContent = safeParseJSON(content);
    const parsedRawEditorValue = safeParseJSON(rawEditorValue);

    const normalizedCurrent = normalizeAttributes(currentCustomAttributes);
    const normalizedProps = normalizeAttributes(propertiesUsingAttributes);

    const isAllValuesEmpty =
      parsedRawEditorValue &&
      typeof parsedRawEditorValue === 'object' &&
      Object.values(parsedRawEditorValue).every(
        value => value === null || value === undefined || value === ''
      );
    if (isAllValuesEmpty) {
      return true;
    }
    return (
      isEqual(parsedContent, parsedRawEditorValue) &&
      isEqual(normalizedCurrent, normalizedProps)
    );
  }, [
    content,
    rawEditorValue,
    currentCustomAttributes,
    propertiesUsingAttributes,
  ]);

  useEffect(() => {
    if (isValid) {
      const isGrid =
        columnDefs.length > 0 && initialView === 'table' && reason.length === 0;
      const isForm =
        columnDefs.length === 0 &&
        ((reason.length && initialView === 'table') || initialView === 'form');
      setEditorView({
        isGridViewer: isGrid,
        isRawViewer: !(isGrid || isForm),
        isFormViewer: isForm,
      });
    }
  }, [columnDefs, isValid, initialView, reason]);

  useEffect(() => {
    revertAllChanges();
  }, [content, propertiesUsingAttributes]);

  /**
   * Publishes changes made to the data
   * by the user to API
   */
  const handleSchedule = useCallback(
    (result: ScheduleRevisionPayload) => {
      postScheduledRevisions.mutate(
        {
          instanceId,
          payload: result,
        },
        {
          onError: (error: any) => {
            if (error?.response?.data?.message) {
              enqueueSnackbar(error.response.data.message, {
                variant: 'error',
              });
            } else {
              enqueueSnackbar('Schedule failed', { variant: 'error' });
            }
          },
          onSuccess: (success: any) => {
            if (success?.message) {
              enqueueSnackbar(success.message, { variant: 'success' });
            } else {
              enqueueSnackbar('Scheduled successfully', { variant: 'success' });
            }
            queryClient.invalidateQueries(['getInstanceDetails', instanceId]);
            queryClient.invalidateQueries(['getRevisions', instanceId]);
            queryClient.invalidateQueries(['getInstances']);
            setIsScheduleDialogOpen(false);
          },
        }
      );

      updateDeployable(undefined);
    },
    [instanceId, postPublishRevisions, updateDeployable, isAssignmentsEmpty]
  );

  const scheduleRevisions = useCallback(
    (data: ScheduleData) => {
      const schedulePayload = {
        ...data,
        scheduleContent: rawEditorValue,
        propertiesUsingAttributes: currentCustomAttributes?.map(
          ({ path, attributeValue }) => ({
            name: path.replace(/\.(\d{1,})\./g, '[$1].'),
            attributeValue: attributeValue.replace(ATTRIBUTE_REGEX, ''),
          })
        ),
      };
      handleSchedule(schedulePayload);
    },
    [rawEditorValue, currentCustomAttributes, handleSchedule]
  );

  const handleCancel = () => {
    setDialogOpen(false);
  };

  const handleSubmit = () => {
    setDialogOpen(false);
    setIsDialogOpen(true);
  };

  const handleClose = () => {
    setIsSuccessSnackBaropenOpen(false);
  };

  return (
    <Stack
      direction='column'
      flex={1}
      sx={{
        overflow: 'hidden',
      }}
    >
      <Stack
        justifyContent='space-between'
        alignItems='center'
        direction='row'
        flexGrow={0}
        flexShrink={0}
        spacing={2}
        sx={{
          paddingX: 2,
          paddingY: 1,
          minHeight: 48,
        }}
      >
        <Typography variant='titleMedium'>{title}</Typography>
        <Stack direction='row' spacing={2}>
          {columnState?.length && editorView.isGridViewer ? (
            <PopupState variant='popover' popupId='form-actions-popup'>
              {popupState => (
                <>
                  <Button variant='outlined' {...bindTrigger(popupState)}>
                    Columns {columnState.filter(({ hide }) => !hide).length}/
                    {columnState.length} <ArrowDropDown />
                  </Button>
                  <Menu
                    {...bindMenu(popupState)}
                    MenuListProps={{
                      disablePadding: true,
                      sx: {
                        overflowY: 'scroll',
                        maxHeight: 400,
                      },
                    }}
                  >
                    <MenuItem
                      disableGutters
                      onClickCapture={(clickEvent: MouseEvent) => {
                        /**
                         * Prevents the item to be selected when
                         * the user clicks on it
                         */
                        clickEvent.stopPropagation();
                        clickEvent.preventDefault();
                      }}
                      onKeyDown={(keyboardEvent: KeyboardEvent) => {
                        /**
                         * Prevents the other menu items from being
                         * selected when the user presses any key
                         */
                        keyboardEvent.stopPropagation();
                      }}
                      sx={{
                        left: 0,
                        padding: 0,
                        position: 'sticky',
                        right: 0,
                        top: 0,
                        width: '100%',
                        zIndex: 1,
                      }}
                    >
                      <Box
                        sx={{
                          backgroundColor: '#fff',
                          width: '100%',
                        }}
                      >
                        <TextField
                          sx={{ width: '100%', minWidth: 350, zIndex: 2 }}
                          placeholder='Filter'
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position='start'>
                                <Search />
                              </InputAdornment>
                            ),
                          }}
                          onChange={handleFilterColumns}
                          value={filterColumns}
                          variant='filled'
                        />
                      </Box>
                    </MenuItem>
                    {columnState?.length
                      ? columnState?.map(({ colId, hide }, index) => (
                          <MenuItem
                            key={colId}
                            onClick={() => {
                              const newColumnState = [...columnState];
                              newColumnState[index].hide = !hide;
                              handleSetColumnState(newColumnState);
                              if (gridSchemaViewerRef?.current) {
                                gridSchemaViewerRef.current.setColumState(
                                  columnState
                                );
                              }
                            }}
                            sx={{
                              display:
                                filterColumns.length > 2 &&
                                !colId
                                  .toLocaleLowerCase()
                                  .startsWith(filterColumns.toLocaleLowerCase())
                                  ? 'none'
                                  : 'flex',
                              alignItems: 'center',
                              zIndex: 0,
                            }}
                          >
                            {!hide ? (
                              <CheckBoxRounded
                                color='primary'
                                fontSize='small'
                              />
                            ) : (
                              <CheckBoxOutlineBlankRounded fontSize='small' />
                            )}
                            <Box sx={{ marginLeft: 0.5 }}>{colId}</Box>
                          </MenuItem>
                        ))
                      : ''}
                  </Menu>
                </>
              )}
            </PopupState>
          ) : (
            ''
          )}
          {initialView !== 'raw' && (
            <PopupState variant='popover' popupId='editor-view-popup'>
              {popupState => (
                <>
                  {!columnDefs.length ? (
                    <Tooltip title='Form View'>
                      <Button
                        disabled={editorView.isFormViewer}
                        onClick={() => {
                          popupState.close();
                          revertAllChanges();
                          handleViewChange({
                            isFormViewer: true,
                            isGridViewer: false,
                            isRawViewer: false,
                          });
                        }}
                        variant={
                          editorView.isFormViewer ? 'contained' : 'outlined'
                        }
                      >
                        <FileEarmarkText fontSize='large' />
                      </Button>
                    </Tooltip>
                  ) : (
                    ''
                  )}
                  {columnDefs.length ? (
                    <Tooltip title='Table View'>
                      <Button
                        disabled={editorView.isGridViewer}
                        onClick={() => {
                          popupState.close();
                          revertAllChanges();
                          handleViewChange({
                            isFormViewer: false,
                            isGridViewer: true,
                            isRawViewer: false,
                          });
                        }}
                        variant={
                          editorView.isGridViewer ? 'contained' : 'outlined'
                        }
                      >
                        <List fontSize='large' />
                      </Button>
                    </Tooltip>
                  ) : (
                    ''
                  )}
                  <Tooltip title='Raw view (.json)'>
                    <Button
                      disabled={editorView.isRawViewer}
                      onClick={() => {
                        if (
                          editorView.isFormViewer ||
                          editorView.isGridViewer
                        ) {
                          setPrevEditorView({
                            isGridViewer: editorView.isGridViewer,
                            isRawViewer: false,
                            isFormViewer: editorView.isFormViewer,
                          });
                        }
                        popupState.close();
                        handleGetCurrentData();
                        setEditorView({
                          isFormViewer: false,
                          isGridViewer: false,
                          isRawViewer: true,
                        });
                      }}
                      variant={
                        editorView.isRawViewer ? 'contained' : 'outlined'
                      }
                    >
                      <CodeSlash fontSize='large' />
                    </Button>
                  </Tooltip>
                </>
              )}
            </PopupState>
          )}
        </Stack>
      </Stack>
      <Stack
        flex={1}
        flexGrow={1}
        flexShrink={0}
        alignItems='stretch'
        sx={{
          overflow: 'hidden',
        }}
      >
        <Suspense fallback={<LoadingFallback />}>
          {editorView.isGridViewer && schema && (
            <ErrorBoundary
              fallbackRender={FallbackRender}
              onReset={handleResetErrorBoundary}
            >
              <GridSchemaViewer
                ref={gridSchemaViewerRef}
                columnDefs={columnDefs}
                formSchema={formSchema}
                rowData={rowData}
                isFetching={false}
                isLoading={isLoading}
                onChange={handleGetCurrentData}
                onFirstRender={handleSetColumnState}
                primaryKey={primaryKey}
              />
            </ErrorBoundary>
          )}
        </Suspense>
        <Suspense fallback={<LoadingFallback />}>
          {editorView.isFormViewer && schema && (
            <ErrorBoundary
              fallbackRender={FallbackRender}
              onReset={handleResetErrorBoundary}
            >
              <FormEditor
                ref={formSchemaViewerRef}
                oSchema={schema}
                pSchema={currentSchema}
                pData={currentData as UnknownObject}
                onChange={() =>
                  handleGetCurrentData({
                    updateRawEditor: true,
                    updateSchema: true,
                    updateCustomAttributes: true,
                  })
                }
                propertiesUsingAttributes={propertiesUsingAttributes}
                setPropertyNavigationRefs={setPropertyNavigationRefs}
              />
            </ErrorBoundary>
          )}
        </Suspense>
        <Suspense fallback={<LoadingFallback />}>
          {!schema && (
            <Editor
              key={content}
              isLoading={isLoading}
              onChange={handleRawEditorChange}
              value={rawEditorValue}
            />
          )}
        </Suspense>
        <Suspense fallback={<LoadingFallback />}>
          {editorView.isRawViewer && schema && (
            <ErrorBoundary
              fallbackRender={FallbackRender}
              onReset={handleResetErrorBoundary}
            >
              <JsonEditor
                key={content}
                initialView={initialView}
                onChange={handleJSONEditorChange}
                originalSchema={schema}
                ref={jsonViewerRef}
                schema={currentSchema}
                value={safeJsonParse(rawEditorValue).data as UnknownObject}
              />
            </ErrorBoundary>
          )}
        </Suspense>
      </Stack>
      <Stack
        justifyContent='flex-end'
        alignItems='center'
        direction='row'
        flexGrow={0}
        flexShrink={0}
        spacing={1}
        sx={{
          borderTop: '1px solid #C6C5D0',
          paddingX: 2,
          paddingY: 1.5,
        }}
      >
        {editorView.isRawViewer && (
          <Button
            disabled={hasDataChanged}
            onClick={() => {
              revertAllChanges();
              if (editorView.isRawViewer) {
                if (schema.title === 'SAFConfigs') {
                  setEditorView({
                    isGridViewer: false,
                    isRawViewer: true,
                    isFormViewer: false,
                  });
                } else {
                  setEditorView(
                    prevEditorView.isFormViewer
                      ? {
                          isGridViewer: false,
                          isRawViewer: false,
                          isFormViewer: true,
                        }
                      : {
                          isGridViewer: true,
                          isRawViewer: false,
                          isFormViewer: false,
                        }
                  );
                }
              }
            }}
            variant='outlined'
          >
            Discard
          </Button>
        )}
        {!isScheduleTab &&
          (isApprover || !isApprovalEnabled) &&
          entityPendingObject?.status !== 'pending' && (
            <LoadingButton
              disabled={hasDataChanged}
              loading={
                isLoading ||
                putUpdateInstance.isLoading ||
                postPublishRevisions.isLoading
              }
              onClick={async () => {
                handleGetCurrentData({
                  updateSchema: true,
                  updateCustomAttributes: true,
                  updateRawEditor: true,
                });
                await handleSave();
              }}
              variant='outlined'
            >
              Save
            </LoadingButton>
          )}
        {!isApprover &&
          isApprovalEnabled &&
          isValidFile &&
          entityPendingObject?.status !== 'pending' && (
            <RevisionDeploymentTypeDialog
              closeDialog={() => setIsDialogOpen(false)}
              content={rawEditorValue}
              customAttributes={currentCustomAttributes}
              initialStep={RevisionDeploymentTypeDialogStep.confirmation}
              assignmentEmpty={isAssignmentsEmpty}
              isDialogOpen={isDialogOpen}
              isDisabled={hasDataChanged}
              isLoading={
                isLoading ||
                putUpdateInstance.isLoading ||
                postPublishRevisions.isLoading
              }
              onSubmit={handleSubmitForApproval}
              openDialog={() => {
                handleGetCurrentData({
                  updateSchema: true,
                  updateCustomAttributes: true,
                  updateRawEditor: false,
                });
                setDialogOpen(true);
              }}
              instanceId={instanceId}
              instanceDetails={instanceDetails}
              isMfaAccess={false}
              hasDataChanged={hasDataChanged}
              isSaveClicked={isSaveClicked}
              isCloseClicked={isCloseClicked}
              setIsCloseClicked={setIsCloseClicked}
              buttonText='Submit for Approval'
            />
          )}
        {hasPublishAccess &&
          gotAssignments &&
          !isScheduleTab &&
          (!isApprovalEnabled || isApprover) &&
          entityPendingObject?.status !== 'pending' && (
            <RevisionDeploymentTypeDialog
              closeDialog={() => setIsDialogOpen(false)}
              content={rawEditorValue}
              customAttributes={currentCustomAttributes}
              initialStep={
                isAssignmentsEmpty
                  ? RevisionDeploymentTypeDialogStep.initialNoAssignments
                  : RevisionDeploymentTypeDialogStep.initial
              }
              assignmentEmpty={isAssignmentsEmpty}
              isDialogOpen={isDialogOpen}
              isDisabled={hasDataChanged}
              isLoading={
                isLoading ||
                putUpdateInstance.isLoading ||
                postPublishRevisions.isLoading
              }
              onSubmit={result => {
                handlePublish(result);
              }}
              openDialog={async () => {
                handleGetCurrentData({
                  updateSchema: true,
                  updateCustomAttributes: true,
                  updateRawEditor: true,
                });
                const saved = await handleSave();
                if (saved) {
                  setCurrentCustomAttributes(saved.propertiesUsingAttributes);
                  if (hasImmediateDeploymentAccess) {
                    setIsDialogOpen(true);
                    return;
                  }
                  handlePublish({
                    deploymentType:
                      RevisionPublishDeploymentType.maintenanceWindow,
                    ...saved,
                  });
                }
              }}
              instanceId={instanceId}
              instanceDetails={instanceDetails}
              isMfaAccess={isMfaAccess}
              hasDataChanged={hasDataChanged}
              isSaveClicked={isSaveClicked}
              isCloseClicked={isCloseClicked}
              setIsCloseClicked={setIsCloseClicked}
              mfaErrorTrigger={mfaErrorTrigger}
            />
          )}
        {isScheduleTab && (
          <>
            <LoadingButton
              disabled={isEmpty(rawEditorValue)}
              loading={
                isLoading ||
                putUpdateInstance.isLoading ||
                postPublishRevisions.isLoading
              }
              onClick={openScheduleDialog}
              variant='contained'
            >
              Schedule
            </LoadingButton>
            <ScheduleDeploymentModal
              isOpen={isScheduleDialogOpen}
              onScheduled={scheduleRevisions}
              // sourceType={SourceType.updateAssignments}
              close={closeScheduleDialog}
            />
          </>
        )}

        <Dialog
          open={dialogOpen}
          onClose={handleCancel}
          maxWidth='sm'
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: '20px',
              boxShadow: 3,
              pb: 0,
            },
          }}
        >
          <Box
            sx={{ px: 3, pt: 2, backgroundColor: '#E1E1EC', borderRadius: 2 }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <InfoOutlinedIcon color='primary' sx={{ mr: 1 }} />
              <Typography variant='h6' fontWeight='bold'>
                Submit for Review
              </Typography>
            </Box>

            <Divider />

            <DialogContent sx={{ pt: 2 }}>
              <Typography variant='body1' color='#5D5D67'>
                Attribute changes need to be approved by a user with approval
                permissions. You will be alerted when this request has been
                approved. You can then deploy this instance.
              </Typography>
            </DialogContent>

            <Divider />

            <DialogActions
              sx={{ justifyContent: 'space-between', px: 1.5, pb: 1, pt: 1 }}
            >
              <Button
                onClick={handleCancel}
                sx={{ color: '#3f51b5', textTransform: 'none' }}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                variant='contained'
                sx={{
                  textTransform: 'none',
                  borderRadius: '12px',
                  boxShadow: 3,
                  backgroundColor: '#3f51b5',
                  '&:hover': {
                    backgroundColor: '#303f9f',
                  },
                }}
              >
                Submit for Approval
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
        <Snackbar
          open={isSuccessSnackBaropen}
          autoHideDuration={6000}
          onClose={handleClose}
        >
          <Alert
            onClose={handleClose}
            severity='success'
            variant='filled'
            sx={{ width: '100%' }}
          >
            Configuration request has been submitted. You will be notified of
            its approval status once reviewed.
          </Alert>
        </Snackbar>
      </Stack>
    </Stack>
  );
};

export default RevisionEditor;
