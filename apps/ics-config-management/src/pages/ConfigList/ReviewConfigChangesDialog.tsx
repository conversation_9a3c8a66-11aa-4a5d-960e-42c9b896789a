import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Button,
  Box,
} from '@mui/material';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import { useNavigate } from 'react-router';

const ReviewConfigChangesDialog = ({
  open,
  setIsApproveChangesClicked,
  instanceId,
  onClickAway,
}) => {
  const navigate = useNavigate();

  const handleOnReview = () => {
    navigate(`/remote/config-management/instance/${instanceId}/revisions`);
  };

  const handleOnClose = () => {
    onClickAway();
    setIsApproveChangesClicked(false);
  };

  return (
    <Dialog
      open={open}
      onClose={handleOnClose}
      maxWidth='xs'
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: 'common.modalBackground',
          borderRadius: '20px',
          p: 3,
        },
      }}
    >
      <Box display='flex' alignItems='center' mb={1}>
        <WarningAmberIcon sx={{ color: '#FFA000', mr: 1 }} />
        <DialogTitle sx={{ p: 0, fontWeight: 'bold' }}>
          Approve changes?
        </DialogTitle>
      </Box>

      <DialogContent sx={{ p: 0, pb: 2 }}>
        <Typography>
          Before approving, please review the changes carefully.
        </Typography>
      </DialogContent>

      <DialogActions sx={{ p: 0, pt: 2 }}>
        <Button onClick={handleOnClose} variant='text'>
          Close
        </Button>
        <Button
          onClick={handleOnReview}
          variant='contained'
          sx={{ boxShadow: 2 }}
        >
          Review Changes
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ReviewConfigChangesDialog;
