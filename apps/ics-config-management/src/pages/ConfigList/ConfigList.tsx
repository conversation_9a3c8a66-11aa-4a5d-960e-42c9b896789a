import React, { useCallback, useMemo, memo, Suspense, useState } from 'react';
import Box from '@mui/material/Box';
import Pagination from '@mui/material/Pagination';
import Typography from '@mui/material/Typography';
import ceil from 'lodash/ceil';
import { useSnackbar } from 'notistack';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';

import ExportInstancesDialog from '../../components/ConfigList/ExportInstancesDialog';
import ConfigInstanceActions from '../../components/ConfigList/ConfigInstanceActions';
import InstanceList from '../../components/InstanceList/InstanceList';
import {
  CONFIG_LIST_PATH,
  DEPLOYMENT_LIST_PATH,
  SCHEDULE_LIST_PATH,
} from '../../constants/routes';
import {
  DeviceConfig,
  InstancesResponse,
  LinkTabProps,
} from '../../constants/types';
import { useMergeState } from '../../hooks/useMergeStates';
import MainLayout from '../../layouts/MainLayout';
import { getInstanceCsvUrl } from '../../services/api-request';
import { getInstanceUrl } from '../../utils/helpers';
import {
  useGetEntityPendingData,
  useGetInstances,
  useGetUserConfig,
} from '../../services/use-query';
import removeEmpty from '../../components/ConfigListSearch/removeEmpty';
import { safeJsonParse } from '../../components/DynamicEditor/utils';
import safeBase64decode from '../../components/ConfigListSearch/safeBase64decode';
import {
  InstanceSortOrder,
  InstanceSortOrderBy,
  InstanceView,
} from '../../components/ConfigListSearch/types';
import ScheduleList from '../../components/ScheduleList';
import FeatureFlags from '../../constants/featureFlags';
import UserRoles from '../../constants/userRoles';
import useHasPermissions from '../../hooks/useHasPermissions';
import lazyWithPreload from '../../utils/lazyWithPreload';
import ApplicationPane from '../../components/ApplicationPane';
import DeploymentHistoryList from '../../components/DeploymentHistory';
import { ENTITY_STATUS, ENTITY_TYPE } from '../../constants/staticContent';
import CreateInstanceDialog from './CreateInstanceDialog';

const ConfigListSearch = lazyWithPreload(
  () => import('../../components/ConfigListSearch')
);
const ConfigurationSettingsDialog = lazyWithPreload(
  () => import('./ConfigurationSettingsDialog')
);

ConfigListSearch.preload();
ConfigurationSettingsDialog.preload();

interface States {
  isCreateInstanceDialogOpen?: boolean;
  isExportInstanceDialogOpen?: boolean;
  isSnackBarOpen?: boolean;
  deviceConfigs?: DeviceConfig[];
  isConfigurationSettingsOpen?: boolean;
}

type DialogToOpen = 'create' | 'export';

interface TableTitleProps {
  totalCount: number;
  deviceConfigs: DeviceConfig[];
  view: InstanceView;
  handleViewChange: (
    event: React.MouseEvent<HTMLElement>,
    view: InstanceView
  ) => void;
}

const TableTitle = memo(
  ({ totalCount, deviceConfigs, view, handleViewChange }: TableTitleProps) => (
    <Box
      height='74px'
      display='flex'
      alignItems='center'
      justifyContent='space-between'
      mt={0}
      bgcolor='white'
      padding='16px 24px'
    >
      <Typography variant='titleMedium'>
        All configurations ({totalCount || 0})
      </Typography>
      <Suspense fallback={<div />}>
        <ConfigListSearch
          deviceConfigs={deviceConfigs}
          view={view}
          handleViewChange={handleViewChange}
        />
      </Suspense>
    </Box>
  ),
  (prevProps, nextProps) =>
    prevProps.totalCount === nextProps.totalCount &&
    prevProps.deviceConfigs === nextProps.deviceConfigs &&
    prevProps.view === nextProps.view &&
    prevProps.handleViewChange === nextProps.handleViewChange
);

const ConfigList = () => {
  const { pathname } = useLocation();
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const [
    {
      isCreateInstanceDialogOpen,
      isExportInstanceDialogOpen,
      isConfigurationSettingsOpen,
    },
    setStates,
  ] = useMergeState<States>({
    isCreateInstanceDialogOpen: false,
    isExportInstanceDialogOpen: false,
    isConfigurationSettingsOpen: false,
  });
  const [params, setParams] = useSearchParams({
    appDescriptorId: '',
    configFileId: '',
    createdBy: '',
    editedBy: '',
    approvalStatus: '',
    instanceName: '',
    order: InstanceSortOrder.desc,
    orderBy: InstanceSortOrderBy.lastUpdated,
    pageNumber: '',
  });
  const [view, setView] = useState<InstanceView>(InstanceView.ALL_INSTANCES);

  const appDescriptorId = params.get('appDescriptorId') ?? '';
  const configFileId = params.get('configFileId') ?? '';
  const createdBy = params.get('createdBy') ?? '';
  const editedBy = params.get('editedBy') ?? '';
  const approvalStatus = params.get('approvalStatus') ?? '';
  const instanceName = params.get('instanceName') ?? '';
  const pageNumber = params.get('pageNumber') ?? '';
  const order = params.get('order') ?? InstanceSortOrder.desc;
  const orderBy = params.get('orderBy') ?? InstanceSortOrderBy.lastUpdated;

  const searchObject = useMemo(() => {
    const fullObject = {
      appDescriptorId,
      configFileId,
      createdBy: (
        safeJsonParse(safeBase64decode(createdBy)).data as { id: string }
      )?.id,
      editedBy: (
        safeJsonParse(safeBase64decode(editedBy)).data as { id: string }
      )?.id,
      approvalStatus: (
        safeJsonParse(safeBase64decode(approvalStatus)).data as {
          value: string;
        }
      )?.value,
      instanceName,
      order,
      orderBy,
      pageNumber,
    };
    return removeEmpty(fullObject);
  }, [
    appDescriptorId,
    configFileId,
    createdBy,
    editedBy,
    approvalStatus,
    instanceName,
    order,
    orderBy,
    pageNumber,
  ]);

  const { data: instancesData } = useGetInstances(searchObject, {
    cacheTime: 1_000, // 10 seconds instead of the default 5 minutes
    enabled: !isCreateInstanceDialogOpen && !isExportInstanceDialogOpen,
    onError: () => {
      enqueueSnackbar('Failed on fetching instances', { variant: 'error' });
    },
  });

  const { data: deviceConfigs, isFetching: isUserConfigDataLoading } =
    useGetUserConfig({
      placeholderData: [],
      onError: () => {
        enqueueSnackbar('Failed on fetching user configurations.', {
          variant: 'error',
        });
      },
    });

  const { totalCount = 0, results: instances = [] }: InstancesResponse =
    useMemo(() => {
      const instancesLastUpdated =
        instancesData?.results?.map(instance => {
          const lastUpdated = instance?.stats?.lastUpdated;
          const isValid = new Date(lastUpdated) instanceof Date;
          const lastUpdatedFormat = isValid
            ? new Intl.DateTimeFormat('en-UK', {
                day: 'numeric',
                month: 'short',
                year: '2-digit',
              }).format(new Date(lastUpdated))
            : '';

          return {
            ...instance,
            stats: {
              ...instance.stats,
              lastUpdated: lastUpdatedFormat,
            },
          };
        }) ?? [];

      return {
        totalCount: instancesData?.totalCount ?? 0,
        results: instancesLastUpdated,
      };
    }, [instancesData, pageNumber, instanceName]);

  const openCreateOrExportDialog = useCallback(
    (dialogToOpen: DialogToOpen) => {
      if (dialogToOpen === 'create') {
        setStates({ isCreateInstanceDialogOpen: true });
      }
      if (dialogToOpen === 'export') {
        setStates({ isExportInstanceDialogOpen: true });
      }
    },
    [setStates]
  );

  // need to the configuration settings logic here
  const openConfigurationSettingsDialog = () => {
    setStates({ isConfigurationSettingsOpen: true });
  };

  const handlePaginationChange = useCallback(
    (_, selectedPage) => {
      setParams({
        ...searchObject,
        pageNumber: selectedPage.toString(),
      });
    },
    [params]
  );

  const hasSchedulePermission = useHasPermissions({
    userRoles: [UserRoles.CONFIG_MGMT_DEPLOY],
    companyFeatureFlags: [FeatureFlags.SCHEDULING],
  });

  const hasDeploymentPermission = useHasPermissions({
    userRoles: [UserRoles.CONFIG_MGMT_DEPLOY],
    companyFeatureFlags: [FeatureFlags.CONFIG_MGMT],
  });

  const isCompanyAdmin = useHasPermissions({
    userRoles: [UserRoles.COMPANY_ADMIN],
  });
  const isPowerUser = useHasPermissions({
    userRoles: [UserRoles.POWER_USER],
  });
  const hasConfigurationSettingsPermission = !!(isCompanyAdmin || isPowerUser);

  const linksTab: LinkTabProps[] = [
    { label: 'Instances', to: CONFIG_LIST_PATH, key: 'instances' },
  ];

  if (hasSchedulePermission) {
    linksTab.push({
      label: 'Schedules',
      to: SCHEDULE_LIST_PATH,
      key: 'schedules',
    });
  }

  if (hasDeploymentPermission) {
    linksTab.push({
      label: 'Deployment',
      to: DEPLOYMENT_LIST_PATH,
      key: 'deployment',
    });
  }

  const deploymentTab = useMemo(
    () => pathname.includes('deployment'),
    [pathname]
  );

  const currentTab = useMemo(
    () => (pathname.includes('schedule') ? 1 : 0),
    [pathname]
  );

  const handleViewChange = (
    _: React.MouseEvent<HTMLElement>,
    value: InstanceView
  ) => {
    if (value) {
      setView(value);
      setParams({
        ...(value === InstanceView.APPLICATIONS
          ? {
              order: InstanceSortOrder.asc,
              orderBy: InstanceSortOrderBy.name,
            }
          : {
              order: InstanceSortOrder.desc,
              orderBy: InstanceSortOrderBy.lastUpdated,
            }),
      });
    }
  };

  const {
    data: approvalFlowStatus,
    refetch: refetchApprovalFlowStatus,
    isLoading: isApprovalFlowDataLoading,
  } = useGetEntityPendingData(
    {
      entityType: ENTITY_TYPE,
      status: ENTITY_STATUS,
    },
    {
      cacheTime: 0,
      staleTime: 0,
      refetchOnMount: true,
      refetchOnWindowFocus: true,
    }
  );

  return (
    <MainLayout
      header={{
        title: 'Configuration',
        links: linksTab,
        action: (
          <ConfigInstanceActions
            loading={isUserConfigDataLoading}
            onCreateInstanceClick={() => {
              openCreateOrExportDialog('create');
            }}
            onExportInstanceClick={() => {
              openCreateOrExportDialog('export');
            }}
            openConfigurationSettingsDialog={openConfigurationSettingsDialog}
            hasConfigurationSettingsPermission={
              hasConfigurationSettingsPermission
            }
          />
        ),
      }}
    >
      {(() => {
        if (deploymentTab) {
          return <DeploymentHistoryList />;
        }
        if (currentTab === 0) {
          return (
            <Box
              display='flex'
              flexDirection='column'
              flexGrow={1}
              flexShrink={1}
              minHeight={0}
              height='100%'
              width='100%'
            >
              <TableTitle
                deviceConfigs={deviceConfigs}
                totalCount={totalCount}
                view={view}
                handleViewChange={handleViewChange}
              />

              <Box
                display='flex'
                flexDirection='row'
                height='100%'
                width='100%'
                minHeight={0}
              >
                {view !== InstanceView.ALL_INSTANCES && (
                  <ApplicationPane
                    isFetching={isUserConfigDataLoading}
                    deviceConfigs={deviceConfigs}
                    totalCount={totalCount}
                  />
                )}

                <InstanceList
                  key={approvalFlowStatus?.results?.length ?? 0}
                  data-testid='InstanceList'
                  instances={instances}
                  fetchCsv={getInstanceCsvUrl}
                  approvalFlowStatus={approvalFlowStatus?.results}
                  refetchApprovalFlowStatus={refetchApprovalFlowStatus}
                  isApprovalFlowDataLoading={isApprovalFlowDataLoading}
                />
              </Box>

              {totalCount > 0 && (
                <Box
                  height='48px'
                  display='flex'
                  justifyContent='center'
                  py={1}
                  bgcolor='white'
                >
                  <Pagination
                    count={ceil(totalCount / 20)}
                    page={pageNumber ? parseInt(pageNumber, 10) : 1}
                    color='primary'
                    shape='rounded'
                    onChange={handlePaginationChange}
                  />
                </Box>
              )}
            </Box>
          );
        }
        return (
          <Box
            display='flex'
            flexDirection='column'
            flexGrow={1}
            flexShrink={1}
            minHeight={0}
          >
            <ScheduleList />
          </Box>
        );
      })()}
      {isConfigurationSettingsOpen &&
        Boolean(deviceConfigs?.length) &&
        (isCompanyAdmin || isPowerUser) && (
          <ConfigurationSettingsDialog
            isVisible={isConfigurationSettingsOpen}
            onClose={() => setStates({ isConfigurationSettingsOpen: false })}
            data={deviceConfigs}
            loading={false}
          />
        )}

      {Boolean(deviceConfigs?.length) && (
        <>
          {isCreateInstanceDialogOpen && (
            <CreateInstanceDialog
              open={isCreateInstanceDialogOpen}
              deviceConfigs={deviceConfigs}
              onCreateSuccess={async id => {
                setStates({ isCreateInstanceDialogOpen: false });
                navigate(getInstanceUrl(id), { replace: true });
              }}
              onClose={() => setStates({ isCreateInstanceDialogOpen: false })}
            />
          )}
          {isExportInstanceDialogOpen && (
            <ExportInstancesDialog
              open={isExportInstanceDialogOpen}
              deviceConfigs={deviceConfigs}
              onDownloadSuccess={() => {
                setStates({ isExportInstanceDialogOpen: false });
              }}
              onClose={() => {
                setStates({ isExportInstanceDialogOpen: false });
              }}
            />
          )}
        </>
      )}
    </MainLayout>
  );
};

export default ConfigList;
