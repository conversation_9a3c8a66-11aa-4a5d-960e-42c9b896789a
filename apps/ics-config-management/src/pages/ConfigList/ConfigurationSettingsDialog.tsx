import React, { useEffect, useMemo, useState, useCallback } from 'react';
import {
  Drawer,
  Box,
  Typography,
  IconButton,
  Tabs,
  Tab,
  Button,
  CircularProgress,
  TextField,
  MenuItem,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { GridColDef } from '@mui/x-data-grid';
import { useSnackbar } from 'notistack';
import compact from 'lodash/compact';
import isEqual from 'lodash/isEqual';
import groupBy from 'lodash/groupBy';
import map from 'lodash/map';
import DataGrid from '../../components/DataGrid';
import {
  useGetEntitySettingsStatus,
  usePostEntitySetting,
  usePutEntitySettings,
} from '../../services/use-query';
import { hasApprovalRequirement } from '../../utils/helpers';
import {
  EntitySettingType,
  NewEntitySettingPayload,
  UpdateEntitySettingsPayload,
} from '../../constants/types';

interface ApprovalSettingsDrawerProps {
  isVisible: boolean;
  onClose: () => void;
  data: any[];
  loading: boolean;
}

const columns: GridColDef[] = [
  {
    field: 'configFileName',
    headerName: 'File Name',
    flex: 1,
  },
];

export type ApplicationConfig = {
  configFileId: string | number;
  configFileName: string;
  configFileDottedString: string;
  configFileType: string;
  configSchemaRenderType?: string;
  schemaType?: string;
  applicationName: string;
};

export type FilteredConfig = {
  id: number | string;
  configFileName: string;
};

const ConfigurationSettingsDialog: React.FC<ApprovalSettingsDrawerProps> = ({
  isVisible,
  onClose,
  data,
  loading,
}) => {
  const [searchText, setSearchText] = useState('');
  const [selectedApplication, setSelectedApplication] = useState('');
  const [pageSize, setPageSize] = useState(10);

  const [initialEnabledIds, setInitialEnabledIds] = useState<string[]>([]);
  const [selectedConfigIds, setSelectedConfigIds] = useState<string[]>([]);

  const { enqueueSnackbar } = useSnackbar();

  const {
    data: dataEntitySettingsStatus,
    isLoading: isFetchingEntitySettings,
    refetch: refetchEntitySettings,
  } = useGetEntitySettingsStatus('config-instance-draft', {
    cacheTime: 0,
    staleTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  useEffect(() => {
    if (!isVisible) {
      // reset all relevant state when dialog closes
      setSearchText('');
      setSelectedApplication('');
      setSelectedConfigIds(initialEnabledIds);
    }
  }, [isVisible]);

  const transformedData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // flatten all configs with appDescriptorId
    const allConfigs = data.flatMap(app =>
      (app.applicationConfigs || []).map(config => ({
        configFileId: String(config.configFileId),
        configFileName: config.configFileName,
        appDescriptorId: String(app.appDescriptorId),
      }))
    );

    // group by configFileId
    const grouped = groupBy(allConfigs, 'configFileId');

    // map to desired shape
    return map(grouped, (items, configFileId) => ({
      id: String(configFileId),
      configFileName: items[0].configFileName,
      appDescriptors: items.map(i => i.appDescriptorId),
    }));
  }, [data]);

  const { mutate: createNewEntitySetting, isLoading: isCreating } =
    usePostEntitySetting();

  const { mutate: putEntitySettings, isLoading: isUpdating } =
    usePutEntitySettings();

  const handleConfirm = useCallback(async () => {
    // IDs that were deselected
    const deselected = initialEnabledIds.filter(
      id => !selectedConfigIds.includes(id)
    );
    // IDs that were newly selected
    const newlySelected = selectedConfigIds.filter(
      id => !initialEnabledIds.includes(id)
    );

    // find the entity for each id
    const getEntityById = (id: string) =>
      dataEntitySettingsStatus?.find(
        item => String(item.condition?.configFileId) === id
      );

    // only for items that exist in dataEntitySettingsStatus
    const putPayload: UpdateEntitySettingsPayload[] = [
      ...deselected.map(id => {
        const entity = getEntityById(id);
        return entity
          ? {
              id: entity.id,
              isEnabled: false,
              rolesNotRequireApproval: ['CONFIG_MGMT_APPROVER'],
            }
          : null;
      }),
      ...newlySelected.map(id => {
        const entity = getEntityById(id);
        return entity
          ? {
              id: entity.id,
              isEnabled: true,
              rolesNotRequireApproval: ['CONFIG_MGMT_APPROVER'],
            }
          : null;
      }),
    ].filter(Boolean);

    // for newly selected items that do not exist in dataEntitySettingsStatus
    const postPayload: NewEntitySettingPayload[] = newlySelected
      .filter(id => !getEntityById(id))
      .map(id => {
        const config = transformedData.find(row => row.id === id);
        return config
          ? {
              condition: { configFileId: Number(config.id) },
              entityType: EntitySettingType.CONFIG_INSTANCE_DRAFT,
              rolesNotRequireApproval: ['CONFIG_MGMT_APPROVER'],
            }
          : null;
      })
      .filter(Boolean);

    const putPromise =
      putPayload.length > 0
        ? new Promise((resolve, reject) => {
            putEntitySettings(putPayload, {
              onSuccess: resolve,
              onError: reject,
            });
          })
        : Promise.resolve();

    const postPromise =
      postPayload.length > 0
        ? new Promise((resolve, reject) => {
            createNewEntitySetting(postPayload, {
              onSuccess: resolve,
              onError: reject,
            });
          })
        : Promise.resolve();

    try {
      await Promise.all([putPromise, postPromise]);
      enqueueSnackbar('Configuration Settings updated successfully', {
        variant: 'success',
      });
      await refetchEntitySettings();
      onClose();
    } catch (error: any) {
      const message =
        error?.response?.data?.message || 'Failed to update entity settings';
      enqueueSnackbar(message, { variant: 'error' });
    }
  }, [
    initialEnabledIds,
    selectedConfigIds,
    dataEntitySettingsStatus,
    putEntitySettings,
    createNewEntitySetting,
    transformedData,
  ]);

  useEffect(() => {
    if (dataEntitySettingsStatus?.length) {
      const initialIds = compact(
        dataEntitySettingsStatus
          .filter(item => item.isEnabled)
          .map(item => item.condition?.configFileId?.toString())
      ) as string[];

      setInitialEnabledIds(initialIds);
      setSelectedConfigIds(initialIds);
    }
  }, [dataEntitySettingsStatus]);

  // prepare the data for dataGrid

  const filteredConfigData = useMemo(() => {
    if (!data || data.length === 0) {
      return [];
    }

    // if application has been filtered
    const applicationFilteredData = selectedApplication
      ? transformedData.filter(row =>
          row.appDescriptors.includes(String(selectedApplication))
        )
      : transformedData;

    if (!searchText.trim()) return applicationFilteredData;

    // if user has searched any input
    const searchedFilteredData = applicationFilteredData.filter(item =>
      item.configFileName
        .toLowerCase()
        .includes(searchText.trim().toLowerCase())
    );

    return searchedFilteredData;
  }, [data, searchText, selectedApplication]);

  const onSelectionModelChange = useCallback(
    newSelectionModel => {
      const visibleIds = filteredConfigData.map(row => row.id);
      const nonVisibleSelectedIds = selectedConfigIds.filter(
        id => !visibleIds.includes(id)
      );

      setSelectedConfigIds([
        ...nonVisibleSelectedIds,
        ...newSelectionModel.map(String),
      ]);
    },
    [filteredConfigData, selectedConfigIds, setSelectedConfigIds]
  );

  const hasSelectionChanged = useMemo(
    () =>
      !isEqual([...selectedConfigIds].sort(), [...initialEnabledIds].sort()),
    [selectedConfigIds, initialEnabledIds]
  );

  return (
    <Drawer
      anchor='right'
      hideBackdrop
      open={isVisible}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: 900,
          borderRadius: '10px 0 0 10px',
          bgcolor: 'common.backgroundLight',
        },
      }}
    >
      <Box
        p={2}
        display='flex'
        justifyContent='space-between'
        alignItems='center'
      >
        <Typography variant='h5'>Configuration Settings</Typography>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </Box>
      <Typography
        variant='h6'
        sx={{
          margin: '5px',
          paddingLeft: '15px',
        }}
      >
        Manage Configuration File Settings
      </Typography>
      <Box />

      <Box px={2}>
        <Tabs value={0}>
          <Tab label='Approval Settings' />
        </Tabs>
      </Box>

      {/* Search & Dropdown */}
      <Box display='flex' gap={2} p={2}>
        <TextField
          size='small'
          fullWidth
          label='Search by File Name'
          placeholder='Search by File Name'
          value={searchText}
          onChange={e => {
            setSearchText(e.target.value);
          }}
          InputProps={{
            endAdornment: searchText && (
              <IconButton
                size='small'
                onClick={() => setSearchText('')}
                edge='end'
                sx={{ visibility: searchText ? 'visible' : 'hidden' }}
              >
                <CloseIcon fontSize='small' />
              </IconButton>
            ),
          }}
        />
        <TextField
          select
          size='small'
          label='Applications'
          value={selectedApplication}
          onChange={e => setSelectedApplication(e.target.value)}
          sx={{ minWidth: 250 }}
        >
          <MenuItem
            sx={{
              fontStyle: 'italic',
            }}
          >
            No Applications
          </MenuItem>
          {data.map(deviceConfig => (
            <MenuItem
              key={deviceConfig.appDescriptorId}
              value={deviceConfig.appDescriptorId}
            >
              Application: {deviceConfig.applicationName}
            </MenuItem>
          ))}
        </TextField>
      </Box>

      <Box p={2} flex={1} display='flex' flexDirection='column' minHeight={0}>
        {loading ? (
          <Box
            flex={1}
            display='flex'
            justifyContent='center'
            alignItems='center'
          >
            <CircularProgress />
          </Box>
        ) : (
          <DataGrid
            autoHeight
            key={filteredConfigData.length}
            sx={{
              flexGrow: 1,
              '& .MuiDataGrid-virtualScroller': {
                overflowY: 'auto',
              },
            }}
            rows={filteredConfigData}
            onPageSizeChange={newPageSize => setPageSize(newPageSize)}
            columns={columns}
            loading={loading || isFetchingEntitySettings}
            checkboxSelection
            pagination
            isRowSelectable={params =>
              hasApprovalRequirement(params.row.configFileName)
            }
            pageSize={pageSize}
            selectionModel={selectedConfigIds.filter(id =>
              filteredConfigData.some(row => row.id === id)
            )}
            onSelectionModelChange={onSelectionModelChange}
            getRowId={row => row.id}
          />
        )}

        <Box mt={2} display='flex' justifyContent='flex-end' gap={2}>
          <Button variant='outlined' onClick={onClose}>
            Cancel
          </Button>
          <Button
            variant='contained'
            onClick={handleConfirm}
            disabled={!hasSelectionChanged || isUpdating || isCreating}
          >
            Confirm
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
};

export default ConfigurationSettingsDialog;
