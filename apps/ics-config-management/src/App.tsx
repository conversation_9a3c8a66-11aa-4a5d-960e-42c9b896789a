/* eslint-disable import/no-cycle */
import { ThemeProvider } from '@mui/material';
import { SnackbarProvider } from 'notistack';
import React, { Suspense } from 'react';
import { BrowserRouter, Navigate, useRoutes } from 'react-router-dom';
import { QueryClientProvider, QueryClient } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

import {
  CONFIG_INSTANCE_PATH,
  CONFIG_INSTANCE_TAB_PATH,
  CONFIG_LIST_PATH,
  DEPLOYMENT_LIST_PATH,
  SCHEDULE_LIST_PATH,
  DEPLOYMENT_EVENT_LIST_PATH,
} from './constants/routes';
import { snackBar, theme } from './constants/theme';
import { AppProvider } from './hooks/Context';
import lazyWithPreload from './utils/lazyWithPreload';
import MainLayoutSkeleton from './layouts/MainLayoutSkeleton';

const ConfigList = lazyWithPreload(
  () => import('./pages/ConfigList/ConfigList')
);
const ConfigInstance = lazyWithPreload(() => import('./pages/ConfigInstance'));

const DeploymentEventDetails = lazyWithPreload(
  () => import('./pages/DeploymentEventDetails')
);

ConfigList.preload();
ConfigInstance.preload();
DeploymentEventDetails.preload();

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: false,
      staleTime: 2 * 60 * 1000,
      cacheTime: 2 * 60 * 1000,
    },
  },
});

const {
  styles,
  constants: { duration, max, documentRoot },
} = snackBar;

const Routes = () =>
  useRoutes([
    {
      path: CONFIG_LIST_PATH,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <ConfigList />
        </Suspense>
      ),
    },
    {
      path: SCHEDULE_LIST_PATH,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <ConfigList />
        </Suspense>
      ),
    },
    {
      path: CONFIG_INSTANCE_PATH,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <ConfigInstance />
        </Suspense>
      ),
    },
    {
      path: CONFIG_INSTANCE_TAB_PATH,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <ConfigInstance />
        </Suspense>
      ),
    },
    {
      path: DEPLOYMENT_LIST_PATH,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <ConfigList />
        </Suspense>
      ),
    },
    {
      path: DEPLOYMENT_EVENT_LIST_PATH,
      element: (
        <Suspense fallback={<MainLayoutSkeleton />}>
          <DeploymentEventDetails />
        </Suspense>
      ),
    },
    { path: '*', element: <Navigate to={CONFIG_LIST_PATH} replace /> },
  ]);
const Root = () => (
  <div className='ics-app'>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <SnackbarProvider
          maxSnack={max}
          autoHideDuration={duration}
          style={styles}
          domRoot={documentRoot}
        >
          <AppProvider>
            <BrowserRouter>
              <Routes />
            </BrowserRouter>
          </AppProvider>
        </SnackbarProvider>
      </ThemeProvider>
      <ReactQueryDevtools position='bottom-left' />
    </QueryClientProvider>
  </div>
);

export default Root;
