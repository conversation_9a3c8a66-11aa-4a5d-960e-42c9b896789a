import { GridColDef } from '@mui/x-data-grid';

enum ExportInstanceDialogContent {
  title = 'Export configurations',
  content = 'All configuration instances will be combined into a csv',
  action = 'Export',
}

enum SharedDialogContent {
  cancel = 'Cancel',
}

enum ConfigInstanceActionsContent {
  callToAction = 'Actions',
  createInstanceItem = 'Create config instance',
  exportInstanceItem = 'Export config instance (csv)',
  configurationSettings = 'Configuration Settings',
}

enum DeleteConfigInstanceDialogContent {
  callToAction = 'Delete configuration instance',
  dialogContent = `Once deleted you can't get this configuration instance back`,
  dialogContentWarningStart = 'This Configuration Instance has active assignment(s). Please go to the ',
  assignmentTabLink = 'Assignment Tab',
  dialogContentWarningEnd = ' and remove the assignment(s) first.',
  dialogTitle = 'Are you sure?',
  dialogTitleWarning = `Can't delete`,
  deleteSuccessMessage = 'Configuration instance ($instanceId) deleted successfully',
  deleteErrorMessage = 'Error deleting configuration instance',
  cancel = 'Cancel',
  delete = 'Delete',
}

export const CONFIG_VERSION = 'Config Version';

export const tableColumns: GridColDef[] = [
  { field: 'deviceId', headerName: 'Device ID', flex: 1, minWidth: 150 },
  { field: 'appName', headerName: 'App Name', flex: 1, minWidth: 150 },
  {
    field: 'activationMethod',
    headerName: 'Activation Method',
    flex: 1,
    minWidth: 150,
  },
  {
    field: 'activationJobStatus',
    headerName: 'Activation Job Status',
    flex: 1,
    minWidth: 150,
  },
  {
    field: 'deployedJobId',
    headerName: 'Deployed Job ID',
    flex: 1,
    minWidth: 150,
  },
  {
    field: 'deployedJobStatus',
    headerName: 'Deployed Job Status',
    flex: 1,
    minWidth: 150,
  },
  {
    field: 'renderError',
    headerName: 'Render Error',
    flex: 1,
    minWidth: 150,
  },
  {
    field: 'insertedAt',
    headerName: 'Inserted At',
    flex: 1,
    minWidth: 150,
  },
];

export const DEFAULT_ROWS_PER_PAGE = 20;
export const DEFAULT_PAGE = 1;
export const ENTITY_TYPE = 'config-instance-draft';
export const ENTITY_STATUS = ['pending'];

export {
  ExportInstanceDialogContent,
  SharedDialogContent,
  ConfigInstanceActionsContent,
  DeleteConfigInstanceDialogContent,
};
