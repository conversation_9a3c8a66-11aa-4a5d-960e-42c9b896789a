import React from 'react';
import ReactDOMClient from 'react-dom/client';
import singleSpaReact from 'single-spa-react';
import Root from './root.component';

// Support Standalone Mode
const domElementGetter = () =>
  document.getElementById('main-app') ||
  (() => {
    const container = document.createElement('div');
    container.className = 'main-app';
    document.body.appendChild(container);
    return container;
  })();

const lifecycles = singleSpaReact({
  React,
  ReactDOMClient,
  rootComponent: Root,
  errorBoundary(err, errInfo, props) {
    console.error(err?.message, errInfo, props);
    return <div />;
  },
  domElementGetter,
});

export const { bootstrap, mount, unmount } = lifecycles;
