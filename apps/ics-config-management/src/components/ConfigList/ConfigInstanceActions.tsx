import React from 'react';
import { Menu, MenuItem } from '@mui/material';
import { ArrowDropDown } from '@mui/icons-material';
import { LoadingButton } from '@mui/lab';
import PopupState, { bindTrigger, bindMenu } from 'material-ui-popup-state';

import { ConfigInstanceActionsContent } from '../../constants/staticContent';

type ConfigInstanceActionsProps = {
  loading: boolean;
  onCreateInstanceClick: () => void;
  onExportInstanceClick: () => void;
  openConfigurationSettingsDialog: () => void;
  hasConfigurationSettingsPermission: boolean;
};

const ConfigInstanceActions = ({
  loading,
  onCreateInstanceClick,
  onExportInstanceClick,
  openConfigurationSettingsDialog,
  hasConfigurationSettingsPermission,
}: ConfigInstanceActionsProps) => (
  <PopupState variant='popover' popupId='config-instance-actions'>
    {popupState => (
      <>
        <LoadingButton
          data-testid='createBtn'
          loading={loading}
          variant='outlined'
          endIcon={<ArrowDropDown />}
          {...bindTrigger(popupState)}
        >
          {ConfigInstanceActionsContent.callToAction}
        </LoadingButton>
        <Menu {...bindMenu(popupState)}>
          <MenuItem
            onClick={() => {
              popupState.close();
              onCreateInstanceClick();
            }}
          >
            {ConfigInstanceActionsContent.createInstanceItem}
          </MenuItem>
          <MenuItem
            onClick={() => {
              popupState.close();
              onExportInstanceClick();
            }}
          >
            {ConfigInstanceActionsContent.exportInstanceItem}
          </MenuItem>
          {hasConfigurationSettingsPermission && (
            <MenuItem
              onClick={() => {
                popupState.close();
                openConfigurationSettingsDialog();
              }}
            >
              {ConfigInstanceActionsContent.configurationSettings}
            </MenuItem>
          )}
        </Menu>
      </>
    )}
  </PopupState>
);

export default ConfigInstanceActions;
