import React, { memo, useCallback, useState } from 'react';
import { <PERSON><PERSON>, Box, Dialog, Typography, Divider } from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { useSnackbar } from 'notistack';
import isEqual from 'lodash/isEqual';
import { CheckLg } from 'react-bootstrap-icons';
import { WarningAmber } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import {
  usePostAssignments,
  useGetMaintenanceWindow,
  useGetInstanceDetails,
  useGetDeviceCountByTenant,
} from '../../services/use-query';
import useHasPermissions from '../../hooks/useHasPermissions';
import { RevisionPublishDeploymentType } from '../../constants/types';
import HTTP_STATUS from '../../constants/httpCodes';
import UserRoles from '../../constants/userRoles';
import FeatureFlags from '../../constants/featureFlags';
import { getFormattedDescription } from '../../utils/helpers';
import TwoFactorVerification from '../TwoFactorAuthentication/TwoFactorAuthentication';
import { getSiteCount, getSiteTagCount } from '../../services/api-request';
import {
  AssignmentDeploymentTypeDialogProps,
  AssignmentDeploymentTypeDialogStep,
  AssignmentDeploymentTypeDialogState,
  TimeAndDateFormatInScheduleWindowType,
} from './types';
import {
  AssignmentDeploymentTypeDialogText,
  AssignmentDeploymentTypeDialogContent,
} from './constants';

const AssignmentDeploymentTypeDialog = memo(
  ({
    assignments,
    defaultDeploymentType,
    instanceId,
    isLoading,
    onSubmit,
    isMfaAccess,
    selectedAssignments,
    level,
    disableDeployButton,
  }: AssignmentDeploymentTypeDialogProps) => {
    const hasImmediateDeploymentAccess = useHasPermissions({
      userRoles: [UserRoles.CONFIG_MGMT_DEPLOY],
      companyFeatureFlags: [FeatureFlags.CONFIG_MGMT],
    });
    const [confirmDialogState, setConfirmDialogState] =
      useState<AssignmentDeploymentTypeDialogState>({
        isDialogOpen: false,
        step: AssignmentDeploymentTypeDialogStep.confirmation,
      });
    const { enqueueSnackbar } = useSnackbar();
    const { data: dataInstanceDetails } = useGetInstanceDetails(instanceId);
    const { isDialogOpen, step } = confirmDialogState;
    const { data: NextMaintenanceIntimationWindow } = useGetMaintenanceWindow(
      isDialogOpen &&
        step === AssignmentDeploymentTypeDialogStep.confirmationMaintainance
    );
    const [isTwoFactorOpen, setTwoFactorOpen] = useState(false);
    const [mfaCode, setMfaCode] = useState('');
    const [mfaErrorTrigger, setMfaErrorTrigger] = useState(0);
    const [isTotalDeviceCount, setIsTotalDeviceCount] = useState<number>(0);
    const { title, description, primaryAction, secondaryAction } =
      AssignmentDeploymentTypeDialogText[step];

    const navigate = useNavigate();

    const { refetch: reFetchDeviceCountByTenant } = useGetDeviceCountByTenant(
      {
        pageIndex: 0,
        pageSize: 20,
        fields: [],
      },
      {
        enabled: false,
      }
    );

    const getDeviceCount = useCallback(async () => {
      const selectedSites =
        selectedAssignments?.Site?.map(site => site.assignmentValue) ?? [];
      const selectedSiteTags =
        selectedAssignments?.SiteTag?.map(tag => tag.assignmentValue) ?? [];
      const selectedTenant =
        selectedAssignments?.Tenant?.map(tenant => tenant.assignmentValue) ??
        [];
      const selectedDevices =
        selectedAssignments?.Device?.map(device => device.assignmentValue) ??
        [];
      let totalDeviceCount = selectedDevices.length || 0;

      if (selectedTenant.length > 0) {
        const result = await reFetchDeviceCountByTenant();
        totalDeviceCount = result?.data?.resultsMetadata?.totalResults;
      } else {
        if (selectedSiteTags.length > 0) {
          try {
            const fields = [];
            const siteTagCount = await getSiteTagCount({
              pageIndex: 0,
              pageSize: 20,
              fields,
              selecteddevices: selectedDevices,
              selectedSiteTags,
            });
            totalDeviceCount +=
              siteTagCount?.resultsMetadata?.totalResults || 0;
          } catch (error) {
            console.error('Error fetching devices:', error);
          }
        }

        if (selectedSites.length > 0) {
          try {
            const fields = [];
            const sitesCount = await getSiteCount({
              pageIndex: 0,
              pageSize: 20,
              fields,
              selecteddevices: selectedDevices,
              selectedSites,
            });
            totalDeviceCount += sitesCount?.resultsMetadata?.totalResults || 0;
          } catch (error) {
            console.error('Error fetching devices:', error);
          }
        }
      }
      totalDeviceCount = Number.isFinite(totalDeviceCount)
        ? totalDeviceCount
        : 0;
      setIsTotalDeviceCount(totalDeviceCount);
    }, [selectedAssignments, level]);

    const handleCloseDialog = useCallback(() => {
      setConfirmDialogState({
        isDialogOpen: false,
        step: AssignmentDeploymentTypeDialogStep.confirmation,
      });
      onSubmit();
      setMfaCode('');
    }, [confirmDialogState, setConfirmDialogState, onSubmit]);

    const getDeploymentMessage = useCallback((deploymentType: string) => {
      switch (deploymentType) {
        case 'immediate':
          return AssignmentDeploymentTypeDialogContent.immediate;
        case 'maintenance-window':
          return AssignmentDeploymentTypeDialogContent.maintenancewindow;
        default:
          return AssignmentDeploymentTypeDialogContent.immediate;
      }
    }, []);

    const { mutate: postAssignments, isLoading: isPosting } =
      usePostAssignments(
        {
          instanceId,
          configFileId: dataInstanceDetails.configFileId,
          appDescriptorId: dataInstanceDetails.appDescriptorId,
          appName: dataInstanceDetails.appName,
        },
        {
          onSuccess: (data, variables) => {
            const message = getDeploymentMessage(variables.deploymentType);
            if (message) {
              enqueueSnackbar(message, {
                variant: 'success',
              });
            }
            navigate(
              `/remote/config-management/instance/${instanceId}/revisions`
            );

            // Close the TwoFactor modal on successful API call
            setTwoFactorOpen(false);
            setMfaCode('');
            setMfaErrorTrigger(0);

            handleCloseDialog();
          },
          onError: error => {
            // Check if it's an MFA validation error (typically 400 Bad Request)
            if (error?.status === HTTP_STATUS.BadRequest || error?.status === 400) {
              const errorMessage = error?.validationFailureMessage || error?.data || error?.message || '';
              console.log('400 Error details in AssignmentDeploymentTypeModal:', { errorMessage, fullError: error });

              if (errorMessage.toLowerCase().includes('mfa') ||
                  errorMessage.toLowerCase().includes('authentication') ||
                  errorMessage.toLowerCase().includes('invalid code') ||
                  errorMessage.toLowerCase().includes('two-factor') ||
                  errorMessage.toLowerCase().includes('2fa')) {
                console.log('Detected MFA error in AssignmentDeploymentTypeModal, triggering MFA error handler');
                // Trigger MFA error handling in the TwoFactorVerification component
                setMfaErrorTrigger(prev => prev + 1);
                return;
              }
            }

            let message = '';
            // as the error response structure is different for Tenant and other level hierarchy,
            // that's why the different if-else condition for the error message to set
            if (error.status) {
              message = `Error while assigning the assets: ${error.validationFailureMessage}`;
            } else if (error.configAssignmentRequest) {
              message = `Error while assigning the assets: ${error.configAssignmentRequest[0]}`;
            } else {
              message = `Error while assigning the assets: ${error}`;
            }
            if (
              error?.status === HTTP_STATUS.Conflict &&
              typeof error?.data === 'string'
            ) {
              message =
                error?.data ||
                AssignmentDeploymentTypeDialogContent.errorSnackBarConflict;
            } else if (
              error?.status === HTTP_STATUS.BadRequest &&
              typeof error?.data === 'string'
            ) {
              message =
                error?.data ||
                AssignmentDeploymentTypeDialogContent.errorSnackBar;
            } else if (
              error?.response?.status === HTTP_STATUS.BadRequest &&
              error?.response?.data?.validationFailure
            ) {
              message =
                error?.response?.data?.validationFailureMessage ||
                AssignmentDeploymentTypeDialogContent.errorSnackBar;
            }
            enqueueSnackbar(message, {
              variant: 'error',
            });
            navigate(`/remote/config-management/`);
          },
        }
      );

    const handleSubmitConfirm = useCallback(
      async ({
        deploymentType,
        currentCode,
      }: {
        deploymentType: RevisionPublishDeploymentType;
        currentCode?: string;
      }) => {
        postAssignments({
          assignments,
          deploymentType,
          mfaCode: currentCode,
        });
      },
      [assignments, confirmDialogState, instanceId, setConfirmDialogState, step]
    );

    const handleOpenDialog = useCallback(async () => {
      await getDeviceCount();
      if (hasImmediateDeploymentAccess) {
        setConfirmDialogState(
          (prevState: AssignmentDeploymentTypeDialogState) => ({
            ...prevState,
            isDialogOpen: true,
          })
        );
        return;
      }
      await handleSubmitConfirm({
        deploymentType:
          defaultDeploymentType ??
          RevisionPublishDeploymentType.maintenanceWindow,
      });
    }, [
      assignments,
      confirmDialogState,
      setConfirmDialogState,
      hasImmediateDeploymentAccess,
    ]);

    const handleMfa = useCallback(
      async (
        deploymentType: RevisionPublishDeploymentType,
        currentCode?: string
      ) => {
        // If MFA feature flag is present
        if (isMfaAccess) {
          if (!currentCode) {
            setMfaCode('');
            setTwoFactorOpen(true);
          } else if (
            currentCode &&
            NextMaintenanceIntimationWindow?.isNext &&
            deploymentType === 'maintenance-window'
          ) {
            if (step === AssignmentDeploymentTypeDialogStep.scheduleWindow) {
              await handleSubmitConfirm({ deploymentType, currentCode });
              setMfaCode('');
            } else {
              setConfirmDialogState(
                (prevState: AssignmentDeploymentTypeDialogState) => ({
                  ...prevState,
                  step: AssignmentDeploymentTypeDialogStep.scheduleWindow,
                })
              );
            }
          } else if (currentCode && deploymentType === 'immediate') {
            await handleSubmitConfirm({ deploymentType, currentCode });
            setMfaCode('');
          } else {
            await handleSubmitConfirm({ deploymentType, currentCode });
            setMfaCode('');
          }
        } else if (
          deploymentType !== 'immediate' &&
          NextMaintenanceIntimationWindow?.isNext
        ) {
          if (step === AssignmentDeploymentTypeDialogStep.scheduleWindow) {
            await handleSubmitConfirm({ deploymentType });
          } else {
            setConfirmDialogState(
              (prevState: AssignmentDeploymentTypeDialogState) => ({
                ...prevState,
                step: AssignmentDeploymentTypeDialogStep.scheduleWindow,
              })
            );
          }
        } else {
          await handleSubmitConfirm({ deploymentType });
        }
      },
      [isMfaAccess, handleSubmitConfirm]
    );

    const handlePrimaryAction = useCallback(
      async (code?: string) => {
        const currentCode = code || mfaCode;
        switch (step) {
          case AssignmentDeploymentTypeDialogStep.confirmation:
            setConfirmDialogState(
              (prevState: AssignmentDeploymentTypeDialogState) => ({
                ...prevState,
                step: AssignmentDeploymentTypeDialogStep.confirmationImmediate,
              })
            );
            break;
          case AssignmentDeploymentTypeDialogStep.confirmationImmediate:
            handleMfa(RevisionPublishDeploymentType.immediate, currentCode);
            break;
          case AssignmentDeploymentTypeDialogStep.confirmationMaintainance:
            handleMfa(
              RevisionPublishDeploymentType.maintenanceWindow,
              currentCode
            );
            break;
          case AssignmentDeploymentTypeDialogStep.scheduleWindow:
            handleMfa(
              RevisionPublishDeploymentType.maintenanceWindow,
              currentCode
            );
            break;
          default:
            break;
        }
      },
      [step, assignments, instanceId, mfaCode]
    );

    const handleSecondaryAction = useCallback(async () => {
      switch (step) {
        case AssignmentDeploymentTypeDialogStep.confirmation:
          setConfirmDialogState(
            (prevState: AssignmentDeploymentTypeDialogState) => ({
              ...prevState,
              step: AssignmentDeploymentTypeDialogStep.confirmationMaintainance,
            })
          );
          break;
        default:
          setConfirmDialogState(
            (prevState: AssignmentDeploymentTypeDialogState) => ({
              ...prevState,
              step: AssignmentDeploymentTypeDialogStep.confirmation,
            })
          );
          break;
      }
    }, [
      step,
      assignments,
      instanceId,
      confirmDialogState,
      setConfirmDialogState,
    ]);

    const timeAndDateFormatInScheduleWindow: TimeAndDateFormatInScheduleWindowType =
      (from, to) => {
        // Helper function to format time
        const formatTime = (date: string | Date | undefined) =>
          new Date(date as string)
            .toLocaleString('en-GB', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true,
              timeZone: 'GMT',
            })
            .replace(' ', '');

        // Helper function to format date
        const formatDate = (date: string | Date | undefined) =>
          new Date(date as string).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            timeZone: 'GMT',
          });

        return `Est. ${formatTime(from)} - ${formatTime(to)} GMT ${formatDate(
          from
        )}`;
      };

    const formattedDescription = getFormattedDescription(
      description,
      dataInstanceDetails,
      isTotalDeviceCount
    );

    const handleCancelTwoFactor = () => {
      setTwoFactorOpen(false);
      setMfaErrorTrigger(0);
      setMfaCode('');
    };
    const dialogStyles =
      step === AssignmentDeploymentTypeDialogStep.scheduleWindow
        ? {
            borderRadius: '16px',
            width: '624px',
            height: '190px',
            padding: 3,
            display: 'flex',
            justifyContent: 'space-between',
            backgroundColor: 'common.modalBackground',
            boxSizing: 'unset',
            overflow: 'hidden',
          }
        : {
            borderRadius: '20px',
            width: 500,
            padding: 3,
            display: 'flex',
            justifyContent: 'space-between',
            backgroundColor: 'common.modalBackground',
            boxSizing: 'unset',
          };

    return (
      <>
        {!isTwoFactorOpen && (
          <Dialog
            open={isDialogOpen}
            PaperProps={{
              sx: dialogStyles,
            }}
          >
            <Box
              display='flex'
              flexDirection='column'
              gap={
                step === AssignmentDeploymentTypeDialogStep.scheduleWindow
                  ? 1
                  : 2
              }
            >
              <Box
                display='flex'
                flexDirection='column'
                gap={
                  step === AssignmentDeploymentTypeDialogStep.scheduleWindow
                    ? 1
                    : 2
                }
              >
                <Box display='flex' alignItems='center' gap={1}>
                  <WarningAmber sx={{ color: 'orange', fontSize: '24px' }} />
                  <Typography
                    variant='h6'
                    sx={
                      step === AssignmentDeploymentTypeDialogStep.scheduleWindow
                        ? {
                            fontFamily: 'Roboto',
                            fontWeight: 500,
                            fontSize: '15px',
                            lineHeight: '24px',
                            letterSpacing: '0.1px',
                          }
                        : {}
                    }
                  >
                    {title}
                  </Typography>
                </Box>
                {step === AssignmentDeploymentTypeDialogStep.scheduleWindow && (
                  <Divider />
                )}
                <Typography
                  variant='bodyMedium'
                  sx={
                    step === AssignmentDeploymentTypeDialogStep.scheduleWindow
                      ? {
                          fontFamily: 'Roboto',
                          fontWeight: 400,
                          fontSize: '14px',
                          lineHeight: '20px',
                          letterSpacing: '0.25px',
                          color: '#5D5D67',
                        }
                      : { whiteSpace: 'pre-line' }
                  }
                >
                  {formattedDescription}
                  {step === AssignmentDeploymentTypeDialogStep.scheduleWindow &&
                    NextMaintenanceIntimationWindow && (
                      <Typography
                        sx={{
                          fontSize: '14px',
                          lineHeight: '20px',
                          paddingTop: '20px',
                        }}
                      >
                        {timeAndDateFormatInScheduleWindow(
                          NextMaintenanceIntimationWindow?.from,
                          NextMaintenanceIntimationWindow?.to
                        )}
                      </Typography>
                    )}
                </Typography>
                {step === AssignmentDeploymentTypeDialogStep.scheduleWindow && (
                  <Divider />
                )}
              </Box>
              <Box display='flex' flexDirection='column' gap={2}>
                <Box
                  display='flex'
                  justifyContent={
                    step === AssignmentDeploymentTypeDialogStep.scheduleWindow
                      ? 'flex-end'
                      : 'space-between'
                  }
                  alignItems='center'
                >
                  {step !==
                    AssignmentDeploymentTypeDialogStep.scheduleWindow && (
                    <Button
                      disabled={isPosting}
                      onClick={handleCloseDialog}
                      sx={{ alignSelf: 'flex-start' }}
                    >
                      {AssignmentDeploymentTypeDialogContent.closeDialogButton}
                    </Button>
                  )}
                  <Box display='flex' gap={2}>
                    {step ===
                      AssignmentDeploymentTypeDialogStep.scheduleWindow && (
                      <Box flexGrow={1} />
                    )}
                    {step ===
                    AssignmentDeploymentTypeDialogStep.scheduleWindow ? (
                      <Button disabled={isPosting} onClick={handleCloseDialog}>
                        {
                          AssignmentDeploymentTypeDialogContent.cancelDialogButton
                        }
                      </Button>
                    ) : (
                      secondaryAction && (
                        <Button
                          disabled={isPosting}
                          onClick={handleSecondaryAction}
                          variant='outlined'
                          sx={{
                            whiteSpace: 'nowrap',
                          }}
                        >
                          {secondaryAction}
                        </Button>
                      )
                    )}
                    {primaryAction && (
                      <Button
                        disabled={isPosting}
                        variant='contained'
                        onClick={() => handlePrimaryAction(mfaCode)}
                        sx={{
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {primaryAction}
                      </Button>
                    )}
                  </Box>
                </Box>
              </Box>
            </Box>
          </Dialog>
        )}
        <TwoFactorVerification
          open={isTwoFactorOpen}
          onClose={handleCancelTwoFactor}
          mfaErrorTrigger={mfaErrorTrigger}
          onSubmit={code => {
            setMfaCode(code);
            handlePrimaryAction(code);
          }}
        />
        <LoadingButton
          disabled={disableDeployButton}
          loading={isLoading}
          onClick={handleOpenDialog}
          sx={{ minWidth: '100px' }}
          variant='contained'
        >
          <Typography
            variant='body2'
            sx={{
              position: 'relative',
              right: '4px',
            }}
          >
            {`${AssignmentDeploymentTypeDialogContent.deployButton}`}
          </Typography>
          <CheckLg
            style={{
              position: 'relative',
              left: '5px',
              bottom: '1px',
            }}
            fontSize='medium'
          />
        </LoadingButton>
      </>
    );
  },
  (prevProps, nextProps) =>
    prevProps.isLoading === nextProps.isLoading &&
    isEqual(prevProps.assignments, nextProps.assignments) &&
    prevProps.instanceId === nextProps.instanceId
);

export default AssignmentDeploymentTypeDialog;
