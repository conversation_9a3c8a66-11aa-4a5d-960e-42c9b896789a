import React, { useCallback, useMemo } from 'react';
import type { WidgetProps } from '@rjsf/utils';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import IndeterminateCheckBoxIcon from '@mui/icons-material/IndeterminateCheckBox';
import { theme } from '../constants/theme';

const values = ['true', 'false', 'undefined'];

const IndeterminateCheckbox = (props: WidgetProps) => {
  const { disabled, name, value, onChange } = props;

  const currentIndex = useMemo(
    () =>
      values.indexOf(
        typeof value === 'undefined' ? 'undefined' : JSON.stringify(value)
      ),
    [value]
  );

  const handleOnChange = useCallback((newValue: string) => {
    if (newValue === 'undefined') {
      onChange(undefined);
      return;
    }
    onChange(JSON.parse(newValue));
  }, []);

  const handleOnClick = useCallback(() => {
    const nextIndex = (currentIndex + 1) % values.length;
    handleOnChange(values[nextIndex]);
  }, [currentIndex, handleOnChange]);

  return (
    <Button
      disabled={disabled}
      disableRipple
      onClick={handleOnClick}
      sx={{
        color: theme.palette.common.greyIcon,
        display: 'flex',
        fontWeight: 'normal',
        fontSize: '1.57rem',
        justifyContent: 'flex-start',
        textTransform: 'none',
        '&:focus': {
          outline: 'none',
        },
        '&:hover': {
          backgroundColor: 'transparent',
        },
        p: 0,
        width: '100%',
      }}
      variant='text'
    >
      <IconButton
        sx={{
          '&:focus': {
            outline: 'none',
          },
        }}
      >
        {values[currentIndex] === 'true' && <CheckBoxIcon color='primary' />}
        {values[currentIndex] === 'false' && <CheckBoxOutlineBlankIcon />}
        {values[currentIndex] === 'undefined' && <IndeterminateCheckBoxIcon />}
      </IconButton>
      {name}
    </Button>
  );
};

export default IndeterminateCheckbox;
