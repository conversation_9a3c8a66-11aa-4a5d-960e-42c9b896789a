import React, { useState } from 'react';
import {
  FieldErrorProps,
  FieldTemplateProps,
  ObjectFieldTemplateProps,
} from '@rjsf/utils';
import HelpIcon from '@mui/icons-material/Help';
import { IconButton, Tooltip } from '@mui/material';
import { ExpandLess, ExpandMore } from '@mui/icons-material';
import './InfoFieldTemplate.css';

const customBorder = {
  borderBottom: '1px solid lightgray',
};

const customLabelStyles = {
  fontSize: '20px',
  fontWeight: 'normal',
};

const customListItemStyles = {
  whiteSpace: 'nowrap',
};

const CustomFieldTemplate: React.FC<FieldTemplateProps> = ({
  id,
  classNames,
  label,
  required,
  description,
  errors,
  children,
  schema,
  rawDescription,
}) => {
  const descriptionForNestedProperties = description.props.description;
  return (
    <div
      style={schema.type === 'array' ? customBorder : null}
      className={classNames}
    >
      <label
        style={
          schema.type === 'array' || schema.type === 'object'
            ? customLabelStyles
            : null
        }
        htmlFor={id}
      >
        {label}
        {required ? <span style={{ color: 'red' }}>*</span> : null}
        {descriptionForNestedProperties !== '' ? (
          <Tooltip title={rawDescription}>
            <IconButton aria-label='info' size='small'>
              <HelpIcon fontSize='small' />
            </IconButton>
          </Tooltip>
        ) : null}
      </label>
      <span
        className={schema?.readOnly ? 'disabled' : ''}
        title={schema.readOnly ? 'Read-only field' : ''}
      >
        {children}
      </span>
      {errors}
    </div>
  );
};

const CustomObjectFieldTemplate: React.FC<ObjectFieldTemplateProps> = ({
  idSchema,
  properties,
}) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(true);
  if (!properties.length) return null;
  return (
    <>
      <span id={idSchema.$id} />
      {!isExpanded && (
        <ExpandMore
          fontSize='small'
          color='action'
          onClick={() => setIsExpanded(prev => !prev)}
        />
      )}
      {isExpanded && (
        <ExpandLess
          fontSize='small'
          color='action'
          onClick={() => setIsExpanded(prev => !prev)}
        />
      )}
      {isExpanded && (
        <div className='card-shadow'>
          {properties.map(items => (
            <div key={items.name} className='property-wrapper field-content'>
              {items.content}
            </div>
          ))}
        </div>
      )}
    </>
  );
};

const CustomErrorFieldTemplate: React.FC<FieldErrorProps> = ({ errors }) => {
  if (!errors?.length) return null;

  return (
    <div>
      <ul className='error-detail bs-callout bs-callout-info'>
        {errors.map(error => (
          <li
            key={error.toString()}
            className='text-danger'
            style={customListItemStyles}
          >
            {error}
          </li>
        ))}
      </ul>
    </div>
  );
};

export {
  CustomFieldTemplate,
  CustomObjectFieldTemplate,
  CustomErrorFieldTemplate,
};
