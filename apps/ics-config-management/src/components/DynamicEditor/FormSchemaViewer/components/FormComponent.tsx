import React, {
  memo,
  useMemo,
  useCallback,
  Ref,
  useContext,
  useRef,
  useEffect,
} from 'react';
import Form, { getDefaultRegistry } from '@rjsf/core';
import validator from '@rjsf/validator-ajv8';
import { RJSFSchema, RJSFValidationError, UiSchema } from '@rjsf/utils';
import isEqual from 'lodash/isEqual';
import { useEffectOnce } from 'usehooks-ts';
import { SchemaContext } from '../SchemaContext';
import { FieldHOC } from '../hocs';
import { UnknownObject } from '../../types';
import {
  PATTERN_ERROR_MESSAGE,
  generateCustomAttributesAndMutateSchema,
} from '../utils';
import {
  getCustomAttributesFromFormData,
  generateUISchemaForClearableCheckbox,
} from '../../utils';
import {
  CustomErrorFieldTemplate,
  CustomFieldTemplate,
  CustomObjectFieldTemplate,
} from '../InfoFieldTemplate';
import '../InfoFieldTemplate.css';
import { getFormattedErrorMessage } from '../../../../utils/helpers';

const defaultRegistry = getDefaultRegistry();

const DescriptionFieldTemplate = () => null;
const TitleFieldTemplate = () => null;

const fieldsOverrides = {
  BooleanField: FieldHOC(defaultRegistry.fields.BooleanField),
  StringField: FieldHOC(defaultRegistry.fields.StringField),
};

const uiSchemaStyle = {
  default: {
    'ui:options': {
      label: false,
    },
  },
  'ui:submitButtonOptions': {
    norender: true,
  },
};

type FormComponentProps = {
  formRef: Ref<Form<UnknownObject, RJSFSchema, UnknownObject>>;
  setPropertyNavigationRefs: (obj: Object) => void;
};

const FormComponent = ({
  formRef,
  setPropertyNavigationRefs,
}: FormComponentProps) => {
  const formComponentRef = useRef<HTMLDivElement>(null);
  const {
    setUserFormData,
    userFormData,
    newSchema,
    customAttributes,
    setNewSchema,
    setCustomAttributes,
  } = useContext(SchemaContext);

  const uiSchemaForClearableCheckbox: UiSchema = useMemo(
    () => generateUISchemaForClearableCheckbox({ schema: newSchema }),
    [newSchema]
  );

  const handleGenerateData = useCallback(() => {
    const currentCustomAttributes = getCustomAttributesFromFormData({
      formData: userFormData,
    });
    const {
      resultSchema: newSchemaWithCustomAttributes,
      finalCustomAttributes,
    } = generateCustomAttributesAndMutateSchema({
      schemaObject: newSchema,
      customAttributesObject: currentCustomAttributes,
    });

    if (!isEqual(newSchemaWithCustomAttributes, newSchema)) {
      setNewSchema(newSchemaWithCustomAttributes);
    }
    if (!isEqual(finalCustomAttributes, customAttributes)) {
      setCustomAttributes(finalCustomAttributes);
    }
  }, [
    userFormData,
    newSchema,
    setNewSchema,
    setCustomAttributes,
    customAttributes,
  ]);

  const handleOnChange = useCallback(
    data => {
      if (data?.formData) {
        setUserFormData(data.formData);
      }
    },
    [setUserFormData]
  );

  useEffectOnce(() => {
    handleGenerateData();
  });

  useEffect(() => {
    if (formComponentRef.current) {
      const refObj = {};
      const childNodes =
        formComponentRef.current.querySelector('.card-shadow').children;
      const childNodesArray = Array.from(childNodes);
      childNodesArray.forEach((node, index) => {
        refObj[index] = node;
      });
      setPropertyNavigationRefs(refObj);
    }
  }, []);

  const transformErrors = (
    errors: Array<RJSFValidationError>
  ): Array<RJSFValidationError> =>
    errors.map(error => {
      if (error.name === 'pattern') {
        const fieldName = error.property.split('.').pop();
        return {
          ...error,
          message: getFormattedErrorMessage(PATTERN_ERROR_MESSAGE, fieldName),
        };
      }
      return error;
    });

  return (
    <div ref={formComponentRef}>
      <Form
        autoComplete='off'
        fields={fieldsOverrides}
        focusOnFirstError
        formData={userFormData}
        method='dialog'
        noHtml5Validate
        omitExtraData
        onChange={handleOnChange}
        schema={newSchema}
        templates={{
          FieldTemplate: CustomFieldTemplate,
          DescriptionFieldTemplate,
          TitleFieldTemplate,
          ObjectFieldTemplate: CustomObjectFieldTemplate,
          FieldErrorTemplate: CustomErrorFieldTemplate,
        }}
        showErrorList={false}
        uiSchema={{ ...uiSchemaStyle, ...uiSchemaForClearableCheckbox }}
        validator={validator}
        ref={formRef}
        transformErrors={transformErrors}
      />
    </div>
  );
};

const MemoizedFormComponent = memo(FormComponent);

export default MemoizedFormComponent;

export { MemoizedFormComponent as FormComponent };
