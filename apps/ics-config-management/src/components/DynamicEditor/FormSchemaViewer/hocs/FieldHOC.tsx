import React, {
  useState,
  useContext,
  useEffect,
  useCallback,
  useMemo,
} from 'react';
import { Box, Tooltip, Checkbox } from '@mui/material';
import { FieldProps, Field } from '@rjsf/utils';
import get from 'lodash/get';
import cloneDeep from 'lodash/cloneDeep';
import {
  INITIAL_VALUE,
  getPathToFieldPropertiesInSchema,
  removeCustomAttribute,
  generateCustomAttributesAndMutateSchema,
} from '../utils';
import { SchemaContext } from '../SchemaContext';
import { AttributeItem, CustomAttributes } from '../../../../constants/types';

const FieldHOC = (Component: Field) => {
  const WrappedFunction = (props: FieldProps) => {
    const {
      name,
      onChange,
      idSchema: { $id: fieldId },
      readonly,
      formData,
    } = props;
    const {
      customAttributes,
      setCustomAttributes,
      originalSchema,
      setNewSchema,
    } = useContext(SchemaContext);
    const [checkboxState, setCheckboxState] = useState<boolean>(false);
    const [prevFormData, setPrevFormData] = useState(formData);

    const keyToFind = useMemo(
      () =>
        fieldId.replace('root_', 'properties.').replace(/_/g, '.properties.'),
      [fieldId]
    );

    const path = useMemo(
      () => fieldId.replace('root_', '').replace(/_/g, '.'),
      [fieldId]
    );

    const propertiesPathForField = useMemo(
      () =>
        getPathToFieldPropertiesInSchema({
          schemaObject: originalSchema,
          keyToFind,
        }) ?? '',
      [keyToFind, name, originalSchema]
    );

    const originalType = useMemo(() => {
      const finalPath = `${propertiesPathForField}.type`;
      return get(originalSchema, finalPath);
    }, [name, originalSchema]);

    useEffect(() => {
      if (!checkboxState) {
        setPrevFormData(formData);
      }
    }, [formData]);

    const handleSchemaAndCustomAttributes = useCallback(
      (newCustomAttributes: CustomAttributes) => {
        const {
          resultSchema: newSchemaWithCustomAttributes,
          finalCustomAttributes,
        } = generateCustomAttributesAndMutateSchema({
          schemaObject: originalSchema,
          customAttributesObject: newCustomAttributes,
        });
        setNewSchema(newSchemaWithCustomAttributes);
        setCustomAttributes(finalCustomAttributes);
      },
      [customAttributes, originalSchema, setNewSchema, setCustomAttributes]
    );

    const handleCheckboxChange = useCallback(
      event => {
        const isChecked = event.target?.checked ?? false;
        // Regular expression to match the pattern {{['any characters']}} but exclude empty {{[]}}
        const formInputPattern = /\{\{.*[^[]].*\}\}/;
        setCheckboxState(isChecked);
        let currentCustomAttributes: CustomAttributes =
          cloneDeep(customAttributes);
        if (isChecked) {
          const attributeValue = INITIAL_VALUE;
          onChange(attributeValue);

          const newAttribute: AttributeItem = {
            name,
            attributeValue,
            path,
          };
          currentCustomAttributes = [...currentCustomAttributes, newAttribute];
        } else {
          onChange(formInputPattern.test(formData) ? '' : prevFormData);
          currentCustomAttributes = removeCustomAttribute({
            customAttributes: currentCustomAttributes,
            name,
          });
        }
        handleSchemaAndCustomAttributes(currentCustomAttributes);
      },
      [
        // values
        customAttributes,
        name,
        originalType,
        path,
        // functions
        setCheckboxState,
        handleSchemaAndCustomAttributes,
      ]
    );

    const setInitialCustomAttributes = useCallback(() => {
      customAttributes.forEach(attribute => {
        if (
          attribute?.path === path ||
          attribute?.path === path.replace(/\.\d{1,}\./g, '.')
        ) {
          setCheckboxState(!readonly);
        }
      });
    }, [customAttributes, path]);

    useEffect(() => {
      setInitialCustomAttributes();
    }, [customAttributes, setInitialCustomAttributes]);

    return (
      <Box
        display='flex'
        flexDirection='row'
        alignContent='center'
        alignItems='center'
      >
        <Box flex={1} width='100%'>
          <Component {...props} />
        </Box>
        <Box
          flexShrink={0}
          flexGrow={0}
          sx={{
            width: '60px',
            paddingLeft: '8px',
          }}
        >
          {!readonly && (
            <Tooltip
              title={
                checkboxState
                  ? 'Use site attribute: ON'
                  : 'Use site attribute: OFF'
              }
              placement='top'
            >
              <Checkbox
                className='thisIsCheckbox'
                sx={{
                  flexShrink: 0,
                }}
                checked={checkboxState}
                onChange={handleCheckboxChange}
              />
            </Tooltip>
          )}
        </Box>
      </Box>
    );
  };
  return WrappedFunction;
};

export default FieldHOC;
export { FieldHOC };
