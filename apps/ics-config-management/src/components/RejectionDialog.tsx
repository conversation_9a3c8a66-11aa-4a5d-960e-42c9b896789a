import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Divider,
  Box,
  Tooltip,
} from '@mui/material';

interface RejectionDialogProps {
  rejectDialogOpen: boolean;
  rejectionReason: string;
  setRejectionReason: (value: string) => void;
  handleRejectCancel: () => void;
  handleRejectConfirm: () => void;
  setRejectionError?: (boolean) => void;
  rejectionError?: boolean;
}

const RejectionDialog: React.FC<RejectionDialogProps> = ({
  rejectDialogOpen,
  rejectionReason,
  setRejectionReason,
  handleRejectCancel,
  handleRejectConfirm,
  setRejectionError,
  rejectionError,
}) => (
  <Dialog
    open={rejectDialogOpen}
    onClose={handleRejectCancel}
    maxWidth='sm'
    fullWidth
    PaperProps={{
      sx: {
        borderRadius: '20px',
        boxShadow: 3,
        pb: 0,
      },
    }}
  >
    <Box sx={{ backgroundColor: '#E1E1EC' }}>
      <DialogTitle
        sx={{
          fontSize: '1.75rem',
          fontWeight: 600,
        }}
      >
        Please leave a reason for this rejection
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ pt: '16px' }}>
        <TextField
          label='Reason for Rejection'
          type='text'
          fullWidth
          multiline
          minRows={3}
          value={rejectionReason}
          onChange={e => {
            setRejectionReason(e.target.value);
            if (rejectionError && e.target.value.trim()) {
              setRejectionError(false);
            }
          }}
          variant='outlined'
          InputLabelProps={{ shrink: true }}
          onKeyDown={e => {
            e.stopPropagation();
          }}
          error={rejectionError}
          helperText={
            rejectionError ? 'Please enter a reason for rejection' : ''
          }
        />
      </DialogContent>

      <Divider />

      <DialogActions
        sx={{ px: 3, pb: 0, pt: 1, justifyContent: 'space-between' }}
      >
        <Button
          onClick={handleRejectCancel}
          sx={{
            color: '#3f51b5',
            textTransform: 'none',
            fontWeight: 500,
          }}
        >
          Cancel
        </Button>
        <Tooltip
          title={
            !rejectionReason.trim()
              ? 'Please enter a reason to enable this button'
              : ''
          }
          placement='top'
          arrow
        >
          <span
            style={{
              display: 'inline-block',
              cursor: !rejectionReason.trim() ? 'not-allowed' : 'pointer',
            }}
          >
            <Button
              onClick={handleRejectConfirm}
              variant='contained'
              disabled={!rejectionReason.trim()}
              sx={{
                textTransform: 'none',
                borderRadius: '12px',
                boxShadow: 3,
                fontWeight: 600,
                backgroundColor: '#3f51b5',
                '&:hover': {
                  backgroundColor: '#303f9f',
                },
                '&.Mui-disabled': {
                  backgroundColor: '#D3D3D3',
                  color: '#999',
                },
              }}
            >
              Submit Rejection
            </Button>
          </span>
        </Tooltip>
      </DialogActions>
    </Box>
  </Dialog>
);

export default RejectionDialog;
