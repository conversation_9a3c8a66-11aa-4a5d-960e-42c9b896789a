import React, { memo, useCallback, useState, useMemo } from 'react';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Link,
  Typography,
  MenuItem,
  Tooltip,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import type { AxiosError } from 'axios';
import {
  PlusLg,
  Trash3,
  CheckSquareFill,
  CheckCircle,
  SlashCircle,
} from 'react-bootstrap-icons';
import {
  CONFIG_LIST_PATH,
  CONFIG_INSTANCE_TAB_ROOT,
} from '../constants/routes';
import { DeleteConfigInstanceDialogContent } from '../constants/staticContent';
import {
  useGetInstanceAssignments,
  useDeleteConfigInstance,
  useGetInstanceDetails,
  usePutApprovalReject,
} from '../services/use-query';
import {
  ApprovalRejectPayload,
  InstanceDetailsResponse,
} from '../constants/types';
import { hasApprovalRequirement, hasUserRole } from '../utils/helpers';
import ReviewConfigChangesDialog from '../pages/ConfigList/ReviewConfigChangesDialog';
import useHasPermissions from '../hooks/useHasPermissions';
import UserRoles from '../constants/userRoles';
import CloneConfigInstanceDialog from './CloneConfigInstanceDialog';
import RejectionDialog from './RejectionDialog';

type DeleteConfigInstanceDialogProps = {
  instanceId: string;
  onClickAway: () => void;
  isFromConfigList?: boolean;
  handleShowAssignment?: (
    value1: string,
    value2: InstanceDetailsResponse
  ) => void;
  appDescriptorId?: number;
  configFileId?: number;
  totalRevision?: number;
  isApporvalPending?: boolean;
  approvalFlowStatus?: any;
  refetchApprovalFlowStatus?: () => Promise<any>;
};

const DeleteConfigInstanceDialog = memo(
  ({
    instanceId,
    onClickAway,
    isFromConfigList,
    handleShowAssignment,
    totalRevision,
    appDescriptorId,
    configFileId,
    isApporvalPending,
    approvalFlowStatus,
    refetchApprovalFlowStatus,
  }: DeleteConfigInstanceDialogProps) => {
    const [
      isDeleteConfigInstanceDialogOpen,
      setIsDeleteConfigInstanceDialogOpen,
    ] = useState<boolean>(false);
    const [isCloneDialogOpen, setIsCloneDialogOpen] = useState<boolean>(false);
    const [rejectionError, setRejectionError] = useState(false);
    const [isApproveChangesClicked, setIsApproveChangesClicked] =
      useState<boolean>(false);
    const [rejectionReason, setRejectionReason] = useState('');
    const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
    const queryClient = useQueryClient();
    const { enqueueSnackbar } = useSnackbar();
    const navigate = useNavigate();
    const { data: instanceDetails, isLoading: isLoadingInstanceDetails } =
      useGetInstanceDetails(instanceId);

    const { mutate: putApprovalRejectMutate } = usePutApprovalReject();

    const hasCompanyAdmin = useHasPermissions({
      userRoles: [UserRoles.COMPANY_ADMIN], // Adjust roles accordingly
    });
    const hasPowerUser = useHasPermissions({
      userRoles: [UserRoles.POWER_USER], // Adjust roles accordingly
    });
    const isApprover =
      hasUserRole(UserRoles.CONFIG_MGMT_APPROVER) ||
      hasCompanyAdmin ||
      hasPowerUser;

    const handleMenuItemClick = event => {
      event.preventDefault();
      onClickAway();
      handleShowAssignment(instanceId, instanceDetails);
    };

    const handleApprovalRequest = () => {
      setIsApproveChangesClicked(true);
    };

    const handleRejectRequest = () => {
      setRejectDialogOpen(true);
    };

    const { data: assignmentsData, isLoading: isLoadingAssignments } =
      useGetInstanceAssignments(
        { instanceId },
        {
          enabled: isDeleteConfigInstanceDialogOpen,
          onError: error => {
            enqueueSnackbar(
              (error as AxiosError)?.message ?? 'Something went wrong',
              {
                variant: 'error',
              }
            );
          },
        }
      );

    const { mutate: deleteConfigInstance, isLoading: isDeletingInstance } =
      useDeleteConfigInstance(
        { instanceId },
        {
          onSuccess: data => {
            if (data === 204) {
              enqueueSnackbar(
                DeleteConfigInstanceDialogContent.deleteSuccessMessage.replace(
                  '$instanceId',
                  instanceId
                ),
                {
                  variant: 'success',
                }
              );
              queryClient.invalidateQueries(['getInstances']);
              setIsDeleteConfigInstanceDialogOpen(false);
              onClickAway();
              navigate(CONFIG_LIST_PATH);
            }
          },
          onError: () => {
            enqueueSnackbar(
              DeleteConfigInstanceDialogContent.deleteErrorMessage,
              {
                variant: 'error',
              }
            );
          },
        }
      );

    const hasAnyAssignments: boolean = useMemo(() => {
      if (!isLoadingAssignments && assignmentsData) {
        return assignmentsData.some(({ assignments }) =>
          Boolean(assignments.length)
        );
      }
      return false;
    }, [assignmentsData, isLoadingAssignments]);

    const handleDeleteConfigInstanceDialogOpen = useCallback(() => {
      setIsDeleteConfigInstanceDialogOpen(true);
    }, [setIsDeleteConfigInstanceDialogOpen, onClickAway]);

    const handleDeleteConfigInstanceDialogClose = useCallback(() => {
      setIsDeleteConfigInstanceDialogOpen(false);
      onClickAway();
    }, [setIsDeleteConfigInstanceDialogOpen]);

    const handleDeleteConfigInstance = useCallback(() => {
      if (!hasAnyAssignments) {
        deleteConfigInstance({
          instanceId,
        });
      }
    }, [instanceId, deleteConfigInstance, hasAnyAssignments]);

    const handleCloneClick = useCallback(() => {
      setIsCloneDialogOpen(true);
    }, []);

    const handleRejectCancel = () => {
      onClickAway();
      setRejectDialogOpen(false);
      setRejectionReason('');
    };

    const handleRejectConfirm = () => {
      onClickAway();
      if (!rejectionReason.trim()) {
        setRejectionError(true);
        return;
      }

      setRejectionError(false);
      const matchingApproval = approvalFlowStatus.find(
        item => item?.condition?.configInstanceId === Number(instanceId)
      );

      const rejectedPayload: ApprovalRejectPayload[] = [
        {
          id: matchingApproval?.id,
          status: 'rejected',
          comment: rejectionReason.trim(),
          additionalData: {},
        },
      ];

      putApprovalRejectMutate(rejectedPayload, {
        onSuccess: () => {
          enqueueSnackbar(
            'Configuration request has been rejected. Submitter will be notified of its status.',
            { variant: 'success' }
          );
          setRejectDialogOpen(false);
          setRejectionReason('');
          refetchApprovalFlowStatus();
        },
        onError: (error: any) => {
          enqueueSnackbar(
            `Rejection failed: ${error.message || 'Unknown error'}`,
            { variant: 'error' }
          );
        },
      });
    };

    const handleCloseCloneDialog = useCallback(() => {
      setIsCloneDialogOpen(false);
      onClickAway();
    }, [onClickAway]);

    return (
      <>
        {isFromConfigList ? (
          <>
            <MenuItem
              data-testid='assign-config-instance-menu-item'
              disabled={!instanceDetails}
              onClick={handleMenuItemClick}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
              }}
            >
              <PlusLg
                style={{
                  fontSize: '15px',
                  verticalAlign: 'middle',
                }}
              />
              <span
                style={{
                  fontSize: '16px',
                  lineHeight: '0.2',
                }}
              >
                Assign
              </span>
            </MenuItem>
            <Tooltip
              title='Clone operation is not allowed because of zero revisions.'
              arrow
              disableHoverListener={!(totalRevision === 0)}
              placement='left'
            >
              <span>
                <MenuItem
                  data-testid='clone-config-instance-menu-item'
                  disabled={
                    totalRevision === 0 ||
                    !instanceDetails ||
                    isLoadingInstanceDetails
                  }
                  onClick={handleCloneClick}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                  }}
                >
                  <CheckSquareFill
                    style={{
                      fontSize: '15px',
                      verticalAlign: 'middle',
                    }}
                  />
                  <span
                    style={{
                      fontSize: '16px',
                      lineHeight: '0.2',
                    }}
                  >
                    Clone
                  </span>
                </MenuItem>
              </span>
            </Tooltip>
            {isApporvalPending && isApprover && (
              <>
                <MenuItem
                  data-testid='approve-config-instance-menu-item'
                  disabled={
                    !hasApprovalRequirement(instanceDetails?.configFile)
                  }
                  onClick={handleApprovalRequest}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                  }}
                >
                  <CheckCircle
                    style={{
                      fontSize: '15px',
                      verticalAlign: 'middle',
                    }}
                  />
                  <span
                    style={{
                      fontSize: '16px',
                      lineHeight: '0.2',
                    }}
                  >
                    Approve
                  </span>
                </MenuItem>
                <MenuItem
                  data-testid='reject-config-instance-menu-item'
                  disabled={
                    !hasApprovalRequirement(instanceDetails?.configFile)
                  }
                  onClick={handleRejectRequest}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                  }}
                >
                  <SlashCircle
                    style={{
                      fontSize: '15px',
                      verticalAlign: 'middle',
                    }}
                  />
                  <span
                    style={{
                      fontSize: '16px',
                      lineHeight: '0.2',
                    }}
                  >
                    Reject
                  </span>
                </MenuItem>
              </>
            )}

            <MenuItem
              data-testid='delete-config-instance-menu-item'
              onClick={handleDeleteConfigInstanceDialogOpen}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
              }}
            >
              <Trash3
                style={{
                  fontSize: '15px',
                  verticalAlign: 'middle',
                }}
              />
              <span
                style={{
                  fontSize: '16px',
                  lineHeight: '0.2',
                }}
              >
                {DeleteConfigInstanceDialogContent.callToAction}
              </span>
            </MenuItem>
          </>
        ) : (
          <MenuItem
            data-testid='delete-config-instance-menu-item'
            onClick={handleDeleteConfigInstanceDialogOpen}
          >
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
              }}
            >
              <Trash3
                style={{
                  fontSize: '15px',
                  verticalAlign: 'middle',
                }}
              />
              <span
                style={{
                  fontSize: '16px',
                  lineHeight: '0.2',
                }}
              >
                Delete Config Instance
              </span>
            </div>
          </MenuItem>
        )}
        <Dialog
          open={isDeleteConfigInstanceDialogOpen}
          onClose={handleDeleteConfigInstanceDialogClose}
          PaperProps={{
            sx: { borderRadius: 8, width: 400 },
          }}
        >
          <DialogContent sx={{ bgcolor: 'common.lightSurface' }}>
            <Typography variant='headerlineSmall'>
              {hasAnyAssignments
                ? DeleteConfigInstanceDialogContent.dialogTitleWarning
                : DeleteConfigInstanceDialogContent.dialogTitle}
            </Typography>
            <DialogContentText sx={{ fontSize: 14 }}>
              {hasAnyAssignments ? (
                <>
                  {DeleteConfigInstanceDialogContent.dialogContentWarningStart}
                  <Link
                    href={`${CONFIG_INSTANCE_TAB_ROOT}/${instanceId}/assignment`}
                    onClick={handleDeleteConfigInstanceDialogClose}
                  >
                    {DeleteConfigInstanceDialogContent.assignmentTabLink}
                  </Link>
                  {DeleteConfigInstanceDialogContent.dialogContentWarningEnd}
                </>
              ) : (
                DeleteConfigInstanceDialogContent.dialogContent
              )}
            </DialogContentText>
          </DialogContent>
          <DialogActions sx={{ bgcolor: 'common.lightSurface' }}>
            <Button
              onClick={handleDeleteConfigInstanceDialogClose}
              variant='grey'
            >
              {DeleteConfigInstanceDialogContent.cancel}
            </Button>
            <LoadingButton
              disabled={
                hasAnyAssignments || isLoadingAssignments || isDeletingInstance
              }
              loading={isLoadingAssignments || isDeletingInstance}
              onClick={handleDeleteConfigInstance}
              variant='grey'
            >
              {DeleteConfigInstanceDialogContent.delete}
            </LoadingButton>
          </DialogActions>
        </Dialog>
        {instanceDetails?.instanceName && (
          <CloneConfigInstanceDialog
            instanceId={instanceId}
            open={isCloneDialogOpen}
            onClose={handleCloseCloneDialog}
            appDescriptorId={appDescriptorId}
            configFileId={configFileId}
            draftContent={instanceDetails?.draftContent}
            instanceName={instanceDetails?.instanceName}
          />
        )}
        {isApproveChangesClicked && (
          <ReviewConfigChangesDialog
            open={isApproveChangesClicked}
            setIsApproveChangesClicked={setIsApproveChangesClicked}
            instanceId={instanceId}
            onClickAway={onClickAway}
          />
        )}
        <RejectionDialog
          rejectDialogOpen={rejectDialogOpen}
          rejectionReason={rejectionReason}
          setRejectionReason={setRejectionReason}
          handleRejectCancel={handleRejectCancel}
          handleRejectConfirm={handleRejectConfirm}
          setRejectionError={setRejectionError}
          rejectionError={rejectionError}
        />
      </>
    );
  },
  (prevProps, nextProps) => prevProps.instanceId === nextProps.instanceId
);

export default DeleteConfigInstanceDialog;
