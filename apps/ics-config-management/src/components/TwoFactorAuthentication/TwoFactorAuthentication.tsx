import React, { useEffect, useRef, useState } from 'react';
import {
  Text<PERSON><PERSON>,
  Button,
  Typography,
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material';
import PhoneAndroidIcon from '@mui/icons-material/PhoneAndroid';
import { useSnackbar } from 'notistack';

const TwoFactorVerification = ({ onClose, onSubmit, open, mfaError }) => {
  const [mfaCode, setMfaCode] = useState('');
  const [mfaAttempts, setMfaAttempts] = useState(0);
  const [maxMfaAttempts] = useState(3);
  const [mfaAttemptsExceeded, setMfaAttemptsExceeded] = useState(false);
  const { enqueueSnackbar } = useSnackbar();
  const inputRef = useRef(null);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (open) {
      // Reset attempts when dialog opens
      setMfaAttempts(0);
      setMfaAttemptsExceeded(false);
      setMfaCode('');
      timer = setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [open]);

  // Handle MFA validation errors from parent
  useEffect(() => {
    if (mfaError) {
      console.log('MFA error triggered in TwoFactorAuthentication component', { mfaAttempts, maxMfaAttempts });
      const newAttempts = mfaAttempts + 1;
      setMfaAttempts(newAttempts);

      if (newAttempts >= maxMfaAttempts) {
        setMfaAttemptsExceeded(true);
        console.log('Maximum MFA attempts exceeded');
        enqueueSnackbar(`Maximum MFA attempts (${maxMfaAttempts}) exceeded. Please try again later.`, {
          autoHideDuration: 5_000,
          variant: 'error',
        });
        // Close the dialog after max attempts
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        console.log(`MFA attempt ${newAttempts}/${maxMfaAttempts} failed`);
        enqueueSnackbar(`MFA code is invalid. ${maxMfaAttempts - newAttempts} attempts remaining.`, {
          autoHideDuration: 5_000,
          variant: 'error',
        });
        // Clear the input field and refocus
        setMfaCode('');
        setTimeout(() => {
          inputRef.current?.focus();
        }, 100);
      }
    }
  }, [mfaError, mfaAttempts, maxMfaAttempts, enqueueSnackbar, onClose]);

  const handleSubmit = () => {
    if (mfaAttemptsExceeded) {
      return;
    }

    if (mfaCode.trim().length === 6) {
      onSubmit(mfaCode);
      setMfaCode('');
      onClose();
    } else if (mfaCode.trim().length !== 6) {
      enqueueSnackbar('MFA code is invalid', {
        autoHideDuration: 5_000,
        variant: 'error',
      });
      setMfaCode('');
    }
  };

  const handleCancel = () => {
    setMfaCode('');
    setMfaAttempts(0);
    setMfaAttemptsExceeded(false);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='xs'
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: '#E1E1ec',
          borderRadius: 5,
        },
      }}
    >
      <DialogTitle>Two-factor Verification</DialogTitle>
      <DialogContent>
        <Typography variant='body2' gutterBottom>
          Re-enter your MFA code to create the download for selected devices.
        </Typography>
        {mfaAttempts > 0 && !mfaAttemptsExceeded && (
          <Typography variant='body2' color='warning.main' gutterBottom>
            {maxMfaAttempts - mfaAttempts} attempts remaining
          </Typography>
        )}
        {mfaAttemptsExceeded && (
          <Typography variant='body2' color='error.main' gutterBottom>
            Maximum attempts exceeded. Please try again later.
          </Typography>
        )}
        <TextField
          fullWidth
          variant='outlined'
          margin='dense'
          label='MFA Code'
          size='small'
          value={mfaCode}
          onChange={e => setMfaCode(e.target.value)}
          inputProps={{ maxLength: 6 }}
          autoComplete='off'
          inputRef={inputRef}
          disabled={mfaAttemptsExceeded}
          error={mfaAttempts > 0}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCancel} color='primary'>
          Cancel
        </Button>
        <Button
          variant='contained'
          color='primary'
          onClick={handleSubmit}
          disabled={mfaAttemptsExceeded || mfaCode.trim().length !== 6}
        >
          Confirm
        </Button>
      </DialogActions>
      <Box display='flex' alignItems='center' sx={{ padding: 2 }}>
        <PhoneAndroidIcon sx={{ marginRight: 1 }} />
        <Typography variant='body2'>
          Open the two-factor authentication app on your device to view your
          authentication code and verify your identity.
        </Typography>
      </Box>
    </Dialog>
  );
};

export default TwoFactorVerification;
