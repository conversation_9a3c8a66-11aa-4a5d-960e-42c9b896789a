import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Button, Box, Dialog, Typography, Divider } from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { WarningAmber } from '@mui/icons-material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { RevisionPublishDeploymentType } from '../../constants/types';
import { getRevisionTitle, getFormattedDescription } from '../../utils/helpers';
import { useGetMaintenanceWindow } from '../../services/use-query';
import TwoFactorVerification from '../TwoFactorAuthentication/TwoFactorAuthentication';
import {
  RevisionDeploymentTypeDialogProps,
  RevisionDeploymentTypeDialogStep,
  RevisionDeploymentTypeDialogState,
  TimeAndDateFormatInScheduleWindowType,
} from './types';
import {
  RevisionDeploymentTypeDialogText,
  RevisionDeploymentTypeDialogContent,
} from './constants';

const RevisionDeploymentTypeDialog = ({
  closeDialog,
  content,
  customAttributes,
  initialStep,
  isDialogOpen,
  isDisabled,
  isLoading,
  onSubmit,
  openDialog,
  revert = false,
  assignmentEmpty,
  revisionName,
  instanceDetails,
  isMfaAccess,
  hasDataChanged,
  isSaveClicked,
  isCloseClicked,
  setIsCloseClicked,
  buttonText,
}: RevisionDeploymentTypeDialogProps) => {
  const [confirmDialogState, setConfirmDialogState] =
    useState<RevisionDeploymentTypeDialogState>({
      step: initialStep,
    });
  const { step } = confirmDialogState;

  const { title, description, primaryAction, secondaryAction } =
    RevisionDeploymentTypeDialogText[step];

  const { data: NextMaintenanceIntimationWindow } = useGetMaintenanceWindow(
    isDialogOpen &&
      step === RevisionDeploymentTypeDialogStep.confirmationMaintainance
  );

  const revisionDescription = getRevisionTitle(description, revisionName);

  const [isTwoFactorOpen, setTwoFactorOpen] = useState(false);
  const [mfaCode, setMfaCode] = useState('');

  const handleCloseDialog = useCallback(() => {
    const newStep =
      revert && !assignmentEmpty
        ? RevisionDeploymentTypeDialogStep.revertDialog
        : initialStep;
    setConfirmDialogState(prevState => ({
      ...prevState,
      step: newStep,
    }));
    closeDialog();
    setIsCloseClicked(true);
    if (!hasDataChanged) {
      setIsCloseClicked(false);
    }
  }, [
    revert,
    assignmentEmpty,
    initialStep,
    closeDialog,
    confirmDialogState.step,
  ]);

  const handleCloseDialogWithSelectedDeploymentType = useCallback(
    (deploymentType: RevisionPublishDeploymentType, code?: string) => {
      if (code || RevisionDeploymentTypeDialogStep.initialNoAssignments) {
        onSubmit({
          content,
          deploymentType,
          propertiesUsingAttributes: customAttributes,
          mfaCode: code,
        });
        handleCloseDialog();
      }
    },
    [content, customAttributes, onSubmit, handleCloseDialog]
  );

  const handleMfa = useCallback(
    (deploymentType: RevisionPublishDeploymentType, currentCode?: string) => {
      // If MFA feature flag is present
      if (isMfaAccess) {
        if (!currentCode) {
          setMfaCode('');
          setTwoFactorOpen(true);
        } else if (
          currentCode &&
          NextMaintenanceIntimationWindow?.isNext &&
          deploymentType === 'maintenance-window'
        ) {
          if (step === RevisionDeploymentTypeDialogStep.scheduleWindow) {
            handleCloseDialogWithSelectedDeploymentType(
              deploymentType,
              currentCode
            );
            setMfaCode('');
          } else {
            setConfirmDialogState(prevState => ({
              ...prevState,
              step: RevisionDeploymentTypeDialogStep.scheduleWindow,
            }));
          }
        } else {
          console.log('handleMfa', deploymentType, currentCode);
          handleCloseDialogWithSelectedDeploymentType(
            deploymentType,
            currentCode
          );
          setMfaCode('');
        }
      } else if (
        deploymentType !== 'immediate' &&
        NextMaintenanceIntimationWindow?.isNext
      ) {
        if (step === RevisionDeploymentTypeDialogStep.scheduleWindow) {
          handleCloseDialogWithSelectedDeploymentType(deploymentType);
        } else {
          setConfirmDialogState(prevState => ({
            ...prevState,
            step: RevisionDeploymentTypeDialogStep.scheduleWindow,
          }));
        }
      } else {
        handleCloseDialogWithSelectedDeploymentType(deploymentType);
      }
    },
    [isMfaAccess, step, handleCloseDialogWithSelectedDeploymentType]
  );

  const handlePrimaryAction = useCallback(
    (code?: string) => {
      const currentCode = code || mfaCode;
      console.log(step, 'STEP', currentCode, 'CODE' );
      switch (step) {
        case RevisionDeploymentTypeDialogStep.initial:
          setConfirmDialogState(
            (prevState: RevisionDeploymentTypeDialogState) => ({
              ...prevState,
              step: RevisionDeploymentTypeDialogStep.confirmation,
            })
          );
          break;
        case RevisionDeploymentTypeDialogStep.revertDialog:
          setConfirmDialogState(
            (prevState: RevisionDeploymentTypeDialogState) => ({
              ...prevState,
              step: RevisionDeploymentTypeDialogStep.confirmation,
            })
          );
          break;
        case RevisionDeploymentTypeDialogStep.initialNoAssignments:
          handleCloseDialogWithSelectedDeploymentType(
            RevisionPublishDeploymentType.maintenanceWindow
          );
          break;
        case RevisionDeploymentTypeDialogStep.confirmation:
          setConfirmDialogState(
            (prevState: RevisionDeploymentTypeDialogState) => ({
              ...prevState,
              step: RevisionDeploymentTypeDialogStep.confirmationImmediate,
            })
          );
          break;
        case RevisionDeploymentTypeDialogStep.confirmationImmediate:
          handleMfa(RevisionPublishDeploymentType.immediate, currentCode);
          break;
        case RevisionDeploymentTypeDialogStep.confirmationMaintainance:
          handleMfa(
            RevisionPublishDeploymentType.maintenanceWindow,
            currentCode
          );
          break;
        case RevisionDeploymentTypeDialogStep.scheduleWindow:
          handleMfa(
            RevisionPublishDeploymentType.maintenanceWindow,
            currentCode
          );
          break;
        default:
          break;
      }
    },
    [content, customAttributes, initialStep, step, mfaCode]
  );

  const handleSecondaryAction = useCallback(() => {
    switch (step) {
      case RevisionDeploymentTypeDialogStep.confirmation:
        setConfirmDialogState(
          (prevState: RevisionDeploymentTypeDialogState) => ({
            ...prevState,
            step: RevisionDeploymentTypeDialogStep.confirmationMaintainance,
          })
        );
        break;
      case RevisionDeploymentTypeDialogStep.scheduleWindow:
        setMfaCode('');
        break;
      default:
        break;
    }
  }, [content, customAttributes, initialStep, step]);

  const timeAndDateFormatInScheduleWindow: TimeAndDateFormatInScheduleWindowType =
    (from, to) => {
      const formatTime = (date: string | Date | undefined) =>
        new Date(date as string)
          .toLocaleString('en-GB', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
            timeZone: 'GMT',
          })
          .replace(' ', '');

      // Helper function to format date
      const formatDate = (date: string | Date | undefined) =>
        new Date(date as string).toLocaleDateString('en-GB', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          timeZone: 'GMT',
        });

      return `Est. ${formatTime(from)} - ${formatTime(to)} GMT ${formatDate(
        from
      )}`;
    };

  const formattedDescription = getFormattedDescription(
    description,
    instanceDetails
  );
  const handleCancelTwoFactor = () => {
    setTwoFactorOpen(false);
  };

  const isPublishDisabled = useMemo(
    () =>
      isCloseClicked || isSaveClicked
        ? false
        : isLoading || isDisabled || hasDataChanged,
    [isLoading, isCloseClicked, isDisabled, hasDataChanged, isSaveClicked]
  );

  const dialogStyles =
    step === RevisionDeploymentTypeDialogStep.scheduleWindow
      ? {
          borderRadius: '16px',
          width: '624px',
          height: '190px',
          padding: 3,
          display: 'flex',
          justifyContent: 'space-between',
          backgroundColor: 'common.modalBackground',
          boxSizing: 'unset',
          overflow: 'hidden',
        }
      : {
          borderRadius: '20px',
          width: 500,
          padding: 3,
          display: 'flex',
          justifyContent: 'space-between',
          backgroundColor: 'common.modalBackground',
          boxSizing: 'unset',
        };

  const approveButtonStyle = {
    minWidth: '100px',
    m: 0,
    fontSize: '11px',
    color: '#3f51b5',
    border: '1px solid #00394D',
    backgroundColor: 'transparent',
    borderRadius: 10,
  };
  useEffect(() => {
    setConfirmDialogState(prevState => ({
      ...prevState,
      step:
        revert && !assignmentEmpty
          ? RevisionDeploymentTypeDialogStep.revertDialog
          : initialStep,
    }));
  }, [revert, initialStep, assignmentEmpty]);

  return (
    <>
      {!isTwoFactorOpen && (
        <Dialog
          open={isDialogOpen}
          PaperProps={{
            sx: dialogStyles,
          }}
        >
          <Box
            display='flex'
            flexDirection='column'
            gap={
              step === RevisionDeploymentTypeDialogStep.scheduleWindow ? 1 : 2
            }
          >
            <Box
              display='flex'
              flexDirection='column'
              gap={
                step === RevisionDeploymentTypeDialogStep.scheduleWindow ? 1 : 2
              }
            >
              <Box display='flex' alignItems='center' gap={1}>
                <WarningAmber sx={{ color: 'orange', fontSize: '24px' }} />
                <Typography
                  variant='h6'
                  sx={
                    step === RevisionDeploymentTypeDialogStep.scheduleWindow
                      ? {
                          fontFamily: 'Roboto',
                          fontWeight: 500,
                          fontSize: '15px',
                          lineHeight: '24px',
                          letterSpacing: '0.1px',
                        }
                      : {}
                  }
                >
                  {title}
                </Typography>
              </Box>
              {step === RevisionDeploymentTypeDialogStep.scheduleWindow && (
                <Divider />
              )}
              <Typography
                variant='bodyMedium'
                sx={
                  step === RevisionDeploymentTypeDialogStep.scheduleWindow
                    ? {
                        fontFamily: 'Roboto',
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '20px',
                        letterSpacing: '0.25px',
                        color: '#5D5D67',
                      }
                    : {}
                }
              >
                {step === RevisionDeploymentTypeDialogStep.revertDialog
                  ? revisionDescription
                  : formattedDescription}
                {step === RevisionDeploymentTypeDialogStep.scheduleWindow &&
                  NextMaintenanceIntimationWindow && (
                    <Typography
                      sx={{
                        fontSize: '14px',
                        lineHeight: '20px',
                        paddingTop: '20px',
                      }}
                    >
                      {timeAndDateFormatInScheduleWindow(
                        NextMaintenanceIntimationWindow?.from,
                        NextMaintenanceIntimationWindow?.to
                      )}
                    </Typography>
                  )}
              </Typography>
              {step === RevisionDeploymentTypeDialogStep.scheduleWindow && (
                <Divider />
              )}
            </Box>

            <Box
              display='flex'
              justifyContent='space-between'
              alignItems='center'
              mt='auto'
            >
              {step !== RevisionDeploymentTypeDialogStep.scheduleWindow && (
                <Button onClick={handleCloseDialog}>
                  {RevisionDeploymentTypeDialogContent.closeDialogButton}
                </Button>
              )}
              <Box display='flex' gap={2} ml='auto'>
                {step === RevisionDeploymentTypeDialogStep.scheduleWindow ? (
                  <>
                    <Button onClick={handleCloseDialog}>
                      {RevisionDeploymentTypeDialogContent.cancelDialogButton}
                    </Button>
                    <Button
                      variant='contained'
                      onClick={() => handlePrimaryAction(mfaCode)}
                    >
                      {primaryAction}
                    </Button>
                  </>
                ) : (
                  secondaryAction && (
                    <Button
                      onClick={handleSecondaryAction}
                      variant='outlined'
                      sx={{
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {secondaryAction}
                    </Button>
                  )
                )}
                {step !== RevisionDeploymentTypeDialogStep.scheduleWindow && (
                  <Button
                    variant='contained'
                    onClick={() => handlePrimaryAction(mfaCode)}
                    sx={{
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {primaryAction}
                  </Button>
                )}
              </Box>
            </Box>
          </Box>
        </Dialog>
      )}
      <TwoFactorVerification
        open={isTwoFactorOpen && isMfaAccess}
        onClose={handleCancelTwoFactor}
        onSubmit={code => {
          setTwoFactorOpen(false);
          setMfaCode(code);
          handlePrimaryAction(code);
        }}
      />
      {!revert && (
        <LoadingButton
          disabled={isPublishDisabled}
          loading={isLoading}
          onClick={openDialog}
          sx={
            buttonText === 'Approve'
              ? approveButtonStyle
              : { minWidth: '100px' }
          }
          endIcon={
            buttonText === 'Approve' ? <CheckCircleIcon fontSize='small' /> : ''
          }
          variant='contained'
        >
          {buttonText || RevisionDeploymentTypeDialogContent.openDialogButton}
        </LoadingButton>
      )}
    </>
  );
};
export default RevisionDeploymentTypeDialog;
