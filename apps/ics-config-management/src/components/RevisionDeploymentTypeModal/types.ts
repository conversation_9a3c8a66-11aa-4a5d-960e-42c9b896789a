import {
  RevisionPublishDeploymentType,
  CustomAttributes,
  InstanceDetailsResponse,
} from '../../constants/types';

export enum RevisionDeploymentTypeDialogStep {
  initial,
  revertDialog,
  initialNoAssignments,
  confirmation,
  confirmationImmediate,
  confirmationMaintainance,
  scheduleWindow,
}

export type RevisionDeploymentTypeDialogResult = {
  deploymentType: RevisionPublishDeploymentType;
  propertiesUsingAttributes: CustomAttributes;
  content: string;
  mfaCode?: string;
};

export type RevisionDeploymentTypeDialogProps = {
  closeDialog: () => void;
  content: string;
  customAttributes: CustomAttributes;
  initialStep: RevisionDeploymentTypeDialogStep;
  isDialogOpen: boolean;
  isDisabled: boolean;
  isLoading: boolean;
  onSubmit: (result: RevisionDeploymentTypeDialogResult) => void;
  openDialog: () => void;
  revert?: boolean;
  revisionName?: string;
  instanceId: string;
  instanceDetails: InstanceDetailsResponse;
  assignmentEmpty: boolean;
  isMfaAccess: boolean;
  hasDataChanged: boolean;
  isSaveClicked: boolean;
  isCloseClicked: boolean;
  setIsCloseClicked: (value: boolean) => void;
  buttonText?: string;
  mfaErrorTrigger?: number;
};

export type RevisionDeploymentTypeDialogState = {
  step: RevisionDeploymentTypeDialogStep;
};

export type TimeAndDateFormatInScheduleWindowType = (
  from: string | Date | undefined,
  to: string | Date | undefined
) => string;
