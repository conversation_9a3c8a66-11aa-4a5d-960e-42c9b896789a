import React, { useCallback, useState } from 'react';
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Typography,
  Button,
  TextField,
  Box,
  Tooltip,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { useSnackbar } from 'notistack';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { usePutUpdateInstance } from '../services/use-query';
import { postCreateInstance } from '../services/api-request';

type CloneConfigInstanceDialogProps = {
  open: boolean;
  onClose: () => void;
  instanceId?: string;
  appDescriptorId?: number;
  configFileId?: number;
  draftContent?: string;
  instanceName?: string;
};

const CloneConfigInstanceDialog = ({
  instanceId,
  open,
  onClose,
  appDescriptorId,
  configFileId,
  draftContent,
  instanceName: originalInstanceName,
}: CloneConfigInstanceDialogProps) => {
  const { enqueueSnackbar } = useSnackbar();
  const putUpdateInstance = usePutUpdateInstance(instanceId);
  const [instanceName, setInstanceName] = useState(
    `Clone of ${originalInstanceName || ''}`
  );

  const [loading, setLoading] = useState(false);
  const [errorText, setErrorText] = useState('');
  const [isFocused, setIsFocused] = useState(false);

  const handleClone = useCallback(
    async event => {
      event.preventDefault();
      setLoading(true);
      setErrorText('');
      try {
        const newInstance = await postCreateInstance({
          configFileId: configFileId?.toString(),
          appDescriptorId: appDescriptorId?.toString(),
          instanceName,
        });
        if (!newInstance?.$id) {
          setErrorText(
            'Failed to create instance. Instance may already exist.'
          );
          return;
        }
        const payload = {
          draftContent,
          propertiesUsingAttributes: [],
        };

        await new Promise<void>((resolve, reject) => {
          putUpdateInstance.mutate(
            {
              instanceId: newInstance.$id,
              payload,
            },
            {
              onError(error) {
                setErrorText(`Update Error: ${error.toString()}`);
                reject(error);
              },
              onSuccess() {
                resolve();
              },
            }
          );
        });
        enqueueSnackbar(
          'Clone of configuration instance successfully created.',
          { variant: 'success' }
        );
        onClose();
        window.location.reload();
      } catch (error) {
        setErrorText(`${error.message}`);
      } finally {
        setLoading(false);
      }
    },
    [
      configFileId,
      appDescriptorId,
      instanceName,
      draftContent,
      putUpdateInstance,
      enqueueSnackbar,
      onClose,
    ]
  );

  const handleInstanceNameChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    event.preventDefault();
    const { value } = event.target;
    if (value?.length < 60) {
      setInstanceName(value);
    } else {
      setInstanceName(value.substring(0, 60));
    }
    if (errorText) setErrorText('');
  };

  const getColor = () => {
    if (errorText) return 'error.main';
    if (isFocused) return 'primary.main';
    return 'grey.500';
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{ sx: { borderRadius: 4, width: 550 } }}
    >
      <DialogContent>
        <Typography variant='h6' sx={{ pb: 2 }}>
          Clone configuration instance
        </Typography>
        <DialogContentText sx={{ fontSize: 14, pb: 3 }}>
          A cloned copy will retain the configuration file and its values, but
          will not include any assignments or revision
        </DialogContentText>
        <TextField
          label={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              Configuration Instance Name
              <InfoOutlinedIcon
                sx={{
                  marginX: '4px',
                  color: getColor(),
                  fontSize: 'large',
                }}
              />
            </Box>
          }
          variant='outlined'
          fullWidth
          sx={{ pb: 2 }}
          value={instanceName}
          error={!!errorText}
          helperText={errorText}
          onKeyDown={e => {
            e.stopPropagation();
          }}
          onChange={handleInstanceNameChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          autoComplete='off'
        />
      </DialogContent>
      <DialogActions sx={{ pr: 3, pb: 2 }}>
        <Button onClick={onClose} variant='outlined'>
          Cancel
        </Button>
        <Tooltip
          title='Please enter a Instance name'
          arrow
          disableHoverListener={!(instanceName === '')}
          placement='right'
        >
          <span
            style={{
              cursor: instanceName === '' ? 'not-allowed' : 'pointer',
            }}
          >
            <LoadingButton
              disabled={instanceName === ''}
              onClick={handleClone}
              variant='contained'
              color='primary'
              loading={loading}
            >
              Clone Instance
            </LoadingButton>
          </span>
        </Tooltip>
      </DialogActions>
    </Dialog>
  );
};

export default CloneConfigInstanceDialog;
