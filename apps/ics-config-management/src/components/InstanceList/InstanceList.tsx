import React, { memo, useEffect, useState } from 'react';
import { Grid, Box } from '@mui/material';
import isEqual from 'lodash/isEqual';

import { EntityPending, Instance } from '../../constants/types';
import lazyWithPreload from '../../utils/lazyWithPreload';
import InstanceItem from './InstanceItem';

const Assignment = lazyWithPreload(
  () => import('../../pages/ConfigInstanceTabs/Assignment')
);

Assignment.preload();

interface InstanceListProps {
  instances: Instance[];
  fetchCsv?: (instanceId: string, name: string) => void;
  approvalFlowStatus?: EntityPending[];
  refetchApprovalFlowStatus?: () => Promise<any>;
  isApprovalFlowDataLoading?: boolean;
}

const InstanceList = memo(
  ({
    instances,
    fetchCsv,
    approvalFlowStatus,
    refetchApprovalFlowStatus,
    isApprovalFlowDataLoading,
  }: InstanceListProps) => {
    const [instanceDetails, setInstanceDetails] = useState(null);
    const [id, setId] = useState('');
    const [showDialog, setShowDialog] = useState(false);

    const handleShowAssignment = (instanceId, configInstanceDetails) => {
      setId(instanceId);
      setInstanceDetails(configInstanceDetails);
    };

    useEffect(() => {
      if (id && instanceDetails) {
        setShowDialog(true);
      }
    }, [id, instanceDetails]);

    return (
      <>
        <Box
          p={2}
          bgcolor='common.backgroundLight'
          display='flex'
          minHeight={0}
          height={1}
          width={1}
          data-testid='InstanceList'
          overflow='auto'
        >
          <Grid
            display='flex'
            flexDirection='column'
            gap={1}
            flexGrow={1}
            flexShrink={1}
            minHeight={0}
          >
            {instances?.map(instance => (
              <InstanceItem
                key={instance.$id}
                instance={instance}
                fetchCsv={fetchCsv}
                handleShowAssignment={handleShowAssignment}
                approvalFlowStatus={approvalFlowStatus}
                refetchApprovalFlowStatus={refetchApprovalFlowStatus}
                isApprovalFlowDataLoading={isApprovalFlowDataLoading}
              />
            ))}
          </Grid>
        </Box>
        {showDialog ? (
          <Assignment
            id={id}
            instanceDetails={instanceDetails}
            isVisible={showDialog}
            onClose={() => setShowDialog(false)}
            handleShowAssignment={handleShowAssignment}
          />
        ) : null}
      </>
    );
  },
  (prevProps, nextProps) =>
    prevProps.instances === nextProps.instances &&
    prevProps.fetchCsv === nextProps.fetchCsv &&
    isEqual(prevProps.approvalFlowStatus, nextProps.approvalFlowStatus) &&
    prevProps.refetchApprovalFlowStatus ===
      nextProps.refetchApprovalFlowStatus &&
    prevProps.isApprovalFlowDataLoading === nextProps.isApprovalFlowDataLoading
);

export default InstanceList;
