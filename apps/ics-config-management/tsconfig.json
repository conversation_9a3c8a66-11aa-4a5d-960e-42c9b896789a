{"compilerOptions": {"target": "es2021", "outDir": "dist", "jsx": "react", "types": ["@testing-library/jest-dom", "node"], "lib": ["dom", "es2022"], "module": "esnext", "allowSyntheticDefaultImports": true, "moduleResolution": "node", "resolveJsonModule": true, "declarationDir": "./types", "declaration": true, "allowJs": true, "downlevelIteration": true, "esModuleInterop": true, "incremental": true, "noEmit": true, "noFallthroughCasesInSwitch": true}, "include": ["src/**/*", "node_modules/@types,", "__test__/**/*.test*", "types/*.d.ts"], "exclude": ["webpack.config.ts"]}