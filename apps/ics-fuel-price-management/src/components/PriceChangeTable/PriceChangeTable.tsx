// pricechangeTable

import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import {
  Box,
  Button,
  IconButton,
  CircularProgress,
  Typography,
  Pagination,
  Checkbox,
  FormControlLabel,
  Badge,
  Dialog,
  Snackbar,
  Alert,
} from '@mui/material';
import LoadingButton from '@mui/lab/LoadingButton';
import {
  RefreshTwoTone,
  CloudDownloadTwoTone,
  FilterList,
  Settings,
  CloudUpload,
} from '@mui/icons-material';
import { cloneDeep, isEmpty } from 'lodash';
import { useSearchParams } from 'react-router-dom';
import { useNavigate } from 'react-router';
import CloseIcon from '@mui/icons-material/Close';
import { theme } from '../../theme';
import { useGetCurrentPrice } from '../../services/useGetCurrentPrice';
import { useGetExportCurrentPrice } from '../../services/useGetExportCurrentPrice';
import {
  PRICE_LIMIT,
  REGEX_FOUR_DIGIT_WITH_DECIMAL,
  defaultPageIndex,
  defaultPageSize,
} from '../../utils/constants';
import useHasPermissions from '../../hooks/useHasPermissions';
import { USER_ROLES } from '../../types';
import ConfirmDialog from '../ConfirmDialog/ConfirmDialog';
import { FilterBuilder } from '../FilterBuilder';
import {
  AppliedFilters,
  OnCloseFilterBuilderProps,
} from '../FilterBuilder/types';
import { FILTER_BUILDER_ACTIONS } from '../FilterBuilder/contants';
import { FilterChips } from '../FilterBuilder/FilterChips';
import { OnDeleteCurrentFilterProps } from '../FilterBuilder/FilterChips/types';
import { useGetPriceChangeFilterValues } from '../../services/useGetPriceChangeFilterValues';
import { PriceHistoryDialog } from '../PriceHistoryDialog';
import usePriceChangeEventTabContext from '../../hooks/usePriceChangeEventTabContext';
import FPUpdateThresholdDialog from '../FPUpdateThresholdDialog';
import MobileSitesPriceTable from '../MobileView/PriceChangeTable/MobileSitesPriceTable';
import {
  BOTTOM_HEIGHT,
  GAP,
  SUCCESS_MSG_UPDATE_PRICE,
} from '../MobileView/Constants';
import FilterBuilderMobile from '../MobileView/FilterBuilder/FilterBuilderMobile';
import useIsMobile from '../../hooks/useIsMobile';
import BulkUpdateUploader from '../BulkUpload/BulkUpdateUploader';
import {
  PRICE_QUEUE_OPERATORS_BY_VARIABLE,
  PRICE_QUEUE_VARIABLE_FILTER_OPTIONS,
} from './constants';
import { EnhancedSiteDetail } from './types';
import SchedulePriceModal from './SchedulePriceModal/SchedulePriceModal';
import SitesPriceTable from './SitesPriceTable/SitesPriceTable';

const PriceChangeTable: FC = () => {
  const {
    isPriceHistoryModalOpen,
    updatedProducts,
    setUpdatedProducts,
    filteredProducts,
    setFilteredProducts,
    isFetchingThresholdValue,
    refetchTenantDetails,
    refetchSettingsDefinitions,
  } = usePriceChangeEventTabContext();

  const [currentPriceResponse, setCurrentPriceResponse] =
    useState<EnhancedSiteDetail[]>(null);
  const [filteredProductsByPageIndex, setFiltereProductsByPageIndex] = useState<
    EnhancedSiteDetail[]
  >([]);

  const [isScheduleModalOpen, setIsScheduleModalOpen] =
    useState<boolean>(false);
  const [pageIndex, setPageIndex] = useState<number>(defaultPageIndex);
  const [pageSize] = useState<number>(defaultPageSize);
  const [isCheck, setIsCheck] = useState<boolean>(false);
  const [filteredTotalPages, setFilteredTotalPages] = useState<number>(1);
  const [filteredPageIndex, setFilteredPageIndex] =
    useState<number>(defaultPageIndex);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [isFilterBuilderOpen, setIsFilterBuilderOpen] =
    useState<boolean>(false);
  const [appliedFilters, setAppliedFilters] = useState<
    AppliedFilters | undefined
  >(undefined);

  const [isUploaderOpen, setIsUploaderOpen] = useState(false);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [__, setSearchParams] = useSearchParams();
  const isMobile = useIsMobile();
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const navigate = useNavigate();
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);

  useEffect(() => {
    if (updatedProducts?.length) {
      setSearchParams(params => {
        params.set('unsavedChanges', 'true');
        return params;
      });
    } else {
      setSearchParams(params => {
        params.delete('unsavedChanges');
        return params;
      });
    }
  }, [updatedProducts]);

  const getFilteredPageData = (pIndex: number, pSize: number) => {
    const start = (pIndex - 1) * pSize;
    const end = pIndex * pSize;
    setFilteredPageIndex(pIndex);
    setFiltereProductsByPageIndex(filteredProducts.slice(start, end));
    const totalSiteCount = filteredProducts.reduce(
      accumulator => accumulator + 1,
      0
    );
    const totalPages = Math.ceil(totalSiteCount / pSize);
    setFilteredTotalPages(totalPages);
  };

  const {
    data: prices,
    isLoading: isPriceLoading,
    isFetching,
    refetch: getCurrentPrices,
  } = useGetCurrentPrice(pageIndex, pageSize, appliedFilters, {
    queryKey: ['useGetCurrentPrice', pageIndex, pageSize],
    enabled: false,
  });

  const {
    data: exportCurrentPrices,
    isFetching: isLoadingExportCurrentPrices,
    refetch: getExportCurrentPrices,
  } = useGetExportCurrentPrice({
    filters: appliedFilters,
    queryOpts: {
      queryKey: ['useGetExportCurrentPrice'],
      enabled: false,
    },
  });

  const handleFilterBuilderClose = ({
    action,
    newFilters,
  }: OnCloseFilterBuilderProps) => {
    if (action === FILTER_BUILDER_ACTIONS.apply) {
      setAppliedFilters(newFilters);
      setPageIndex(defaultPageIndex);
    }
    setIsFilterBuilderOpen(false);
    setIsMobileFilterOpen(false);
  };

  const handleCheckboxChange = () => {
    if (!isCheck) {
      getFilteredPageData(filteredPageIndex, pageSize);
    }
    if (isCheck) {
      setCurrentPriceResponse(null);
      setPageIndex(pageIndex);
      getCurrentPrices();
    }
    setIsCheck(checked => !checked);
  };

  useEffect(() => {
    if (isPriceLoading || isFetching) {
      setCurrentPriceResponse(null);
      return;
    }
    if (prices && !isFetching) {
      const updatedProductsData = prices.results.map(site => ({
        ...site,
        products: site.products.map(product => ({
          ...product,
          siteName: site.siteName,
        })),
      }));
      // setCurrentPriceResponse(updatedProductsData);
      setCurrentPriceResponse(prev =>
        pageIndex === 1
          ? updatedProductsData
          : [...(prev ?? []), ...updatedProductsData]
      );
    } else {
      setCurrentPriceResponse([]);
    }
  }, [prices, isFetching, isPriceLoading]);

  useEffect(() => {
    if (filteredProducts.length) {
      const totalSiteCount = filteredProducts.reduce(
        accumulator => accumulator + 1,
        0
      );
      setFilteredTotalPages(Math.ceil(totalSiteCount / pageSize));
    }
  }, [filteredProducts]);

  useEffect(() => {
    getCurrentPrices();
    refetchTenantDetails();
  }, [pageIndex, pageSize, appliedFilters]);

  useEffect(() => {
    if (exportCurrentPrices?.url) {
      const link = document.createElement('a');
      link.href = exportCurrentPrices.url;
      link.download = 'Test';
      link.click();
      link.remove();
    }
  }, [exportCurrentPrices]);

  const totalResults = prices?.resultsMetadata?.totalResults
    ? prices.resultsMetadata.totalResults
    : 0;

  const totalPages = Math.ceil(totalResults / defaultPageSize);

  const hasFuelPriceEditRole = useHasPermissions({
    userRoles: [USER_ROLES.FUEL_PRICE_MGMT_EDIT],
  });

  const hasSiteViewRole = useHasPermissions({
    userRoles: [USER_ROLES.SITE_SETTINGS_VIEW],
  });

  const isPriceInHardRange = useMemo(
    () =>
      updatedProducts.every(
        p =>
          p.newFuelPrice > PRICE_LIMIT.MIN && p.newFuelPrice < PRICE_LIMIT.MAX
      ),
    [updatedProducts]
  );

  const isValidRule = useMemo(() => {
    const isValid = updatedProducts.every(p =>
      // require 4 digits and 1 decimal places
      REGEX_FOUR_DIGIT_WITH_DECIMAL.test(p.newFuelPrice.toString())
    );
    return isValid;
  }, [updatedProducts]);

  const onRefreshButtonClick = async () => {
    setCurrentPriceResponse(null);
    setUpdatedProducts([]);
    setFilteredProducts([]);
    setFiltereProductsByPageIndex([]);
    setFilteredTotalPages(1);
    setIsCheck(false);
    setAppliedFilters(undefined);
    await refetchTenantDetails();
    await getCurrentPrices();
    await refetchSettingsDefinitions();
  };
  const SNACKBAR_FLAG = 'price_update_success';

  /** snackbar close */
  const handleSnackbarClose = (
    _e?: React.SyntheticEvent | Event,
    reason?: string
  ) => {
    if (reason === 'clickaway') return;
    setSnackbarOpen(false);
  };
  const handleUpdateSuccess = useCallback(() => {
    setIsScheduleModalOpen(false);
    if (isMobile) {
      sessionStorage.setItem(SNACKBAR_FLAG, '1');
      window.location.reload();
    }
  }, [navigate, isMobile]);

  useEffect(() => {
    if (isPriceLoading || isFetching || currentPriceResponse === null) return;
    if (sessionStorage.getItem(SNACKBAR_FLAG)) {
      setSnackbarOpen(true);
      sessionStorage.removeItem(SNACKBAR_FLAG);
    }
  }, [isPriceLoading, isFetching, currentPriceResponse]);

  const handleDeleteAppliedFilter = ({
    variableFilterValue,
    operatorFilterValue,
    valueFilterValue,
  }: OnDeleteCurrentFilterProps) => {
    const newCurrentFilters = cloneDeep(appliedFilters);
    const newFilters = newCurrentFilters[variableFilterValue][
      operatorFilterValue
    ].filter(({ value }) => value !== valueFilterValue);
    if (!newFilters.length) {
      Object.keys(newCurrentFilters[variableFilterValue]).length > 1
        ? delete newCurrentFilters[variableFilterValue][operatorFilterValue]
        : delete newCurrentFilters[variableFilterValue];
    } else {
      newCurrentFilters[variableFilterValue][operatorFilterValue] = newFilters;
    }
    setAppliedFilters(
      isEmpty(newCurrentFilters) ? undefined : newCurrentFilters
    );
  };

  const appliedFiltersCount = appliedFilters
    ? Object.keys(appliedFilters).reduce((count, variableFilterValue) => {
        Object.keys(appliedFilters[variableFilterValue]).forEach(
          operatorFilterValue => {
            // eslint-disable-next-line no-param-reassign
            count +=
              appliedFilters[variableFilterValue][operatorFilterValue].length;
          }
        );
        return count;
      }, 0)
    : 0;

  const [isThresholdModalOpen, setIsThresholdModalOpen] = useState(false);

  const handleOpenDialog = async () => {
    setIsThresholdModalOpen(true);
  };

  const handleCloseDialog = async () => {
    setIsThresholdModalOpen(false);
  };

  const isUpdatePriceDisabled = useMemo(
    () =>
      !updatedProducts.length ||
      !isPriceInHardRange ||
      !isValidRule ||
      !hasFuelPriceEditRole,
    [
      updatedProducts.length,
      isPriceInHardRange,
      isValidRule,
      hasFuelPriceEditRole,
    ]
  );

  const isMobileLoading: boolean = Boolean(
    isMobile &&
      isNavigating &&
      (isPriceLoading || isFetching || currentPriceResponse === null)
  );

  const handlePrevPage = () => {
    if (isPriceLoading || isFetching) return;
    setIsNavigating(true);
    setPageIndex(p => Math.max(1, p - 1));
  };

  const handleNextPage = () => {
    if (isPriceLoading || isFetching) return;
    setIsNavigating(true);
    setPageIndex(p => p + 1);
  };

  if (
    !isMobile &&
    (isPriceLoading ||
      isFetching ||
      currentPriceResponse === null ||
      isFetchingThresholdValue)
  ) {
    return (
      <Box
        data-testid='pct-circular-progress-box'
        sx={{
          display: 'flex',
          backgroundColor: 'white',
          justifyContent: 'center',
          alignItems: 'center',
          width: '100%',
          height: 'calc(100vh - 124px)',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        height: 'calc(100vh - 124px)',
      }}
    >
      {isThresholdModalOpen && (
        <FPUpdateThresholdDialog
          isOpen={isThresholdModalOpen}
          onClose={handleCloseDialog}
        />
      )}
      {isScheduleModalOpen && (
        <SchedulePriceModal
          isScheduleModalOpen={isScheduleModalOpen}
          setIsScheduleModalOpen={setIsScheduleModalOpen}
          onSuccess={handleUpdateSuccess}
          updatedProducts={updatedProducts}
        />
      )}
      {isConfirmDialogOpen && (
        <ConfirmDialog
          isOpen={isConfirmDialogOpen}
          setIsOpen={setIsConfirmDialogOpen}
          title='Changes you made may not be saved'
          handleSubmit={onRefreshButtonClick}
        />
      )}
      {isFilterBuilderOpen && (
        <FilterBuilder
          isOpen={isFilterBuilderOpen}
          variableFilterOptions={PRICE_QUEUE_VARIABLE_FILTER_OPTIONS}
          operatorsByVariable={PRICE_QUEUE_OPERATORS_BY_VARIABLE}
          onCloseFilterBuilder={handleFilterBuilderClose}
          useGetFilterValues={useGetPriceChangeFilterValues}
          appliedFilters={appliedFilters}
        />
      )}
      {isMobile && (
        <FilterBuilderMobile
          isOpen={isMobileFilterOpen}
          variableFilterOptions={PRICE_QUEUE_VARIABLE_FILTER_OPTIONS}
          operatorsByVariable={PRICE_QUEUE_OPERATORS_BY_VARIABLE}
          onCloseFilterBuilder={handleFilterBuilderClose}
          useGetFilterValues={useGetPriceChangeFilterValues}
          appliedFilters={appliedFilters}
        />
      )}
      {isPriceHistoryModalOpen && <PriceHistoryDialog />}
      {!isMobile && (
        <Box
          display='flex'
          flexDirection='row'
          justifyContent='space-between'
          paddingLeft={3}
          py={1}
        >
          <Box display='flex' gap={2} alignItems='center'>
            <Typography
              data-testid='pct-setprices-typography'
              fontSize='2rem'
              fontWeight='medium'
              alignSelf='center'
            >
              Set prices for fuel price update
            </Typography>
            {!isCheck && (
              <>
                <Badge color='primary' badgeContent={appliedFiltersCount}>
                  <IconButton
                    color='primary'
                    aria-label='Filter Builder'
                    disabled={
                      !currentPriceResponse?.length && !filteredProducts.length
                    }
                    onClick={() => setIsFilterBuilderOpen(isOpen => !isOpen)}
                    sx={{ height: 5, width: 5 }}
                  >
                    <FilterList />
                  </IconButton>
                </Badge>

                {appliedFilters && (
                  <FilterChips
                    variableFilterOptions={PRICE_QUEUE_VARIABLE_FILTER_OPTIONS}
                    operatorsByVariable={PRICE_QUEUE_OPERATORS_BY_VARIABLE}
                    currentFilters={appliedFilters}
                    onDeleteCurrentFilter={handleDeleteAppliedFilter}
                  />
                )}
              </>
            )}
          </Box>
          <Box
            display='flex'
            justifyContent='flex-start'
            alignItems='center'
            marginRight='16px'
            gap='16px'
          >
            <FormControlLabel
              label='Hide unedited sites'
              sx={{ '& .MuiFormControlLabel-label': { fontSize: '14px' } }}
              control={
                <Checkbox
                  checked={isCheck}
                  onChange={handleCheckboxChange}
                  disabled={!isCheck && !filteredProducts.length}
                />
              }
            />
            <Button
              data-testid='pct-btn-refresh'
              variant='outlined'
              startIcon={<RefreshTwoTone />}
              onClick={() =>
                updatedProducts?.length
                  ? setIsConfirmDialogOpen(true)
                  : onRefreshButtonClick()
              }
            >
              Refresh
            </Button>
            {hasSiteViewRole && (
              <Button
                data-testid='pct-btn-threshold'
                onClick={handleOpenDialog}
                variant='outlined'
                startIcon={<Settings />}
              >
                Settings
              </Button>
            )}
            <LoadingButton
              data-testid='pct-btn-export'
              size='small'
              onClick={() => getExportCurrentPrices()}
              loading={isLoadingExportCurrentPrices}
              loadingPosition='start'
              startIcon={<CloudDownloadTwoTone />}
              variant='outlined'
              disabled={!currentPriceResponse?.length}
            >
              <span>Export</span>
            </LoadingButton>
            <Button
              data-testid='pct-btn-bulkupdate'
              size='small'
              startIcon={<CloudUpload />}
              variant='outlined'
              onClick={() => setIsUploaderOpen(true)}
              disabled={!hasFuelPriceEditRole || !currentPriceResponse?.length}
            >
              Bulk Update
            </Button>
          </Box>
        </Box>
      )}
      {isMobile ? (
        <MobileSitesPriceTable
          devicesData={
            isCheck ? filteredProductsByPageIndex : currentPriceResponse
          }
          onUpdatePrices={() => setIsScheduleModalOpen(true)}
          onPrevPage={handlePrevPage}
          onNextPage={handleNextPage}
          currentPage={pageIndex}
          totalPages={totalPages}
          isUpdatePriceDisabled={isUpdatePriceDisabled}
          isLoading={isMobileLoading}
          onOpenFilter={() => setIsMobileFilterOpen(true)}
          appliedFilterCount={appliedFiltersCount}
        />
      ) : (
        <>
          <SitesPriceTable
            devicesData={
              isCheck ? filteredProductsByPageIndex : currentPriceResponse
            }
          />
          <Box
            sx={{
              display: 'flex',
              justifyContent:
                currentPriceResponse?.length === 0 ? 'end' : 'space-between',
              backgroundColor: theme.palette.common.white,
              pr: 2,
              borderTop: `1px solid ${theme.palette.divider}`,
            }}
          >
            <Pagination
              data-testid='pct-pagination'
              count={isCheck ? filteredTotalPages : totalPages}
              page={isCheck ? filteredPageIndex : pageIndex}
              onChange={(_, page) => {
                if (!isCheck && page === pageIndex) return;
                if (isCheck && page === filteredPageIndex) return;
                setCurrentPriceResponse(null);
                isCheck
                  ? getFilteredPageData(page, pageSize)
                  : setPageIndex(page);
              }}
              sx={{
                marginTop: 2,
                marginBottom: 2,
                display: currentPriceResponse?.length === 0 && 'none',
              }}
            />

            <Button
              data-testid='pct-btn-updateprice'
              variant='contained'
              sx={{
                width: 'auto',
                maxWidth: '200px',
              }}
              disabled={
                !updatedProducts.length ||
                !isPriceInHardRange ||
                !isValidRule ||
                !hasFuelPriceEditRole
              }
              onClick={() => setIsScheduleModalOpen(true)}
            >
              Update Price
            </Button>
          </Box>
        </>
      )}
      {/* snack bar */}

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        onClose={handleSnackbarClose}
        sx={{
          bottom: {
            xs: BOTTOM_HEIGHT + GAP,
          },
        }}
      >
        <Alert
          severity='success'
          action={
            <IconButton
              size='small'
              aria-label='close'
              color='inherit'
              onClick={handleSnackbarClose}
            >
              <CloseIcon fontSize='small' />
            </IconButton>
          }
          sx={{ width: '100%' }}
        >
          {SUCCESS_MSG_UPDATE_PRICE}
        </Alert>
      </Snackbar>

      {isUploaderOpen && (
        <Dialog
          open={isUploaderOpen}
          onClose={() => setIsUploaderOpen(false)}
          maxWidth='sm'
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 3,
              overflow: 'visible',
            },
          }}
        >
          <BulkUpdateUploader
            onClose={() => setIsUploaderOpen(false)}
            onCancel={() => setIsUploaderOpen(false)}
          />
        </Dialog>
      )}
    </Box>
  );
};

export default PriceChangeTable;
