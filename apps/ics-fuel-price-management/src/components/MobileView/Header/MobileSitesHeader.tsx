import React, { FC, memo } from 'react';
import {
  Box,
  Avatar,
  Typography,
  Divider,
  useMediaQuery,
  Badge,
  IconButton,
} from '@mui/material';
import TouchAppIcon from '@mui/icons-material/TouchApp';
import FilterListIcon from '@mui/icons-material/FilterList';
import { indigo } from '@mui/material/colors';
import { HEADER_TITLE, SUBTITLE_TEXT } from '../Constants';

interface MobileSitesHeaderProps {
  onOpenFilter: () => void;
  appliedFilterCount?: number;
}

const MobileSitesHeader: FC<MobileSitesHeaderProps> = ({
  onOpenFilter,
  appliedFilterCount,
}) => {
  const isSmallScreen = useMediaQuery('(max-width:375px)');

  return (
    <Box
      p={2}
      sx={{
        position: 'sticky',
        top: 0,
        backgroundColor: 'common.white',
        zIndex: 10,
      }}
    >
      <Box
        display='flex'
        alignItems='center'
        justifyContent='space-between'
        flexWrap={isSmallScreen ? 'wrap' : 'nowrap'}
        sx={{ width: '100%' }}
      >
        <Box display='flex' alignItems='center' sx={{ flex: 1, minWidth: 0 }}>
          <Avatar sx={{ bgcolor: indigo[100] }}>
            <TouchAppIcon sx={{ color: 'common.icon' }} />
          </Avatar>
          <Typography
            variant='h6'
            noWrap
            sx={{
              fontWeight: 600,
              ml: isSmallScreen ? 1 : 2,
              fontSize: '1.8rem',
              whiteSpace: 'nowrap',
            }}
          >
            {HEADER_TITLE}
          </Typography>
        </Box>
      </Box>

      <Divider
        sx={{
          mt: 2,
          mb: 1,
          width: '100vw',
          position: 'relative',
          left: '50%',
          transform: 'translateX(-50%)',
        }}
      />

      <Box
        display='flex'
        alignItems='center'
        gap={3}
        sx={{ width: '100%', mt: 1 }}
      >
        <Typography
          variant='body2'
          sx={{ fontWeight: 600, fontSize: '1.6rem' }}
        >
          {SUBTITLE_TEXT}
        </Typography>

        <Badge
          color='primary'
          badgeContent={appliedFilterCount}
          invisible={!appliedFilterCount}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
          sx={{
            '& .MuiBadge-badge': { top: 10, right: 8 },
          }}
        >
          <IconButton
            size='small'
            onClick={onOpenFilter}
            sx={{
              '&:focus': { outline: 'none' },
            }}
          >
            <FilterListIcon color='primary' />
          </IconButton>
        </Badge>
      </Box>
    </Box>
  );
};

export default memo(MobileSitesHeader);
