import React, {
  FC,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Tooltip,
  Typography,
  Zoom,
} from '@mui/material';
import { <PERSON>Downward, <PERSON>Up<PERSON>, Error } from '@mui/icons-material';
import { EnhancedSiteDetail, CurrentPrice } from '../../PriceChangeTable/types';
import {
  APPROVAL_REQUIRE,
  EMPTY_VALUE,
  ERROR_MSG_INVALID_FORMAT,
  ERROR_MSG_PRICE_RANGE,
  PRICE_ADJUSTMENT,
  PRICE_LIMIT,
  REGEX_FOUR_DIGIT_WITH_DECIMAL,
  REGEX_ONLY_DECIMAL_AND_NUMBER,
} from '../../../utils/constants';
import { theme } from '../../../theme';
import { formatNumberWithSetDigits } from '../../../utils/formatNumberWithSetDigits';
import useHasPermissions from '../../../hooks/useHasPermissions';
import { USER_ROLES } from '../../../types';
import usePriceChangeEventTabContext from '../../../hooks/usePriceChangeEventTabContext';
import { lightenColor } from '../../PriceChangeTable/SitesPriceTable/utils/getStyle';
import useIsMobile from '../../../hooks/useIsMobile';

interface PriceCardProps {
  product: CurrentPrice;
  rowData?: EnhancedSiteDetail;
}

const calculateMinMax = (percentage: number) => {
  const factor = percentage / 100;
  return {
    min: 1 - factor,
    max: 1 + factor,
  };
};

const IconStyle: FC<{
  percentageDifference: number;
  newPriceNumber: number;
  hasPendingApproval: boolean;
}> = ({ newPriceNumber, percentageDifference, hasPendingApproval }) => {
  if (hasPendingApproval) {
    return (
      <Tooltip title='Pending price approval' arrow TransitionComponent={Zoom}>
        <Error
          sx={{
            bottom: 2,
            position: 'relative',
            color: theme.palette.common.errorRed,
            animation: 'flashing 1.5s infinite',
            '@keyframes flashing': {
              '0%': { opacity: 1 },
              '50%': { opacity: 0 },
              '100%': { opacity: 1 },
            },
          }}
        />
      </Tooltip>
    );
  }
  if (newPriceNumber === 0) {
    return null;
  }
  if (percentageDifference === 0) {
    return null;
  }
  return percentageDifference > 0 ? (
    <ArrowUpward sx={{ bottom: 2, position: 'relative', color: 'black' }} />
  ) : (
    <ArrowDownward sx={{ bottom: 2, position: 'relative', color: 'black' }} />
  );
};

const Element: FC<{
  newPrice: string;
  percentageDifference: number;
  newPriceNumber: number;
  hasPendingApproval: boolean;
  isExceedThreshold: boolean;
}> = ({
  newPrice,
  percentageDifference,
  newPriceNumber,
  hasPendingApproval,
  isExceedThreshold,
}) => (
  <Box
    display='flex'
    flexDirection='row'
    width='max-content'
    fontSize={12}
    fontWeight={500}
    color={isExceedThreshold ? 'red' : null}
  >
    {newPrice && percentageDifference !== EMPTY_VALUE
      ? `(${percentageDifference.toFixed(2)}%)`
      : ''}
    <IconStyle
      newPriceNumber={newPriceNumber}
      percentageDifference={percentageDifference}
      hasPendingApproval={hasPendingApproval}
    />
  </Box>
);

const PercentageComponent: FC<{
  newPrice: string;
  product: CurrentPrice;
  isExceedThreshold: boolean;
}> = ({ newPrice, product, isExceedThreshold }) => {
  const { price: productPrice, hasPendingApproval } = product;
  const newPriceNumber = parseFloat(newPrice) || 0;
  const productPriceNumber = parseFloat(productPrice) || 0;
  const percentageDifference =
    ((newPriceNumber - productPriceNumber) / productPriceNumber) * 100;

  return (
    <Typography>
      <Element
        newPrice={newPrice}
        percentageDifference={percentageDifference}
        newPriceNumber={newPriceNumber}
        hasPendingApproval={hasPendingApproval}
        isExceedThreshold={isExceedThreshold}
      />
    </Typography>
  );
};

const MobilePriceCard: FC<PriceCardProps> = memo(
  ({ product, rowData = {} }) => {
    const {
      setUpdatedProducts,
      filteredProducts,
      setFilteredProducts,
      tenantDetails,
    } = usePriceChangeEventTabContext();

    const entityValue =
      tenantDetails?.entityValue && JSON.parse(tenantDetails.entityValue);
    const limit =
      entityValue?.currentThresholdLimit ||
      entityValue?.currentThresholdLimit === 0
        ? calculateMinMax(entityValue.currentThresholdLimit)
        : { min: PRICE_ADJUSTMENT.MIN, max: PRICE_ADJUSTMENT.MAX };

    const hasFuelPriceViewRole = useHasPermissions({
      userRoles: [USER_ROLES.FUEL_PRICE_MGMT_VIEW],
      partialRoleCheck: true,
    });
    const hasFuelPriceEditRole = useHasPermissions({
      userRoles: [USER_ROLES.FUEL_PRICE_MGMT_EDIT],
      partialRoleCheck: true,
    });
    const hasFuelPriceApproverRole = useHasPermissions({
      userRoles: [USER_ROLES.FUEL_PRICE_MGMT_THRESHOLD],
      partialRoleCheck: true,
    });
    const [newPrice, setNewPrice] = useState('');
    const [error, setError] = useState(false);
    const [siteProducts] = useState(rowData.products);
    const [isExceedThreshold, setIsExceedThreshold] = useState(false);

    useEffect(() => {
      const matchSite = filteredProducts.find(
        siteDetail => siteDetail.fpsSiteId === product.fpsSiteId
      );
      if (matchSite) {
        const matchProduct = matchSite.products.find(
          p => p.icsProductId === product.icsProductId
        );
        if (matchProduct?.newFuelPrice) {
          setNewPrice(matchProduct.newFuelPrice.toString());
        }
      }
    }, [filteredProducts]);

    const isInvalidPrice = useMemo(
      () =>
        !product.price || product.price === '0' || product.price === '0.000',
      [product.price]
    );

    const shouldPriceCardDisabled = useMemo(
      () => (value: string) => {
        if (product.hasPendingApproval) {
          return true;
        }
        if (hasFuelPriceEditRole) {
          return false;
        }
        return (
          !value || value === '0' || value === '0.000' || !hasFuelPriceEditRole
        );
      },
      [hasFuelPriceEditRole]
    );

    const minPriceNumber = parseFloat(product.price) * limit.min;
    const minPriceFormatted = formatNumberWithSetDigits(minPriceNumber, 4);
    const minPrice = parseFloat(minPriceFormatted);

    const maxPriceNumber = parseFloat(product.price) * limit.max;
    const maxPriceFormatted = formatNumberWithSetDigits(maxPriceNumber, 4);
    const maxPrice = parseFloat(maxPriceFormatted);

    const msgConvertion = useCallback(
      setPrice => {
        const outOfHardRange =
          setPrice <= PRICE_LIMIT.MIN || setPrice >= PRICE_LIMIT.MAX;
        if (entityValue?.currentThresholdLimit === 0 && !outOfHardRange) {
          return ERROR_MSG_PRICE_RANGE.replace('{minPrice}', '$0.001').replace(
            '{maxPrice}',
            '$9.999'
          );
        }

        if (
          (isInvalidPrice && !hasFuelPriceApproverRole) ||
          (hasFuelPriceViewRole &&
            !hasFuelPriceEditRole &&
            hasFuelPriceViewRole &&
            !hasFuelPriceApproverRole)
        ) {
          return '';
        }

        if (!hasFuelPriceApproverRole) {
          if (
            parseFloat(setPrice) >= PRICE_LIMIT.MAX ||
            parseFloat(setPrice) <= PRICE_LIMIT.MIN
          ) {
            return ERROR_MSG_PRICE_RANGE.replace(
              '{minPrice}',
              `$0.001`
            ).replace('{maxPrice}', `$9.999`);
          }
          if (
            parseFloat(setPrice) < minPrice ||
            parseFloat(setPrice) > maxPrice
          ) {
            return APPROVAL_REQUIRE.replace(
              '{minPrice}',
              `$${minPriceFormatted}`
            ).replace(
              '{maxPrice}',
              +maxPriceFormatted >= PRICE_LIMIT.MAX
                ? `$9.999`
                : `$${maxPriceFormatted}`
            );
          }
        }

        if (hasFuelPriceApproverRole) {
          if (isInvalidPrice) {
            return ERROR_MSG_PRICE_RANGE.replace(
              '{minPrice}',
              `$0.001`
            ).replace('{maxPrice}', `$9.999`);
          }
          if (
            !isInvalidPrice &&
            setPrice &&
            parseFloat(setPrice) >= minPrice &&
            parseFloat(setPrice) <= maxPrice
          ) {
            return ERROR_MSG_PRICE_RANGE.replace(
              '{minPrice}',
              minPrice <= PRICE_LIMIT.MIN ? '$0.001' : `$${minPriceFormatted}`
            ).replace(
              '{maxPrice}',
              maxPrice >= PRICE_LIMIT.MAX ? '$9.999' : `$${maxPriceFormatted}`
            );
          }
          if (
            parseFloat(setPrice) >= PRICE_LIMIT.MIN &&
            parseFloat(setPrice) <= PRICE_LIMIT.MAX
          ) {
            return ERROR_MSG_PRICE_RANGE.replace(
              '{minPrice}',
              `$0.001`
            ).replace('{maxPrice}', `$9.999`);
          }
          if (
            parseFloat(setPrice) >= minPrice &&
            parseFloat(setPrice) <= maxPrice
          ) {
            return ERROR_MSG_PRICE_RANGE.replace(
              '{minPrice}',
              `$${minPriceFormatted}`
            ).replace('{maxPrice}', `$${maxPriceFormatted}`);
          }
          if (
            parseFloat(setPrice) <= PRICE_LIMIT.MIN ||
            parseFloat(setPrice) >= PRICE_LIMIT.MAX
          ) {
            return ERROR_MSG_PRICE_RANGE.replace(
              '{minPrice}',
              `$0.001`
            ).replace('{maxPrice}', `$9.999`);
          }
        }
        return ERROR_MSG_PRICE_RANGE.replace(
          '{minPrice}',
          minPrice <= PRICE_LIMIT.MIN ? '$0.001' : `$${minPriceFormatted}`
        ).replace(
          '{maxPrice}',
          maxPrice >= PRICE_LIMIT.MAX ? '$9.999' : `$${maxPriceFormatted}`
        );
      },
      [minPrice, maxPrice, entityValue?.currentThresholdLimit]
    );

    const [errorMsg, setErrorMsg] = useState(msgConvertion(product.price));

    useEffect(() => {
      setErrorMsg(msgConvertion(product.price));
    }, [minPrice, maxPrice]);

    const handlePriceChange = event => {
      if (!REGEX_ONLY_DECIMAL_AND_NUMBER.test(event.target.value)) {
        return;
      }

      const priceInString = event.target.value;

      const newSetPrice =
        Math.round(Number.parseFloat(event.target.value) * 1000) / 1000;

      if (event.target.value === '') {
        setNewPrice(event.target.value);
        setError(false);
        setErrorMsg(msgConvertion(event.target.value));
        setFilteredProducts(prevFilteredProducts => {
          if (!prevFilteredProducts.length) {
            return prevFilteredProducts;
          }
          const final = prevFilteredProducts
            .map(prevSiteDetail => {
              let fp;
              if (prevSiteDetail.fpsSiteId === product.fpsSiteId) {
                const tp = prevSiteDetail.products.filter(
                  p => p.icsProductId !== product.icsProductId
                );
                fp = tp.sort((p1, p2) => {
                  if (p1.productName < p2.productName) return 1;
                  if (p1.productName > p2.productName) return -1;
                  return 0;
                });
              }
              return {
                ...prevSiteDetail,
                products: fp || [...prevSiteDetail.products],
              };
            })
            .filter(prevSiteDetail =>
              prevSiteDetail?.products.some(p => p?.newFuelPrice)
            )
            .sort((a, b) => {
              const countA = a.products.length;
              const countB = b.products.length;
              if (countA !== countB) {
                return countB - countA;
              }
              const nameA = a.siteName.toUpperCase();
              const nameB = b.siteName.toUpperCase();
              if (nameA < nameB) {
                return -1;
              }
              if (nameA > nameB) {
                return 1;
              }
              return 0;
            });

          const finalFinal = final.map(filteredSite => {
            if (filteredSite.fpsSiteId !== product.fpsSiteId) {
              return filteredSite;
            }
            const updatedSite = {
              ...filteredSite,
              products: siteProducts.map(sp => {
                const match = filteredSite.products.find(
                  p => p.icsProductId === sp.icsProductId
                );
                if (!match) return sp;
                return {
                  ...match,
                };
              }),
            };
            return updatedSite;
          });
          return finalFinal;
        });

        setUpdatedProducts(prevProducts =>
          prevProducts.filter(
            p =>
              p.icsProductId !== product.icsProductId ||
              p.fpsSiteId !== product.fpsSiteId
          )
        );
        return;
      }

      if (
        parseFloat(priceInString) < minPrice ||
        parseFloat(priceInString) > maxPrice
      ) {
        setIsExceedThreshold(true);
      } else {
        setIsExceedThreshold(false);
      }

      if (hasFuelPriceApproverRole) {
        const isValidFormat = REGEX_FOUR_DIGIT_WITH_DECIMAL.test(
          event.target.value
        );
        setError(
          !isValidFormat ||
            event.target.value <= PRICE_LIMIT.MIN ||
            event.target.value >= PRICE_LIMIT.MAX
        );
        setErrorMsg(
          isValidFormat
            ? msgConvertion(event.target.value)
            : ERROR_MSG_INVALID_FORMAT
        );
      }

      if (!hasFuelPriceApproverRole) {
        const outOfSoftRange = newSetPrice < minPrice || newSetPrice > maxPrice;
        const outOfHardRange =
          newSetPrice <= PRICE_LIMIT.MIN || newSetPrice >= PRICE_LIMIT.MAX;
        const isValidFormat = REGEX_FOUR_DIGIT_WITH_DECIMAL.test(
          event.target.value
        );
        if (entityValue?.currentThresholdLimit === 0) {
          setError(!isValidFormat || outOfHardRange);
        } else {
          setError(!isValidFormat || outOfSoftRange || outOfHardRange);
        }
        setErrorMsg(
          isValidFormat
            ? msgConvertion(event.target.value)
            : ERROR_MSG_INVALID_FORMAT
        );
      }

      setNewPrice(event.target.value);

      setFilteredProducts(prevFilteredSites => {
        if (!prevFilteredSites.length) {
          const results = [
            {
              fpsSiteId: product.fpsSiteId,
              siteName: product.siteName,
              siteId: product.siteId,
              products: siteProducts.map(sp => {
                if (product.icsProductId !== sp.icsProductId) return { ...sp };
                return {
                  newFuelPrice: priceInString,
                  ...sp,
                };
              }),
            },
          ];
          return results;
        }

        let isSiteExist = false;
        let isProductExist = false;
        const newPrevFilteredSites = prevFilteredSites.map(prevSiteDetail => {
          let newPrevProducts;
          if (prevSiteDetail.fpsSiteId === product.fpsSiteId) {
            isSiteExist = true;
            const prevProducts = prevSiteDetail.products.map(p => {
              if (p.icsProductId === product.icsProductId && p?.newFuelPrice) {
                isProductExist = true;
                return {
                  ...p,
                  newFuelPrice: priceInString,
                };
              }
              return { ...p };
            });
            newPrevProducts = prevProducts.sort((p1, p2) => {
              if (p1.productName < p2.productName) return 1;
              if (p1.productName > p2.productName) return -1;
              return 0;
            });
          }
          return {
            ...prevSiteDetail,
            products: newPrevProducts || prevSiteDetail.products,
          };
        });

        if (!isSiteExist) {
          const isSiteExistResult = [
            ...prevFilteredSites,
            {
              fpsSiteId: product.fpsSiteId,
              siteName: product.siteName,
              siteId: product.siteId,
              products: siteProducts.map(sp => {
                const matchProduct = sp.icsProductId === product.icsProductId;
                if (!matchProduct) return { ...sp };
                return {
                  newFuelPrice: priceInString,
                  ...sp,
                };
              }),
            },
          ].sort((a, b) => {
            const countA = a.products.length;
            const countB = b.products.length;
            if (countA !== countB) {
              return countB - countA;
            }
            const nameA = a.siteName.toUpperCase();
            const nameB = b.siteName.toUpperCase();
            if (nameA < nameB) {
              return -1;
            }
            if (nameA > nameB) {
              return 1;
            }
            return 0;
          });
          return isSiteExistResult;
        }

        if (!isProductExist) {
          const data = prevFilteredSites.map(site => {
            if (product.fpsSiteId !== site.fpsSiteId) {
              return { ...site };
            }
            const newProducts = siteProducts.map(p => {
              if (p.icsProductId === product.icsProductId) {
                return {
                  ...p,
                  newFuelPrice: priceInString,
                };
              }
              const newPriceExist = site.products.find(
                p1 => p1.icsProductId === p.icsProductId
              )?.newFuelPrice;
              return {
                ...p,
                ...(newPriceExist && { newFuelPrice: newPriceExist }),
              };
            });
            return { ...site, products: newProducts };
          });
          return data.sort((a, b) => {
            const countA = a.products.length;
            const countB = b.products.length;
            if (countA !== countB) {
              return countB - countA;
            }
            const nameA = a.siteName.toUpperCase();
            const nameB = b.siteName.toUpperCase();
            if (nameA < nameB) {
              return -1;
            }
            if (nameA > nameB) {
              return 1;
            }
            return 0;
          });
        }

        return [...newPrevFilteredSites].sort((a, b) => {
          const countA = a.products.length;
          const countB = b.products.length;
          if (countA !== countB) {
            return countB - countA;
          }
          const nameA = a.siteName.toUpperCase();
          const nameB = b.siteName.toUpperCase();
          if (nameA < nameB) {
            return -1;
          }
          if (nameA > nameB) {
            return 1;
          }
          return 0;
        });
      });

      setUpdatedProducts(prevProducts => {
        const updatedProducts = prevProducts.map(p => {
          if (
            p.icsProductId === product.icsProductId &&
            p.fpsSiteId === product.fpsSiteId
          ) {
            // pass string to preserve decimal places
            return { ...p, newFuelPrice: priceInString };
          }
          return p;
        });
        const isExistingProduct = prevProducts.some(
          p =>
            p.icsProductId === product.icsProductId &&
            p.fpsSiteId === product.fpsSiteId
        );
        if (!isExistingProduct) {
          // pass string to preserve decimal places
          updatedProducts.push({
            ...product,
            newFuelPrice: priceInString,
          });
        }

        return updatedProducts;
      });
    };

    const setPriceCardColor = (
      disabledColor: string,
      disabledColorIntensity: number,
      defaultColor: string
    ): string =>
      shouldPriceCardDisabled(product.price) ||
      !hasFuelPriceEditRole ||
      (hasFuelPriceEditRole && !hasFuelPriceApproverRole && isInvalidPrice)
        ? lightenColor(disabledColor, disabledColorIntensity)
        : defaultColor;

    const getPriceCardStyle = (
      disabledColor: string,
      disabledColorIntensity: number,
      defaultColor: string,
      disabledBgColor: string,
      disabledBgColorIntensity: number,
      defaultBgColor: string
    ) => ({
      color: setPriceCardColor(
        disabledColor,
        disabledColorIntensity,
        defaultColor
      ),
      backgroundColor: setPriceCardColor(
        disabledBgColor,
        disabledBgColorIntensity,
        defaultBgColor
      ),
      '&:hover': {
        backgroundColor: lightenColor(defaultBgColor, 50),
      },
    });

    const getColorStyle = useMemo(() => {
      let style = {
        color: theme.palette.common.grey,
        backgroundColor: theme.palette.common.white,
      };
      switch (true) {
        case product.productName === 'Regular':
          style = getPriceCardStyle(
            theme.palette.common.black,
            150,
            theme.palette.common.black,
            theme.palette.common.medRed,
            60,
            theme.palette.common.medRed
          );
          break;
        case product.productName === 'Premium':
          style = getPriceCardStyle(
            theme.palette.common.black,
            150,
            theme.palette.common.black,
            theme.palette.common.medBlack,
            60,
            theme.palette.common.medBlack
          );
          break;
        case product.productName === 'Diesel':
          style = getPriceCardStyle(
            theme.palette.common.black,
            150,
            theme.palette.common.black,
            theme.palette.common.medGreen,
            60,
            theme.palette.common.medGreen
          );
          break;
        case product.productName === 'Additive':
          style = getPriceCardStyle(
            theme.palette.common.black,
            150,
            theme.palette.common.black,
            theme.palette.common.medYellow,
            60,
            theme.palette.common.medYellow
          );
          break;
        case product.productName === 'US Ethanol Free':
          style = getPriceCardStyle(
            theme.palette.common.black,
            150,
            theme.palette.common.black,
            theme.palette.common.medBlue,
            60,
            theme.palette.common.medBlue
          );
          break;
        default:
          break;
      }
      return style;
    }, [product]);

    const [forceShowTooltip, setForceShowTooltip] = useState(false);
    const handleFocus = () => {
      if (errorMsg) {
        setForceShowTooltip(true);
        setTimeout(() => {
          setForceShowTooltip(false);
        }, 2500);
      }
    };

    const handleBlur = () => {
      setForceShowTooltip(false);
    };
    const isMobile = useIsMobile();

    return (
      <Card
        variant='outlined'
        sx={{
          width: '100%',
          minHeight: 132,
          borderRadius: 3,
          my: 1,
          boxSizing: 'border-box',
          ...getColorStyle,
        }}
      >
        <CardContent
          sx={{
            display: 'flex',
            flexDirection: 'column',

            height: '100%',
            '&:last-child': {
              paddingBottom: 2,
            },
          }}
        >
          <Box
            display='flex'
            justifyContent='space-between'
            alignItems='start'
            mb={1}
          >
            <Typography
              variant='h6'
              sx={{
                wordWrap: 'break-word',
                overflowWrap: 'break-word',
                maxWidth: '80px',
                fontSize: '16px',
              }}
              gutterBottom
            >
              {product.productName}
            </Typography>
            <Box
              display='flex'
              flexDirection='row'
              top={2}
              position='relative'
              alignItems='center'
              gap={1}
            >
              {PercentageComponent({ newPrice, product, isExceedThreshold })}
            </Box>
          </Box>
          <Box
            display='flex'
            flexDirection='row'
            justifyContent='space-between'
            alignSelf='auto'
          >
            <Box
              width='100%'
              display='flex'
              flexDirection='column'
              justifyContent='flex-start'
              alignItems='start'
              mt={0.5}
              pr={3.5}
              gap={0.15}
            >
              <Typography variant='body2' component='div'>
                Current:
              </Typography>
              <Typography variant='body2' component='div'>
                {product.price
                  ? `$ ${formatNumberWithSetDigits(product.price)}`
                  : ''}
              </Typography>
            </Box>
            <Box
              width='40%'
              display='flex'
              flexDirection='column'
              justifyContent='end'
              alignItems='flex-start'
            >
              <Typography variant='body2' component='div'>
                New:
              </Typography>

              <Tooltip
                title={product.hasPendingApproval ? '' : errorMsg}
                open={error || forceShowTooltip}
                arrow
                TransitionComponent={Zoom}
                enterDelay={0}
                leaveDelay={200}
                enterTouchDelay={0}
                leaveTouchDelay={3000}
                placement='bottom-start'
                componentsProps={{
                  tooltip: {
                    sx: {
                      bgcolor: error && 'red',
                      color: error && 'white',
                    },
                  },
                }}
              >
                <TextField
                  data-testid='pct-rc-new-price'
                  variant='filled'
                  size='small'
                  value={newPrice}
                  onChange={handlePriceChange}
                  autoComplete='off'
                  inputProps={{
                    'data-testid': 'pct-rc-new-price-input',
                    inputMode: isMobile ? 'decimal' : undefined,
                    style: {
                      fontSize: 14,
                      textAlign: 'right',
                      padding: 1,
                      color: getColorStyle.color,
                    },
                  }}
                  error={error}
                  disabled={
                    !hasFuelPriceEditRole ||
                    shouldPriceCardDisabled(product.price) ||
                    (hasFuelPriceEditRole &&
                      !hasFuelPriceApproverRole &&
                      !Number(product.price))
                  }
                  onFocus={handleFocus}
                  onBlur={handleBlur}
                />
              </Tooltip>
            </Box>
          </Box>
        </CardContent>
      </Card>
    );
  }
);

export default MobilePriceCard;
