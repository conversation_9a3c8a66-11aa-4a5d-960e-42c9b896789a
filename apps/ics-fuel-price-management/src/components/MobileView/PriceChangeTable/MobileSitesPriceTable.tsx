import React, { FC, ReactNode, useEffect, useMemo, useState } from 'react';
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  IconButton,
} from '@mui/material';
import { Virtuoso } from 'react-virtuoso';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';
import { formatDateWithLocaleAndTimezone } from '../../../utils/formatDateWithLocaleAndTimezone';
import { locale } from '../../../hooks/useTimeZoneUtils';
import { EnhancedSiteDetail } from '../../PriceChangeTable/types';
import MobileSitesHeader from '../Header/MobileSitesHeader';
import {
  COLUMN_HEADER_NAME,
  NO_DATA_MESSAGE,
  UPDATE_PRICE,
} from '../Constants';
import SiteAccordion from './SiteAccordion';

interface MobileSitesPriceTableProps {
  devicesData?: EnhancedSiteDetail[];
  onUpdatePrices?: () => void;
  onPrevPage: () => void;
  onNextPage: () => void;
  currentPage: number;
  totalPages: number;

  isUpdatePriceDisabled: boolean;
  isLoading: boolean;
  onOpenFilter: () => void;
  appliedFilterCount?: number;
}

type RenderSiteAccordionFn = (
  expanded: string | false,
  handleAccordionChange: (
    siteId: string
  ) => (event: React.SyntheticEvent, isExpanded: boolean) => void
) => (
  index: number,
  site: EnhancedSiteDetail & { lastUpdatedAt: string }
) => ReactNode;

export const renderSiteAccordion: RenderSiteAccordionFn =
  function renderSiteAccordionFn(expanded, handleAccordionChange) {
    return function renderAccordionItem(index, site) {
      const { fpsSiteId } = site;
      return (
        <SiteAccordion
          site={site}
          expanded={expanded === String(fpsSiteId)}
          onChange={handleAccordionChange(String(fpsSiteId))}
        />
      );
    };
  };

const PageNavFooter: FC<{
  onPrev: () => void;
  onNext: () => void;
  current: number;
  total: number;
  isBusy: boolean;
}> = ({ onPrev, onNext, current, total, isBusy }) => (
  <Box
    display='flex'
    justifyContent='center'
    alignItems='center'
    py={1}
    gap={2}
  >
    <IconButton
      size='small'
      onClick={onPrev}
      disabled={current === 1 || isBusy}
    >
      <ChevronLeft />
    </IconButton>

    <Typography variant='body2'>
      {current} / {total}
    </Typography>

    <IconButton
      size='small'
      onClick={onNext}
      disabled={current === total || isBusy}
    >
      <ChevronRight />
    </IconButton>
  </Box>
);

const MobileSitesPriceTable: FC<MobileSitesPriceTableProps> = ({
  devicesData,
  onUpdatePrices,
  onPrevPage,
  onNextPage,
  currentPage,
  totalPages,
  isUpdatePriceDisabled,
  isLoading,
  onOpenFilter,
  appliedFilterCount,
}) => {
  const [expanded, setExpanded] = useState<string | false>(false);
  const [initialLoadDone, setInitialLoadDone] = useState(false);
  useEffect(() => {
    if (!initialLoadDone && (devicesData?.length ?? 0) > 0) {
      setInitialLoadDone(true);
    }
  }, [devicesData, initialLoadDone]);

  const sitesWithFormattedDates = useMemo(() => {
    if (!devicesData) return [];
    return devicesData?.map(site => {
      const raw = site.products?.[0]?.updatedDate;
      const lastUpdatedAt = raw
        ? formatDateWithLocaleAndTimezone({
            date: new Date(`${raw}+00:00`),
            locale,
          })
        : 'N/A';
      return { ...site, lastUpdatedAt };
    });
  }, [devicesData]);

  if (!isLoading && devicesData?.length === 0) {
    return (
      <Box
        data-testid='pct-spt-box'
        display='flex'
        justifyContent='center'
        alignItems='center'
        height='100%'
      >
        <Typography data-testid='pct-spt-box-typography' variant='h6'>
          {NO_DATA_MESSAGE}
        </Typography>
      </Box>
    );
  }

  const handleAccordionChange =
    (siteId: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpanded(isExpanded ? siteId : false);
    };

  return (
    <Box
      data-testid='pct-mobile-root'
      display='flex'
      flexDirection='column'
      sx={{
        overflowY: 'scroll',
        height: '100vh',
        position: 'relative',
      }}
    >
      <MobileSitesHeader
        onOpenFilter={onOpenFilter}
        appliedFilterCount={appliedFilterCount}
      />

      <Box
        pl={2}
        py={1}
        sx={{
          backgroundColor: 'common.backgroundLight',
          borderBottom: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Typography variant='body1' sx={{ fontWeight: 600, py: 1 }}>
          {COLUMN_HEADER_NAME}
        </Typography>
      </Box>

      <Box flex={1} position='relative'>
        <Virtuoso
          data={sitesWithFormattedDates}
          itemContent={renderSiteAccordion(expanded, handleAccordionChange)}
          overscan={200}
        />

        {isLoading && (
          <Box
            position='absolute'
            top={0}
            left={0}
            right={0}
            bottom={0}
            display='flex'
            justifyContent='center'
            alignItems='center'
            bgcolor='common.backgroundLight'
            zIndex={5}
          >
            <CircularProgress size={24} />
          </Box>
        )}
      </Box>

      <Box
        display='flex'
        justifyContent='space-between'
        alignItems='center'
        gap={1}
        px={2}
        py={1}
        sx={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          bgcolor: '#fff',
          boxShadow: 2,
        }}
      >
        <PageNavFooter
          current={currentPage}
          total={totalPages}
          isBusy={isLoading}
          onPrev={onPrevPage}
          onNext={onNextPage}
        />

        <Button
          variant='contained'
          color='primary'
          onClick={onUpdatePrices}
          size='small'
          disabled={isUpdatePriceDisabled}
          sx={{ flexGrow: 1, ml: 1 }}
        >
          {UPDATE_PRICE}
        </Button>
      </Box>
    </Box>
  );
};

export default MobileSitesPriceTable;
