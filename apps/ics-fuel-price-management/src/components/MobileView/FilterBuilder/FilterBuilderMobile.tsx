import React, {
  FC,
  memo,
  useState,
  useEffect,
  useCallback,
  useMemo,
  SyntheticEvent,
  HTMLAttributes,
  useRef,
} from 'react';
import {
  Box,
  Button,
  TextField,
  MenuItem,
  Chip,
  Autocomplete,
  Card,
  CardHeader,
  CardContent,
  Drawer,
  Typography,
  Popper,
  PopperProps,
} from '@mui/material';
import ClearIcon from '@mui/icons-material/Clear';
import CachedIcon from '@mui/icons-material/Cached';
import DoneIcon from '@mui/icons-material/Done';
import AddIcon from '@mui/icons-material/Add';
import FilterAltOutlinedIcon from '@mui/icons-material/FilterAltOutlined';
import { cloneDeep, debounce, isEmpty } from 'lodash';
import {
  FILTER_BUILDER_TITLE,
  FILTER_BUILDER_CANCEL_BUTTON_TEXT,
  FILTER_BUILDER_RESET_BUTTON_TEXT,
  FILTER_BUILDER_APPLY_BUTTON_TEXT,
  FILTER_BUILDER_ADD_FILTERS_BUTTON_TEXT,
  FILTER_BUILDER_ACTIONS,
  FILTER_BUILDER_VALUE_TEXTFIELD_TEXTS,
  FILTER_BUILDER_VARIABLE_SELECTOR_LABEL,
  FILTER_BUILDER_OPERATOR_SELECTOR_LABEL,
  FILTER_BUILDER_VALUE_SELECTOR_LABEL,
} from '../../FilterBuilder/contants';
import {
  FilterBuilderProps,
  VariableFilterOption,
  OperatorFilterOption,
  ValueFilterOption,
  AppliedFilters,
} from '../../FilterBuilder/types';
import { OnDeleteCurrentFilterProps } from '../../FilterBuilder/FilterChips/types';
import FilterChipsMobile from './FilterChipsMobile';

const CustomPopper = (props: PopperProps) => (
  <Popper {...props} placement='bottom-start' disablePortal />
);

const FilterBuilderMobile: FC<FilterBuilderProps> = memo(
  ({
    isOpen,
    variableFilterOptions,
    operatorsByVariable,
    onCloseFilterBuilder,
    useGetFilterValues,
    appliedFilters,
    freeSoloVariables,
    title,
    variableSelectorLabel,
    operatorSelectorLabel,
    valueSelectorLabel,
    addFiltersBtnText,
    addFiltersBtnIcon,
    cancelBtnText,
    cancelBtnIcon,
    resetBtnText,
    resetBtnIcon,
    applyBtnText,
    applyBtnIcon,
  }) => {
    const [selectedVariableFilter, setSelectedVariableFilter] =
      useState<string>('');

    const [operatorFilterOptions, setOperatorFilterOptions] = useState<
      OperatorFilterOption[]
    >([]);

    const [valueFilterOptions, setValueFilterOptions] = useState<
      ValueFilterOption[]
    >([]);

    const [selectedOperatorFilter] = useState<string>('$in');

    const [selectedValueFilter, setSelectedValueFilter] = useState<
      ValueFilterOption[]
    >([]);

    const [currentFilters, setCurrentFilters] = useState<
      AppliedFilters | undefined
    >(undefined);

    const [pageIndex, setPageIndex] = useState<number>(0);
    const [totalResults, setTotalResults] = useState<number>(0);
    const [hasSearchTextChanged, setHasSearchTextChanged] =
      useState<boolean>(false);

    const [searchText, setSearchText] = useState<string>('');

    const requestingPage = useRef<boolean>(false);
    const initializeSearchText = useRef<boolean>(false);

    const {
      data: valueFilterOptionsResponse,
      isFetching: isValueFilterOptionsFetching,
      refetch: getValueFilterOptions,
    } = useGetFilterValues({
      variableFilterValue: selectedVariableFilter,
      pageIndex,
      pageSize: 25,
      searchText,
      queryOpts: {
        queryKey: [
          'useGetFilterValues',
          selectedVariableFilter,
          pageIndex,
          searchText,
        ],
        enabled: false,
      },
    });

    useEffect(() => {
      setCurrentFilters(appliedFilters);
    }, []);

    useEffect(() => {
      if (selectedVariableFilter) {
        setOperatorFilterOptions(
          operatorsByVariable?.[selectedVariableFilter] || []
        );
        if (
          !freeSoloVariables?.find(
            freeSoloVariable => freeSoloVariable === selectedVariableFilter
          )
        ) {
          if (pageIndex !== 0) {
            setPageIndex(0);
          }
          if (searchText) {
            setSearchText('');
          }
          getValueFilterOptions();
          setSelectedValueFilter([]);
          initializeSearchText.current = true;
        } else {
          setValueFilterOptions([]);
        }
      }
    }, [selectedVariableFilter]);

    useEffect(() => {
      if (valueFilterOptionsResponse) {
        if (hasSearchTextChanged || initializeSearchText.current) {
          setValueFilterOptions(valueFilterOptionsResponse.results);
          setHasSearchTextChanged(false);
          initializeSearchText.current = false;
        } else {
          setValueFilterOptions([
            ...(valueFilterOptions || []),
            ...valueFilterOptionsResponse.results,
          ]);
        }
        setTotalResults(
          valueFilterOptionsResponse.resultsMetadata.totalResults
        );
        requestingPage.current = false;
      }
    }, [valueFilterOptionsResponse]);

    useEffect(() => {
      getValueFilterOptions();
    }, [pageIndex, hasSearchTextChanged]);

    const isFreeSolo = !!freeSoloVariables?.find(
      freeSoloVariable => freeSoloVariable === selectedVariableFilter
    );

    useEffect(() => {
      if (!isFreeSolo) {
        setHasSearchTextChanged(true);
        setPageIndex(0);
      }
    }, [searchText]);

    const handleVariableFilterChange = ({ target: { value } }) => {
      setSelectedVariableFilter(value);
    };

    const handleValueFilterChange = (
      _: any,
      selectedOptions: (ValueFilterOption | string)[]
    ) => {
      const newFilterOptions: ValueFilterOption[] = selectedOptions.map(
        (option, index) =>
          typeof option === 'string'
            ? { key: `${option}-${index}`, value: option, label: option }
            : option
      );
      setSelectedValueFilter(newFilterOptions);
    };

    const handleAddFiltersClick = () => {
      if (
        !currentFilters ||
        !currentFilters?.[selectedVariableFilter]?.[selectedOperatorFilter]
      ) {
        setCurrentFilters({
          ...(currentFilters || {}),
          [selectedVariableFilter]: {
            [selectedOperatorFilter]: selectedValueFilter,
          },
        });
      } else {
        const newCurrentFilters = cloneDeep(currentFilters);
        const stringifiedCurrentFilters = newCurrentFilters[
          selectedVariableFilter
        ][selectedOperatorFilter].map(valueFilter =>
          JSON.stringify(valueFilter)
        );
        const stringifiedSelectedValueFilters = selectedValueFilter.map(
          valueFilter => JSON.stringify(valueFilter)
        );
        const finalValueFilters = [
          ...new Set([
            ...stringifiedCurrentFilters,
            ...stringifiedSelectedValueFilters,
          ]),
        ];
        if (finalValueFilters.length) {
          newCurrentFilters[selectedVariableFilter][selectedOperatorFilter] =
            finalValueFilters.map(valueFilter => JSON.parse(valueFilter));
          setCurrentFilters(newCurrentFilters);
        }
      }
      setSelectedValueFilter([]);
      setSelectedVariableFilter('');
      setPageIndex(0);
      setTotalResults(0);
      setSearchText('');
      setHasSearchTextChanged(false);
    };

    const handleDeleteCurrentFilter = ({
      variableFilterValue,
      operatorFilterValue,
      valueFilterValue,
    }: OnDeleteCurrentFilterProps) => {
      const newCurrentFilters = cloneDeep(currentFilters);
      const newFilters = newCurrentFilters[variableFilterValue][
        operatorFilterValue
      ].filter(({ value }) => value !== valueFilterValue);
      if (!newFilters.length) {
        Object.keys(newCurrentFilters[variableFilterValue]).length > 1
          ? delete newCurrentFilters[variableFilterValue][operatorFilterValue]
          : delete newCurrentFilters[variableFilterValue];
      } else {
        newCurrentFilters[variableFilterValue][operatorFilterValue] =
          newFilters;
      }
      setCurrentFilters(
        isEmpty(newCurrentFilters) ? undefined : newCurrentFilters
      );
    };

    const handleInputChange = debounce((_: any, value: string, __: any) => {
      if (!isFreeSolo) {
        setSearchText(value);
      }
    }, 1000);

    const hasNextPage = useMemo(
      (): boolean => totalResults > (valueFilterOptions?.length || 0),
      [totalResults, valueFilterOptions?.length]
    );

    const isScrollComplete = useCallback(
      (event: React.SyntheticEvent): boolean => {
        const listboxNode = event.currentTarget;
        // a tollerance of 5 pixels in the calculation
        return (
          Math.abs(
            listboxNode.scrollHeight -
              listboxNode.clientHeight -
              listboxNode.scrollTop
          ) < 5
        );
      },
      []
    );

    const onScrollAutocomplete = useCallback(
      (event: SyntheticEvent) => {
        if (
          hasNextPage &&
          !isValueFilterOptionsFetching &&
          isScrollComplete(event)
        ) {
          if (!requestingPage.current) {
            requestingPage.current = true;
            setPageIndex(pI => pI + 1);
          }
        }
      },
      [hasNextPage, isValueFilterOptionsFetching, isScrollComplete]
    );

    const renderOptions = (
      props: HTMLAttributes<HTMLLIElement>,
      { key, label }: Partial<ValueFilterOption>
    ) => (
      <li {...props} key={key}>
        {label}
      </li>
    );

    const handleCancel = () => {
      setSelectedVariableFilter('');
      setSelectedValueFilter([]);
      setPageIndex(0);
      setTotalResults(0);
      setSearchText('');
      setHasSearchTextChanged(false);
      onCloseFilterBuilder({ action: FILTER_BUILDER_ACTIONS.cancel });
    };

    useEffect(() => {
      if (isOpen) {
        setCurrentFilters(appliedFilters);
        setSelectedVariableFilter('');
        setSelectedValueFilter([]);
        setPageIndex(0);
        setTotalResults(0);
        setSearchText('');
        setHasSearchTextChanged(false);
      }
    }, [isOpen]);

    const getTotalFilterCount = (filters?: AppliedFilters): number => {
      if (!filters) return 0;

      return Object.values(filters).reduce(
        (total, operatorMap) =>
          total +
          Object.values(operatorMap).reduce(
            (count, filterArray) => count + filterArray.length,
            0
          ),
        0
      );
    };

    return (
      <Drawer
        anchor='right'
        open={isOpen}
        onClose={handleCancel}
        PaperProps={{
          sx: {
            width: '100vw',
            maxWidth: '100vw',
            height: '100vh',
            maxHeight: '100vh',
            backgroundColor: '#F7F7F7',
          },
        }}
      >
        <Box
          sx={{
            p: 2,
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            backgroundColor: '#F7F7F7',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Typography variant='h6' fontWeight='bold'>
              {title || FILTER_BUILDER_TITLE}
            </Typography>
            <Button
              onClick={() =>
                onCloseFilterBuilder({ action: FILTER_BUILDER_ACTIONS.cancel })
              }
            >
              <ClearIcon />
            </Button>
          </Box>
          <Box sx={{ flex: 1, overflowY: 'auto', pb: '120px', pt: 1 }}>
            <TextField
              label={
                variableSelectorLabel || FILTER_BUILDER_VARIABLE_SELECTOR_LABEL
              }
              select
              value={selectedVariableFilter}
              onChange={handleVariableFilterChange}
              fullWidth
              sx={{
                mb: 2,
                '& .MuiInputLabel-outlined.MuiInputLabel-shrink': {
                  transform: 'translate(14px, -7px) scale(0.75)',
                  px: 0.5,
                },
              }}
            >
              {variableFilterOptions.map(
                ({ value, label }: VariableFilterOption) => (
                  <MenuItem key={value} value={value}>
                    {label}
                  </MenuItem>
                )
              )}
            </TextField>
            <TextField
              label={
                operatorSelectorLabel || FILTER_BUILDER_OPERATOR_SELECTOR_LABEL
              }
              select
              fullWidth
              sx={{ mb: 2 }}
              defaultValue={selectedOperatorFilter}
              SelectProps={{ disabled: !selectedVariableFilter }}
            >
              {operatorFilterOptions.map(
                ({ value, label }: OperatorFilterOption) => (
                  <MenuItem key={value} value={value}>
                    {label}
                  </MenuItem>
                )
              )}
            </TextField>
            <Autocomplete
              id='fb-value-selector'
              data-testid='fb-value-selector'
              loading={isValueFilterOptionsFetching}
              value={selectedValueFilter}
              multiple
              freeSolo={isFreeSolo}
              disableCloseOnSelect
              options={valueFilterOptions}
              renderOption={renderOptions}
              renderTags={(currentValueOptions, getTagProps) => (
                <Box
                  sx={{
                    maxHeight: '100px',
                    overflowY: 'auto',
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: 1,
                    p: 1,
                  }}
                >
                  {currentValueOptions.map(({ value, label }, index) => (
                    <Chip
                      key={value}
                      data-testid={`fb-value-selector-chip-${value}`}
                      variant='outlined'
                      label={label}
                      {...getTagProps({ index })}
                      sx={{ borderRadius: 2 }}
                    />
                  ))}
                </Box>
              )}
              disabled={!selectedVariableFilter}
              renderInput={params => (
                <TextField
                  {...params}
                  data-testid='fb-value-selector-textfield'
                  variant='filled'
                  fullWidth
                  inputProps={{
                    'data-testid': 'fb-value-selector-select',
                    ...params.inputProps,
                  }}
                  label={
                    valueSelectorLabel || FILTER_BUILDER_VALUE_SELECTOR_LABEL
                  }
                  placeholder={
                    isFreeSolo
                      ? FILTER_BUILDER_VALUE_TEXTFIELD_TEXTS.typeAndPressEnter
                      : FILTER_BUILDER_VALUE_TEXTFIELD_TEXTS.searchAndSelect
                  }
                />
              )}
              ListboxProps={{
                onScroll: onScrollAutocomplete,
              }}
              PopperComponent={CustomPopper}
              onInputChange={handleInputChange}
              onChange={handleValueFilterChange}
              getOptionLabel={({ label }: VariableFilterOption) => label}
            />

            <Button
              variant='contained'
              startIcon={
                addFiltersBtnIcon || (
                  <AddIcon sx={{ width: '24px', height: '24px' }} />
                )
              }
              sx={{ mb: 2 }}
              disabled={!selectedValueFilter.length}
              onClick={handleAddFiltersClick}
            >
              {addFiltersBtnText || FILTER_BUILDER_ADD_FILTERS_BUTTON_TEXT}
            </Button>
            {currentFilters && (
              <Card
                sx={{
                  border: '2px solid #e9e9e9',
                  boxShadow: 'none',
                  borderRadius: 2,
                }}
              >
                <CardHeader
                  title={
                    <Typography
                      variant='h6'
                      fontWeight='bold'
                      fontSize='1.6rem'
                    >
                      {`Current Filters (${getTotalFilterCount(currentFilters)} total)`}
                    </Typography>
                  }
                  avatar={<FilterAltOutlinedIcon />}
                />
                <CardContent>
                  <FilterChipsMobile
                    variableFilterOptions={variableFilterOptions}
                    operatorsByVariable={operatorsByVariable}
                    currentFilters={currentFilters}
                    onDeleteCurrentFilter={handleDeleteCurrentFilter}
                  />
                </CardContent>
              </Card>
            )}
          </Box>
          <Box
            sx={{
              position: 'fixed',
              bottom: 0,
              left: 0,
              width: '100%',
              p: 2,
              display: 'flex',
              gap: 1,
              mt: 2,
              zIndex: 1300,
              backgroundColor: '#f7f7f7',
              boxShadow: '0 -2px 8px rgba(0,0,0,0.05)',
            }}
          >
            <Button
              variant='outlined'
              startIcon={cancelBtnIcon || <ClearIcon />}
              fullWidth
              onClick={handleCancel}
            >
              {cancelBtnText || FILTER_BUILDER_CANCEL_BUTTON_TEXT}
            </Button>

            <Button
              variant='outlined'
              startIcon={resetBtnIcon || <CachedIcon />}
              fullWidth
              disabled={!currentFilters}
              onClick={() => setCurrentFilters(undefined)}
            >
              {resetBtnText || FILTER_BUILDER_RESET_BUTTON_TEXT}
            </Button>
            <Button
              variant='contained'
              disabled={!currentFilters && !appliedFilters}
              startIcon={applyBtnIcon || <DoneIcon />}
              fullWidth
              onClick={() =>
                onCloseFilterBuilder({
                  action: FILTER_BUILDER_ACTIONS.apply,
                  newFilters: currentFilters,
                })
              }
            >
              {applyBtnText || FILTER_BUILDER_APPLY_BUTTON_TEXT}
            </Button>
          </Box>
        </Box>
      </Drawer>
    );
  }
);

export default FilterBuilderMobile;
