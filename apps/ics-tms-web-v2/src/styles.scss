/* TO-DO : Import Bootstrap SCSS file once the migration is complete */
@import '~@ng-select/ng-select/themes/default.theme.css';
@import './assets/scss/ics-dropdown.scss';
@import './assets/scss/color.scss';
@import './assets/scss/custom.scss';
@import './assets/scss/ics-icons.scss';
@import './assets/scss/label.scss';
// Isolating Boostrap v5.3.3
@import './assets/css/bootstrap-iso.css';

// TO-DO : Fix this import as it is causing build error
// @import "../../ics-playlist/src/assets/ics-playlist.css";
.is-view-full {
  .mfe-container.col-main {
    padding: 0 !important;
    margin-left: 0 !important;
    width: 100% !important;
  }

  .mfe-container.global-menu-open:not(.no-body-padding) .col-main {
    position: relative !important;
    padding: 0px !important;
  }

  @media (min-width: 768px), (min-width: 992px) {
    .mfe-container.global-menu-open:not(.no-body-padding) .col-main {
      width: 100% !important;
      margin: 0 !important;
    }
  }
}

.bootstrap-iso {
  html,
  body {
    height: 100vh;
    font-size: 10px !important;
    background-color: var(--md-blue-grey-500) !important;
    min-height: 100%;
    font-family:
      'Roboto',
      'Helvetica Neue',
      -apple-system,
      BlinkMacSystemFont,
      Arial,
      sans-serif,
      system-ui !important;
    margin: 0;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    padding-top: 6rem;
  }

  .modal-sm {
    --bs-modal-width: 40rem;
  }

  .modal-lg {
    --bs-modal-width: 62rem;
  }

  .modal-xl {
    --bs-modal-width: 90rem;
  }

  .modal-content {
    padding: 0.3rem !important;
    border-radius: 1rem !important;
  }

  a {
    text-decoration: none !important;
    &:focus,
    &:focus-visible {
      outline: none;
    }
  }

  @media (min-width: 992px) {
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12 {
      float: none;
    }
  }

  .visible-xs-block {
    display: none;
  }

  @media (max-width: 767px) {
    .hidden-xs {
      display: none !important;
    }

    .visible-xs-block {
      display: block !important;
    }
  }

  .max-width-1200 {
    max-width: 1200px;
  }

  ::placeholder {
    /* Chrome, Firefox, Opera, Safari 10.1+ */
    font-size: 1.4rem;
    opacity: 0;
    color: var(--md-grey-1000);
    /* Firefox */
  }

  :-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    font-size: 1.4rem;
    color: var(--md-grey-1000);
  }

  ::-ms-input-placeholder {
    /* Microsoft Edge */
    font-size: 1.4rem;
    color: var(--md-grey-1000);
  }

  input,
  select {
    height: 3.4rem;
  }

  .dropdown-menu {
    --bs-dropdown-link-active-bg: var(--md-dark-blue-100);
    --bs-dropdown-link-hover-bg: var(--md-dark-blue-300);
    --bs-dropdown-link-hover-color: var(--white);
  }

  .form-control {
    padding: 0.6rem 1.2rem;

    &:focus {
      box-shadow: inset 0 0 0 1px var(--md-blue-75);
    }
  }

  input.form-control {
    &::placeholder {
      color: #999;
    }

    &:focus {
      outline: none;
      border: 1px solid;
      border-radius: 3px;
      border-color: var(--md-blue-75);
    }
  }

  .modal.modal-confirm-create-copy-download,
  .modal.modal-multi-factor-authentication {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;
  }

  .stepper.file-downloads-stepper {
    .mat-step {
      cursor: default;

      .mat-step-header {
        cursor: pointer;
      }

      .mat-step-header[aria-labelledby='cursor-pointer'] {
        cursor: pointer;
      }

      .mat-step-header[aria-labelledby='cursor-not-allowed'] {
        cursor: not-allowed;
      }
    }
  }

  .stepper.offline-packages-stepper {
    .mat-step-label:not(.mat-step-label-active),
    .mat-step-icon:not(.mat-step-label-active) {
      cursor: pointer;
    }

    .mat-step-icon.mat-step-icon-state-edit,
    .mat-step-icon.mat-step-icon-selected {
      cursor: pointer;
    }

    .mat-step-label:not(.mat-step-label-active),
    .mat-step-icon:not(.mat-step-label-active) {
      cursor: not-allowed;
    }
  }

  .modal.ics-bulk-actions-modal {
    --bs-modal-margin: 30px auto !important;

    .modal-dialog {
      width: 603px;
    }

    .modal-content {
      padding: 0 !important;
    }
  }

  .common-details-popup {
    .modal-dialog {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .modal-content {
      width: 90rem;
    }
  }

  @media (min-width: 768px) {
    .modal-dialog {
      margin: 30px auto;
    }
  }

  .cancel-bulk-operation-modal {
    .modal-content {
      padding: 0 !important;
      margin-top: 50%;
      border: none;
      border-radius: 2px !important;
    }
  }
}

/* Common Stepper */
.stepper {
  border-radius: 0.3rem;

  .mat-step {
    cursor: pointer;
  }

  html,
  body {
    height: 100vh;
    font-size: 10px !important;
    background-color: var(--md-blue-grey-500) !important;
    min-height: 100%;
    font-family:
      'Roboto',
      'Helvetica Neue',
      -apple-system,
      BlinkMacSystemFont,
      Arial,
      sans-serif,
      system-ui !important;
    margin: 0;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .mat-step-icon {
    --mat-stepper-header-icon-background-color: var(--md-grey-500);

    width: 2.8rem;
    height: 2.8rem;
    font-size: 1.4rem;
  }

  .check-circle {
    border-radius: 50% !important;
    position: absolute;
    transform: translate(-50%, -50%);
    color: rgb(0, 185, 0);
    background-color: var(--white);
    font-size: 2.8rem !important;
    width: 2.8rem !important;
    height: 2.8rem !important;
  }

  .cdk-keyboard-focused,
  .cdk-program-focused,
  .cdk-mouse-focused {
    background-color: transparent !important;
  }

  .mat-step-header[aria-labelledby='disable-mat-step'] {
    pointer-events: none;
  }

  .disable-offline-package-step {
    cursor: not-allowed !important;
  }

  .dropdown-menu {
    --bs-dropdown-link-active-bg: var(--md-dark-blue-100);
    --bs-dropdown-link-hover-bg: var(--md-dark-blue-300);
    --bs-dropdown-link-hover-color: var(--white);
  }

  .custom-pagination {
    padding: 3rem 0;

    &:active {
      background-color: var(--white) !important;
    }
  }

  .mat-step-label {
    font-size: 1.8rem;

    .mat-step-text-label {
      color: var(--black);
    }
  }

  .mat-step-icon-state-edit {
    // ! change this to see why circle is not fitting
    background-color: transparent;
  }

  .mat-step-icon {
    --mat-stepper-header-icon-background-color: var(--md-grey-500);

    width: 2.8rem;
    height: 2.8rem;
    font-size: 1.4rem;
  }

  .check-circle {
    border-radius: 50% !important;
    position: absolute;
    transform: translate(-50%, -50%);
    color: rgb(0, 185, 0);
    background-color: var(--white);
    font-size: 2.8rem !important;
    width: 2.8rem !important;
    height: 2.8rem !important;
  }

  // when moved to next step, remove bg color
  .cdk-keyboard-focused,
  .cdk-program-focused,
  .cdk-mouse-focused {
    background-color: transparent !important;
  }

  .mat-step-header {
    box-sizing: border-box;

    &:hover:not([aria-disabled]),
    &:hover[aria-disabled='false'] {
      background: transparent !important;
    }

    &:active {
      background-color: var(--white) !important;
    }

    .mat-step-label {
      font-size: 1.8rem;

      .mat-step-text-label {
        color: var(--black);
      }
    }

    .mat-ripple {
      display: none !important;
    }
  }

  .mat-vertical-stepper-header {
    height: auto;
    padding-bottom: 0;
    gap: 0.8rem;
  }

  .mat-stepper-vertical-line::before {
    display: none;
  }

  .mat-vertical-content {
    padding-bottom: 2rem;
    padding-left: 3.2rem;
  }

  // all steps visible by default
  .mat-vertical-stepper-content {
    visibility: visible !important;
    padding-top: 1.2rem;
    height: auto !important;
    box-shadow: 0 0.1rem 0.1rem rgba(0, 0, 0, 0.05);
    border-left: 0.1rem solid var(--color-white-shade-three);
    border-radius: 0.3rem;
    border-top-left-radius: 0;
    margin-left: 0.1rem;
    overflow: visible;
  }

  .d-none {
    display: none !important;
  }

  .t-underline {
    &:hover {
      text-decoration: underline;
      cursor: pointer;
    }
  }

  .d-visible {
    display: block !important;
  }

  .data-container {
    font-size: 1.3rem;
    font-weight: 700;
    margin-left: 0.288rem;
  }

  .step-btn-container {
    margin-top: 2rem;

    button:disabled {
      cursor: not-allowed;
      pointer-events: all;
    }
  }

  .button-container {
    padding: 0.5rem 2.4rem 2rem;
    margin-left: 3.6rem;
    margin-top: 1.6rem;

    button:last-child {
      margin-left: 1.5rem;
    }
  }

  .selected-data-container {
    margin-left: 0.288rem;

    .selected {
      display: flex;
      align-items: center;
      gap: 1.6rem;

      > span {
        &:first-child {
          color: var(--color-icon-default);
          font-size: 1.2rem;
          font-weight: 600;
        }

        &:last-child {
          font-size: 1.4rem;
        }
      }

      > p {
        font-size: 1.4rem;
        margin: 0;
      }
    }
  }

  .toaster {
    background-color: var(--color-toast-bg);
    color: var(--white);
    min-width: 45.6rem;
    width: fit-content;
  }

  .tooltip {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    font-family:
      'Roboto',
      'Helvetica Neue',
      -apple-system,
      BlinkMacSystemFont,
      Arial,
      sans-serif,
      system-ui;

    &.show {
      opacity: 0.9;
    }

    .tooltip-arrow {
      --bs-tooltip-bg: var(--tooltip-bg-color);
    }

    .tooltip-inner {
      font-size: 1.1rem;
      padding: 0.3rem 0.8rem;
      background-color: var(--md-grey-1000);
      max-width: 40rem;
    }
  }

  .btn {
    font-size: 1.4rem !important;
    font-weight: 500;
    line-height: 1.428;
    display: inline-block;
    margin-bottom: 0;
    cursor: pointer;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
    border: 0.1rem solid transparent;
    border-radius: 0.3rem;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    padding: 0.6rem 1.2rem;
  }

  .btn-primary:hover {
    color: var(--white);
    border-color: var(--md-dark-blue-300);
    background-color: var(--md-dark-blue-100) !important;
  }

  .ng-dropdown-panel {
    z-index: 5 !important;
    font-size: 1.4rem;
  }

  .ng-select.ng-select-opened .ng-select-container {
    z-index: 4 !important;
  }

  .ng-value-container {
    font-size: 1.4rem;
  }

  .well {
    min-height: 2rem;
    margin-bottom: 2rem;
    padding: 1.9rem;
    border: 0.1rem solid var(--md-red-grey-600);
    border-radius: 0.3rem;
    background-color: var(--color-bg-fa);
    box-shadow: inset 0 0.1rem 0.1rem rgba(0, 0, 0, 0.05);
    font-size: 1.4rem;

    .page-header {
      margin: 4rem 0 2rem;
      padding-bottom: 0.9rem;
      border-bottom: 0.1rem solid var(--md-red-grey-600);
      font-size: 2.6rem;
      font-weight: 400;
      margin-top: 0;
    }
  }

  @media (min-width: 992px) {
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12 {
      float: none;
    }
  }

  .visible-xs-block {
    display: none;
  }

  // all steps visible by default
  .mat-vertical-stepper-content {
    visibility: visible !important;
    padding-top: 1.5rem;
    height: auto !important;
    box-shadow: 0 0.1rem 0.1rem var(--md-black-5);
    border-left: 0.1rem solid var(--md-blue-grey-400);
    border-radius: 0.3rem;
    border-top-left-radius: 0;
    margin-left: 0.1rem;
    overflow: visible;
  }
}

.d-none {
  display: none !important;
}

.t-underline {
  &:hover {
    text-decoration: underline;
    cursor: pointer;
  }
}

.d-visible {
  display: block !important;
}

.data-container {
  font-size: 1.3rem;
  font-weight: 700;
  margin-left: 0.288rem;
}

.step-btn-container {
  button:disabled {
    cursor: not-allowed;
    pointer-events: all;
  }
}

.button-container {
  padding: 0.5rem 2.4rem 2rem;
  margin-left: 3.6rem;

  button:last-child {
    margin-left: 1.5rem;
  }
}

.selected-data-container {
  margin-left: 0.288rem;

  .selected {
    display: flex;
    align-items: center;
    gap: 1.6rem;

    > span {
      &:first-child {
        color: var(--md-grey-800);
        font-size: 1.2rem;
        font-weight: 600;
      }

      &:last-child {
        font-size: 1.4rem;
      }
    }

    > p {
      font-size: 1.4rem;
      margin: 0;
    }
  }
}

.toaster {
  background-color: var(--md-red-grey-1400);
  color: var(--white);
  min-width: 45.6rem;
  width: fit-content;
}

.tooltip {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;

  &.show {
    opacity: 0.9 !important;
  }

  .tooltip-arrow {
    --bs-tooltip-bg: var(--md-red-grey-1300);
  }

  .tooltip-inner {
    font-size: 1.1rem;
    padding: 0.3rem 0.8rem;
    background-color: var(--md-grey-1000);
    max-width: 40rem;
  }
}

.ng-dropdown-panel {
  z-index: 5 !important;
  font-size: 1.4rem;
}

.ng-select.ng-select-opened .ng-select-container {
  z-index: 4 !important;
}

.ng-value-container {
  font-size: 1.4rem;
}

.well {
  min-height: 2rem;
  margin-bottom: 2rem;
  padding: 1.9rem;
  border: 0.1rem solid var(--md-red-grey-600);
  border-radius: 0.3rem;
  background-color: var(--md-grey-50);
  box-shadow: inset 0 0.1rem 0.1rem var(--md-black-5);
  font-size: 1.4rem;

  .well {
    min-height: 2rem;
    margin-bottom: 2rem;
    padding: 1.9rem;
    border: 0.1rem solid var(--md-red-grey-600);
    border-radius: 0.3rem;
    background-color: var(--color-bg-fa);
    box-shadow: inset 0 0.1rem 0.1rem rgba(0, 0, 0, 0.05);
    font-size: 1.4rem;

    .page-header {
      margin: 4rem 0 2rem;
      padding-bottom: 0.9rem;
      border-bottom: 0.1rem solid var(--md-red-grey-600);
      font-size: 2.6rem;
      font-weight: 400;
      margin-top: 0;
    }
  }

  .page-header {
    margin: 4rem 0 2rem;
    padding-bottom: 0.9rem;
    border-bottom: 0.1rem solid var(--md-red-grey-600);
    font-size: 2.6rem;
    font-weight: 400;
    margin-top: 0;
  }
}

/* single date picker */

.single-datepicker {
  border: none;
  padding: 1rem;
  padding-bottom: 0;
  box-shadow:
    0 0.1rem 0.3rem 0 var(--md-black-20),
    0 0.1rem 0.8rem 0 var(--md-black-14),
    0 0.2rem 0.1rem -0.1rem var(--md-black-12);
  z-index: 1;

  .ngb-dp-header {
    background: transparent;
    margin-bottom: 0.4rem;

    .ngb-dp-month-name {
      background-color: transparent;
      font-size: 1.2rem;
    }

    .ngb-dp-arrow-prev,
    .ngb-dp-arrow-next {
      .ngb-dp-arrow-btn {
        .ngb-dp-navigation-chevron {
          width: 0 !important;
          height: 0 !important;
          border-style: inherit;
          margin-left: -0.4rem;

          transform: rotate(-90deg);
          border-left: 0.6rem solid transparent;
          border-right: 0.6rem solid transparent;
          border-bottom: 0.6rem solid var(--md-grey-800);

          &:hover {
            border-bottom: 0.6rem solid var(--black);
          }
        }
      }
    }

    .ngb-dp-arrow-next {
      justify-content: center;

      .ngb-dp-arrow-btn {
        margin: 0;
        padding: 0;

        .ngb-dp-navigation-chevron {
          transform: rotate(90deg);
          margin-left: 3.2rem;
        }
      }
    }
  }

  .ngb-dp-arrow-btn {
    padding: 0 0.25rem !important;
    margin: 0 0.5rem;
    border: none !important;
    background-color: transparent;
    z-index: 1;
  }

  .ngb-dp-content .ngb-dp-month {
    .ngb-dp-week {
      background-color: transparent;
      margin-bottom: 0.5rem;
      font-size: 1.2rem;

      .ngb-dp-weekday {
        font-style: normal;
        color: var(--md-grey-800);
      }

      .ngb-dp-today {
        font-weight: 600;
      }

      .ngb-dp-day {
        width: 100%;
        display: flex;
        justify-content: center;

        &.disabled {
          cursor: not-allowed !important;
          pointer-events: inherit !important;
          color: var(--md-grey-600);
        }

        div {
          padding: 0 0.6rem 0 0.7rem;
          width: 100%;

          &.bg-primary {
            background-color: var(--md-indigo-600) !important;
          }

          &:hover:not(.bg-primary) {
            background-color: var(--md-blue-grey-500) !important;
            outline: none !important;
          }
        }
      }

      .ngb-dp-day.hidden {
        display: flex !important;
      }
    }
  }

  $days: 'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat';

  @for $i from 1 through length($days) {
    .ngb-dp-weekday:nth-child(#{$i}) {
      visibility: hidden;
      width: 100%;
      font-size: 1rem;

      &::before {
        visibility: visible;
        content: nth($days, $i);
      }
    }
  }
}

.rki-stepper {
  .mat-step-header:hover[aria-disabled='true'] {
    cursor: not-allowed;
  }
}

.modal.modal-primary-fee {
  .modal-sm {
    width: 370px;

    .modal-content {
      margin-top: 50%;
      border-radius: 2px !important;
    }
  }
}

.modal.modal-primary {
  .modal-sm {
    width: 400px;

    .modal-content {
      margin-top: 50%;
      border-radius: 2px !important;
    }
  }
}

.rki-request-status-alert {
  background-color: white;
  padding: 1px 3px 2px 3px;
  margin: 0 4px 0px -5px;

  .info-tooltip {
    white-space: pre-line;

    .tooltip-arrow {
      left: -1px !important;
      top: 21px !important;
    }

    .tooltip-inner {
      margin-bottom: 8px;
      font-size: 12px;
      line-height: 1.43;
      text-align: left;
    }
  }
}

.overflow-visible {
  padding-right: 0 !important;
  overflow: visible !important;
}
