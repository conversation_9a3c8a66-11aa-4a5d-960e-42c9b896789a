.reset-password-container {
  display: flex;
  justify-content: center;

  .reset-password {
    box-shadow:
      0 0.1rem 0.3rem 0 var(--md-black-20),
      0 0.1rem 0.1rem 0 var(--md-black-14),
      0 0.2rem 0.1rem -0.1rem var(--md-black-12);
    background-color: var(--white);
    border-radius: 0.3rem;
    width: 100%;
    margin-bottom: 2rem;

    .title {
      padding: 1rem 1.5rem;
      border-bottom: 0.1rem solid var(--md-red-grey-600);

      h3 {
        font-size: 1.6rem;
        font-weight: 500;
        line-height: 1.1;
        margin-bottom: 0.5rem;
      }

      p {
        margin-bottom: 0;
        color: var(--md-grey-800);
        font-size: 1.4rem;
      }
    }
  }

  .reset-password-valid {
    .reset-password-form {
      padding: 4rem 1.5rem;

      .inputs {
        display: flex;
        flex-direction: column;

        .mb-15 {
          margin-bottom: 1.5rem;
        }

        input {
          margin: 0;
        }

        label {
          font-weight: bold;
          margin-bottom: 0.5rem;
          padding: 0;
          font-size: 1.4rem;
          color: var(--md-grey-1000);
        }

        .error {
          font-size: 1.4rem;
          margin-top: 0.5rem;
          line-height: 1.43;

          .text-normal {
            color: var(--md-grey-800);
            margin-bottom: 0;
          }

          .password-danger {
            color: var(--color-bg-red);
            margin-bottom: 0;
          }

          .password-success {
            color: var(--md-green-500);
            margin-bottom: 0;
          }
        }

        .btn-continue {
          padding: 0.6rem 1.8rem;
          border: none;
          border-radius: 0.3rem;
          width: fit-content;
          color: var(--white);
          font-weight: 500;
          font-size: 1.4rem;

          &:disabled {
            opacity: 0.65;
            cursor: not-allowed;
            box-shadow: none;

            &:hover {
              background-color: var(--md-indigo-600);
            }
          }
        }
      }
    }
  }

  .rest-password-expired-token {
    .description {
      padding: 1.5rem;
      line-height: 1.43;
      margin: 0;
      font-size: 1.4rem;
    }
  }
}

.setting-container {
  display: inline-block;
  width: 100%;
  margin-left: 0;
}
