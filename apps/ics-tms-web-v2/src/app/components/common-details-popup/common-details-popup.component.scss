@mixin details-middle-number-box {
  font-size: 2.4rem;
  font-weight: 400;
  line-height: 1;
  z-index: 10;
  padding: 0.2rem 0.8rem;
  border-radius: 0.3rem;
  background-color: var(--details-popup-bg);
  border: 0.1rem solid var(--md-red-grey-600);

  p {
    margin: 0;
  }
}

.details-container {
  box-shadow: 0 0.5rem 1.5rem var(--md-black-50);
  border-radius: 1.6rem 1.6rem 1.6rem 0;

  .details-upper-header {
    background: var(--md-indigo-600);
    border-top-left-radius: 0.4rem;
    border-top-right-radius: 0.4rem;

    .cross {
      color: var(--white);
      cursor: pointer;
      font-size: 2.3rem;
      opacity: 0.5;
      height: 2.3rem;
      display: flex;
      align-items: center;
    }

    .close-details {
      padding: 1rem 1.5rem;
      display: flex;
      justify-content: space-between;
      height: fit-content;

      h4 {
        margin: 0;
        color: var(--white);
        font-size: 1.8rem;
        font-weight: 500;
      }
    }

    .close-details-bulk {
      padding: 1rem 1.5rem;
      display: flex;
      justify-content: flex-end;
      height: fit-content;

      h4 {
        margin: 0;
        color: var(--white);
        font-size: 1.8rem;
        font-weight: 500;
      }
    }
  }

  .blank-div {
    height: 7rem;
    background: var(--md-indigo-600);
  }

  .details-bg-body {
    background-color: var(--details-popup-pale-bg);
    padding: 1rem 1.5rem 4rem 1.5rem;
    border-bottom-left-radius: 0.4rem;
    border-bottom-right-radius: 0.4rem;

    .main-details-container {
      border-radius: 0.2rem;
      box-shadow:
        0 0.1rem 0.3rem 0 var(--md-black-20),
        0 0.1rem 0.1rem 0 var(--md-black-14),
        0 0.2rem 0.1rem -0.1rem var(--md-black-12);
      max-width: 75%;
      margin-right: auto;
      margin-left: auto;
      margin-top: -6.5rem;

      .details-main-heading {
        border-top-left-radius: 0.3rem;
        border-top-right-radius: 0.3rem;
        border-bottom: 0.1rem solid var(--md-red-grey-600);
        padding: 15px;
        height: fit-content;
        background-color: var(--white);

        h1 {
          margin: 7px 0;
          font-size: 20px;
          font-weight: 400;
          line-height: 1;
        }
      }

      .main-details-info {
        height: fit-content;

        .main-details-info-upper {
          padding: 1.5rem;
          font-size: 1.4rem;
          background-color: var(--white);

          p {
            margin: 0;
          }

          .verticalAlign {
            display: flex;
            flex-direction: column;

            .details-bold {
              font-weight: bold;

              .details-text {
                font-weight: normal;
              }
            }

            .prompt-set-tag {
              background-color: var(--color-details-preview-lable-blue);
              font-size: 0.1rem;
              padding: 0.2em 0.6em 0.3em;
              font-weight: 500;
              color: var(--details-popup-bg);
              border-radius: 0.25em;
            }
          }
        }

        .main-details-info-middle {
          height: fit-content;
          padding: 15px;
          background-color: var(--color-white);
          border-bottom: 0.1rem solid var(--md-red-grey-600);
          display: flex;
          flex-direction: column;

          .middle-status {
            .label {
              width: 100%;
              margin: 1rem 0 2rem 0;

              .txt-status {
                color: var(--md-grey-1000);
              }
            }

            h4 {
              font-size: 1.8rem;
              font-weight: 500;
              text-align: center;
            }

            .fw-bolder {
              font-size: 1.4rem;
              font-weight: 500;
            }
          }

          .info-middle-invisible-div {
            margin: auto;
            padding-bottom: 1.875rem;
            width: 77%;
            border-bottom: 0.1rem solid var(--md-red-grey-600);
          }

          .info-middle-content {
            display: flex;
            justify-content: space-between;
            margin-top: -1.625rem;
            padding: 0 3rem;

            .box-left-most {
              display: flex;
              flex-direction: column;
              align-items: center;

              .number-box {
                @include details-middle-number-box;
              }

              h5 {
                margin: 0.8rem 0 0 0;
                font-weight: 400;
              }
            }

            .box-middle {
              display: flex;
              flex-direction: column;
              align-items: center;

              .number-box {
                @include details-middle-number-box;
                color: var(--color-bg-red);
              }

              .status-canceled {
                color: var(--color-bg-orange);
              }

              .status-inProgress {
                color: var(--md-indigo-600);
              }

              h5 {
                margin: 0.8rem 0 0 0;
                font-weight: 400;
              }
            }

            .box-right-most {
              display: flex;
              flex-direction: column;
              align-items: center;
              color: var(--md-red-grey-600);

              .number-box {
                @include details-middle-number-box;
              }

              .status-success-number {
                color: var(--md-green-500);
              }

              .status-scheduled-number {
                color: var(--md-red-grey-600);
              }

              h5 {
                margin: 0.8rem 0 0 0;
                font-weight: 400;
              }
            }

            .status-success-text {
              color: var(--black);
            }

            .status-scheduled-text {
              color: var(--md-red-grey-600);
            }
          }
        }

        .list-group {
          overflow: auto;
          max-height: 30rem;

          .main-details-bottom {
            padding: 1rem 1.5em;
            border-top: 0.1rem solid var(--md-red-grey-600);
            border-bottom-left-radius: 0.4rem;
            border-bottom-right-radius: 0.4rem;
            background-color: var(--detials-bg-bottom);
            font-size: 1.4rem;

            .main-details-bottom-heading {
              margin: 0 0 1rem;

              .text-underline:hover {
                text-decoration: underline;
              }

              .fw-bold {
                font-weight: 500 !important;
              }

              b {
                line-height: 1.1;
                font-weight: 500;
              }
            }

            .accordion {
              display: flex;
              flex-direction: column;
              gap: 0.312rem;

              // device status colors
              .label-default {
                background-color: var(--label-default);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .label-info {
                background-color: var(--md-light-blue-a150);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .label-primary {
                background-color: var(--label-primary);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .label-success {
                background-color: var(--label-success);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .label-danger {
                background-color: var(--md-red-600);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .label-warning {
                background-color: var(--color-bg-orange);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .accordion-item {
                border: 0.1rem solid var(--md-red-grey-600);
                border-radius: 0.187rem;

                // header content
                .accordion-header {
                  margin: 0;

                  .collapsed {
                    border-radius: 0.187rem;
                  }

                  .accordion-bulk-recommission {
                    background-color: #f7f7f8;
                  }

                  .accordion-button {
                    padding: 0;

                    &::after {
                      display: none;
                    }

                    &:focus {
                      box-shadow: none;
                    }

                    .remove-border-radius {
                      border-bottom-left-radius: 0 !important;
                      border-bottom-right-radius: 0 !important;
                    }

                    .m-d-info-lower-card {
                      cursor: pointer;
                      width: 100%;
                      padding: 1rem 1.5rem;
                      background-color: var(--white);
                      height: fit-content;
                      display: flex;
                      justify-content: space-between;
                      border-radius: 0.3rem;
                      align-items: center;

                      .left-content {
                        display: flex;
                        text-align: center;

                        p {
                          margin: 0;
                          font-size: 1.4rem;
                        }

                        .left-content-index {
                          padding-right: 1.6rem;
                        }
                      }

                      .status-style {
                        font-size: 1.05rem;
                        border-radius: 0.25em;
                        font-weight: 500;
                      }
                    }
                  }
                }

                // collapse content
                .accordion-body {
                  padding: 0;
                  word-break: break-word;

                  .job {
                    // for accordion title
                    .status-field {
                      width: 18.33%;
                    }

                    .timestamp {
                      width: 41.66%;
                      color: var(--md-grey-1000);
                    }

                    .status-message {
                      width: 40.01%;
                    }

                    .bulk-status-field {
                      width: 17.74%;
                    }

                    .bulk-timestamp {
                      width: 60.64%;
                    }

                    .bulk-status-message {
                      width: 21.62%;
                    }

                    // list header
                    .job-list-header {
                      .job-type {
                        display: flex;
                        justify-content: space-between;
                        background-color: var(--list-group-item-hover);
                        padding: 1rem;
                        border-top: 0.1rem solid var(--md-red-grey-600);
                      }

                      .job-list-cols {
                        display: flex;
                        padding: 1rem 1.5rem;
                        border-top: 0.1rem solid var(--md-red-grey-600);
                      }
                    }

                    // list body
                    .job-list-body {
                      .job-list-item {
                        display: flex;
                        padding: 1rem 1.5rem;
                        border-top: 0.1rem solid var(--md-red-grey-600);
                        gap: 0.4rem;
                      }
                    }
                  }
                }
              }

              .accordion-collapse {
                .accordion-body {
                  padding: 0;
                }
              }
            }
          }

          .bulk-recommission-items {
            padding: 10px 15px;
            border-top: 0.1rem solid var(--md-red-grey-600);
            border-bottom-left-radius: 0.4rem;
            border-bottom-right-radius: 0.4rem;
            background-color: var(--detials-bg-bottom);
            font-size: 1.4rem;

            .main-details-bottom-heading {
              margin: 0 0 1rem;
              line-height: 1.1;

              .text-underline:hover {
                text-decoration: underline !important;
              }

              .fw-bold {
                color: black !important;
                font-weight: 500 !important;
              }

              b {
                line-height: 1.1;
                font-weight: 500;
              }
            }

            .accordion {
              display: flex;
              flex-direction: column;
              gap: 0.312rem;

              // device status colors
              .label-default {
                background-color: var(--label-default);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .label-info {
                background-color: var(--md-light-blue-a150);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .label-primary {
                background-color: var(--label-primary);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .label-success {
                background-color: var(--label-success);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .label-danger {
                background-color: var(--md-red-600);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .label-warning {
                background-color: var(--color-bg-orange);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .accordion-item {
                border: none;
                border-bottom: 1px solid #d3d3d3;
                border-radius: 2px;

                // header content
                .accordion-header {
                  margin: 0;

                  .collapsed {
                    border-radius: 0.187rem;
                  }

                  .accordion-bulk-recommission {
                    background-color: #f7f7f8;
                  }

                  .accordion-button {
                    padding: 0;

                    &::after {
                      display: none;
                    }

                    &:focus {
                      box-shadow: none;
                    }

                    .remove-border-radius {
                      border-bottom-left-radius: 0 !important;
                      border-bottom-right-radius: 0 !important;
                    }

                    .m-d-info-lower-card {
                      cursor: pointer;
                      width: 100%;
                      padding: 1rem 1.5rem;
                      background-color: #f7f7f8;
                      height: fit-content;
                      display: flex;
                      justify-content: space-between;
                      align-items: center;

                      .left-content {
                        display: flex;
                        text-align: center;
                        font-size: 14px;

                        p {
                          margin: 0;
                          font-size: 1.4rem;
                        }

                        .left-content-index {
                          padding-right: 10px;
                        }

                        .text-underline:hover {
                          text-decoration: underline !important;
                        }
                      }

                      .status-style {
                        font-size: 1.05rem;
                        border-radius: 0.25em;
                        font-weight: 500;
                      }
                    }
                  }
                }

                // collapse content
                .accordion-body {
                  padding: 0;
                  word-break: break-word;

                  .job {
                    // for accordion title
                    .status-field {
                      width: 18.33%;
                    }

                    .timestamp {
                      width: 41.66%;
                      color: var(--md-grey-1000);
                    }

                    .status-message {
                      width: 40.01%;
                    }

                    .bulk-status-field {
                      width: 17.74%;
                    }

                    .bulk-timestamp {
                      width: 60.64%;
                    }

                    .bulk-status-message {
                      width: 21.62%;
                    }

                    // list header
                    .job-list-header {
                      .job-type {
                        display: flex;
                        justify-content: space-between;
                        background-color: var(--list-group-item-hover);
                        padding: 1rem;
                        border-top: 0.1rem solid var(--md-red-grey-600);
                      }

                      .job-list-cols {
                        display: flex;
                        padding: 1rem 1.5rem;
                        border-top: 0.1rem solid var(--md-red-grey-600);
                      }
                    }

                    // list body
                    .job-list-body {
                      .job-list-item {
                        display: flex;
                        padding: 1rem 1.5rem;
                        border-top: 0.1rem solid var(--md-red-grey-600);
                        gap: 0.4rem;
                      }
                    }
                  }
                }
              }

              .accordion-collapse {
                .accordion-body {
                  padding: 0;
                }
              }
            }
          }

          .bulk-recommission-items {
            padding: 10px 15px;
            border-top: 0.1rem solid var(--md-red-grey-600);
            border-bottom-left-radius: 0.4rem;
            border-bottom-right-radius: 0.4rem;
            background-color: var(--detials-bg-bottom);
            font-size: 1.4rem;

            .main-details-bottom-heading {
              margin: 0 0 1rem;
              line-height: 1.1;

              .text-underline:hover {
                text-decoration: underline !important;
              }

              .fw-bold {
                color: black !important;
                font-weight: 500 !important;
              }

              b {
                line-height: 1.1;
                font-weight: 500;
              }
            }

            .accordion {
              display: flex;
              flex-direction: column;
              gap: 0.312rem;

              // device status colors
              .label-default {
                background-color: var(--label-default);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .label-info {
                background-color: var(--md-light-blue-a150);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .label-primary {
                background-color: var(--label-primary);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .label-success {
                background-color: var(--label-success);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .label-danger {
                background-color: var(--md-red-600);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .label-warning {
                background-color: var(--color-bg-orange);
                border-radius: 0.25em;
                font-weight: 500;
              }

              .accordion-item {
                border: none;
                border-bottom: 1px solid #d3d3d3;
                border-radius: 2px;

                // header content
                .accordion-header {
                  margin: 0;

                  .collapsed {
                    border-radius: 0.187rem;
                  }

                  .accordion-bulk-recommission {
                    background-color: #f7f7f8;
                  }

                  .accordion-button {
                    padding: 0;

                    &::after {
                      display: none;
                    }

                    &:focus {
                      box-shadow: none;
                    }

                    .remove-border-radius {
                      border-bottom-left-radius: 0 !important;
                      border-bottom-right-radius: 0 !important;
                    }

                    .m-d-info-lower-card {
                      cursor: pointer;
                      width: 100%;
                      padding: 1rem 1.5rem;
                      background-color: #f7f7f8;
                      height: fit-content;
                      display: flex;
                      justify-content: space-between;
                      align-items: center;

                      .left-content {
                        display: flex;
                        text-align: center;
                        font-size: 14px;

                        p {
                          margin: 0;
                          font-size: 1.4rem;
                        }

                        .left-content-index {
                          padding-right: 10px;
                        }

                        .text-underline:hover {
                          text-decoration: underline !important;
                        }
                      }

                      .status-style {
                        font-size: 1.05rem;
                        border-radius: 0.25em;
                        font-weight: 500;
                      }
                    }
                  }
                }

                // collapse content
                .accordion-body {
                  padding: 0;
                  word-break: break-word;

                  .job {
                    // for accordion title
                    .status-field {
                      width: 18.33%;
                    }

                    .timestamp {
                      width: 41.66%;
                      color: #212121;
                    }

                    .status-message {
                      width: 40.01%;
                    }

                    .bulk-status-field {
                      width: 17.74%;
                    }

                    .bulk-timestamp {
                      width: 60.64%;
                    }

                    .bulk-status-message {
                      width: 21.62%;
                    }

                    // list header
                    .job-list-header {
                      .job-type {
                        display: flex;
                        justify-content: space-between;
                        background-color: var(--list-group-item-hover);
                        padding: 1rem;
                        border-top: 0.1rem solid var(--md-red-grey-600);
                      }

                      .job-list-cols {
                        display: flex;
                        padding: 1rem 1.5rem;
                        border-top: 0.1rem solid var(--md-red-grey-600);
                      }
                    }

                    // list body
                    .job-list-body {
                      .job-list-item {
                        display: flex;
                        padding: 1rem 1.5rem;
                        border-top: 0.1rem solid var(--md-red-grey-600);
                        gap: 0.4rem;
                      }
                    }
                  }
                }
              }

              .accordion-collapse {
                .accordion-body {
                  padding: 0;
                }
              }
            }
          }
        }
      }
    }
  }
}

.approval-buttons {
  margin-top: 2rem;
  display: flex;
  justify-content: space-between;
  width: 20%;
  margin-left: 12.5%;
}
