<div class="multiselect-container">
  <div class="site-tags-strict-filter-container">
    Filter matching criteria for tags by:
    <label class="strict-rb-label">
      <input
        type="radio"
        name="radio-apply-strict-match"
        [value]="false"
        (change)="onFilterChange()"
        [(ngModel)]="isStrictMatch"
        [disabled]="isDisabled"
      />
      <span class="label-text">Any</span>
    </label>
    <label class="strict-rb-label">
      <input
        type="radio"
        name="radio-apply-strict-match"
        [value]="true"
        (change)="onFilterChange()"
        [(ngModel)]="isStrictMatch"
        [disabled]="isDisabled"
      />
      <span class="label-text">All</span>
    </label>
  </div>

  <ng-select
    [items]="tags"
    [hideSelected]="true"
    [multiple]="true"
    bindLabel="name"
    bindValue="name"
    placeholder="Filter by tags"
    class="w-100 custom-padding"
    (ngModelChange)="onChangeTags($event)"
    [(ngModel)]="selectedTags"
    [disabled]="isDisabled"
  >
  </ng-select>
</div>
