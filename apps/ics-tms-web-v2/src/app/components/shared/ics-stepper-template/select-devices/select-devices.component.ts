import {
  Input,
  ViewEncapsulation,
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatStepper } from '@angular/material/stepper';
import { Store } from '@ngrx/store';
import { Observable, Subject, debounceTime } from 'rxjs';
import { PRESENT } from 'src/app/constants/appConstants';
import {
  Device,
  DeviceSummary,
  DeviceSummaryParams,
  DevicesEntity,
} from 'src/app/models/ics-stepper-template.model';
import { SiteGroups } from 'src/app/models/sites-groups.model';
import { Tags } from 'src/app/models/tags.model';
import { FileDownloadItemDetail } from 'src/app/modules/modules-remote/models/file-downloads.model';
import { loadDeviceSummaryData } from 'src/app/store/actions/device-summary.actions';
import {
  getDeviceSummaryData,
  getDeviceSummaryDataLoading,
} from 'src/app/store/selectors/device-summary.selectors';
import { selectTagsData } from 'src/app/store/selectors/tags.selectors';

@Component({
  selector: 'app-select-devices',
  templateUrl: './select-devices.component.html',
  styleUrls: ['./select-devices.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class SelectDevicesComponent implements OnInit, OnChanges {
  @Input() stepper!: MatStepper;

  @Input() isDisabled!: boolean;

  @Input() activeStep!: string;

  @Input() stepId!: string;

  @Input() formGroup!: FormGroup;

  @Input() device!: Device;

  @Input() deployments!: FileDownloadItemDetail[];

  @Input() isCopy!: boolean;

  @Input() siteGroups!: SiteGroups[];

  @Input() isRetry!: boolean;

  @Input() isInstall!: boolean;

  @Input() deviceMode!: string;

  // listing data
  tags$!: Observable<Tags[]>;

  sites$!: Observable<SiteGroups[]>;

  summary$!: Observable<DeviceSummary[]>;

  isSummaryDataLoading$!: Observable<boolean>;

  tagsData!: Tags[];

  summaryData!: DeviceSummary[];

  public totalDevicesCount: number = 0;

  selectedTags!: string[];

  selectedSiteGroups!: string[];

  // sites and devices info
  totalSites: { id: string }[] = [];

  selectedSites: { id: string }[] = [];

  totalDevices: { id: number }[] = [];

  selectedDevices: { id: number }[] = [];

  selectedDevicesLength = 0;

  // for searching and params to pass
  params: DeviceSummaryParams;

  prevSearchQuery = '';

  searchTerms = new Subject<string>();

  applyStrictMatchTags = false;

  currentPageNo = 1;

  itemsPerPage = 10;

  activeIds: string[] = [];

  @ViewChild('searchedFile') searchedFile!: ElementRef;

  // select all
  isSelectAll = false;

  isContinueClickedOnce = false;

  constructor(private store: Store) {
    this.params = {};
  }

  ngOnInit(): void {
    // load tags and sitegroups
    this.tags$ = this.store.select(selectTagsData);
    this.tags$.subscribe(data => {
      this.tagsData = data;
    });

    // load summary data
    this.isSummaryDataLoading$ = this.store.select(getDeviceSummaryDataLoading);
    this.summary$ = this.store.select(getDeviceSummaryData);
    this.summary$.subscribe(summary => {
      this.summaryData = summary;
      this.totalDevicesCount = summary.reduce(
        (sum, data) => sum + (data.devices?.length || 0),
        0
      );
      this.formGroup.get('totalDevicesCount')?.setValue(this.totalDevicesCount);
      this.currentPageNo = 1;

      this.updateTotalSitesAndDevices();
    });

    if (this.isCopy || this.isRetry || this.isInstall) {
      this.updateSelectedSitesAndDevices();
    }
    this.setupSearch();
  }

  ngOnChanges(changes: SimpleChanges) {
    const deviceChanged =
      changes['device'] &&
      changes['device'].previousValue?.id !==
        changes['device'].currentValue?.id;
    const deviceModeChanged =
      changes['deviceMode'] && !changes['deviceMode'].firstChange;
    if (deviceChanged || deviceModeChanged) {
      // clear previous selection
      this.selectedDevices = [];
      this.selectedSites = [];
      this.selectedDevicesLength = 0;
      this.isSelectAll = false;
      this.currentPageNo = 1;
    }
  }

  updateSelectedSitesAndDevices() {
    const sitesTargetMapping = new Map<string, number[]>();
    this.deployments.forEach(deployment => {
      const containsSite = sitesTargetMapping.has(deployment.site.id);
      if (containsSite) {
        let existingDevices = sitesTargetMapping.get(deployment.site.id) || [];
        existingDevices = [...existingDevices, deployment.target.id];
        sitesTargetMapping.set(deployment.site.id, existingDevices);
      } else {
        sitesTargetMapping.set(deployment.site.id, [deployment.target.id]);
      }
    });

    /* eslint-disable no-restricted-syntax */
    for (const [site, targets] of sitesTargetMapping) {
      const siteExists = this.summaryData.find(data => data.id === site);
      if (siteExists?.devices.length === targets.length) {
        this.selectedSites = [...this.selectedSites, { id: site }];
      }
      this.selectedDevices = [
        ...this.selectedDevices,
        ...targets.map(target => ({ id: target })),
      ];
    }
    /* eslint-enable no-restricted-syntax */

    this.selectedDevicesLength = this.selectedDevices.length;
  }

  updateTotalSitesAndDevices() {
    this.totalSites = [];
    this.totalDevices = [];
    this.summaryData.forEach(data => {
      this.totalSites.push({ id: data.id });
      data.devices.forEach(device => {
        this.totalDevices.push({ id: device.id });
      });
    });
  }

  setupSearch() {
    this.searchTerms.pipe(debounceTime(500)).subscribe(term => {
      const value = term.trim();
      if (value !== this.prevSearchQuery) {
        this.prevSearchQuery = value;
        this.setParams(value);
      }
    });
  }

  setParams(query?: string): void {
    if (this.device) {
      this.params = {
        presence: PRESENT,
        isFileDownload: true,
        refererType: 'software',
        type: this.device.deviceType.id,
        ...(this.selectedTags?.length > 0
          ? {
              tags: this.selectedTags.join(),
            }
          : {}),
        ...(this.selectedSiteGroups?.length > 0
          ? {
              siteGroups: this.selectedSiteGroups.join(),
            }
          : {
              siteGroups: this.siteGroups
                .map(siteGroup => siteGroup.name)
                .join(','),
            }),
        ...(query && query.length > 0 && { q: query }),
        ...(this.applyStrictMatchTags
          ? {
              applyStrictMatchTags: true,
            }
          : {}),
        ...(this.device.seqVersion
          ? { seqVersion: this.device.seqVersion }
          : {}),
      };

      // Dispatching the action to load data
      this.store.dispatch(loadDeviceSummaryData({ params: this.params }));
      this.updateTotalSitesAndDevices();
    }
  }

  selectAllSites() {
    this.selectedDevices = [];
    this.selectedSites = [];

    this.summaryData.forEach(data => {
      data.devices.forEach(device => {
        const deviceExists = this.selectedDevices.some(
          selectedDevice => selectedDevice.id === device.id
        );
        if (!deviceExists) {
          this.selectedDevices = [...this.selectedDevices, device];
        }
      });
    });
    this.selectedSites = this.totalSites;
    this.selectedDevicesLength = this.selectedDevices.length;

    this.formGroup.get('targets')?.setValue(this.selectedDevices);
    this.formGroup.get('sites')?.setValue(this.selectedSites);
  }

  deSelectAllSites() {
    this.selectedDevices = [];
    this.selectedSites = [];
    this.selectedDevicesLength = 0;

    this.formGroup.get('targets')?.setValue(this.selectedDevices);
    this.formGroup.get('sites')?.setValue(this.selectedSites);
  }

  applyFilter(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    this.searchTerms.next(inputElement.value);
  }

  getSelectedTags(tags: string[]) {
    this.selectedTags = tags;
    this.setParams();
  }

  getSelectedSiteGroups(siteGroups: string[]) {
    this.selectedSiteGroups = siteGroups;
    this.setParams();
  }

  getPaginatedItems() {
    const startIndex = (this.currentPageNo - 1) * this.itemsPerPage;
    if (this.summaryData) {
      return this.summaryData.slice(startIndex, startIndex + this.itemsPerPage);
    }
    return [];
  }

  onNextPage() {
    if (this.currentPageNo < this.getTotalPages()) {
      this.currentPageNo++;
    }
  }

  getTotalPages() {
    return Math.ceil(this.summaryData.length / this.itemsPerPage);
  }

  onPrevPage() {
    if (this.currentPageNo > 1) {
      this.currentPageNo--;
    }
  }

  handleSelect(event: Event, site: DeviceSummary, device?: DevicesEntity) {
    const target = event.target as HTMLInputElement;
    if (!device) this.handleSiteSelect(target.checked, site);
    else this.handleDeviceSelect(target.checked, site, device);

    this.selectedDevicesLength = this.selectedDevices.length;

    this.formGroup.get('targets')?.setValue(this.selectedDevices);
    this.formGroup.get('sites')?.setValue(this.selectedSites);

    // set form's state to invalid, to avoid navigation around the stepper
    if (
      !this.isCopy &&
      !this.isContinueClickedOnce &&
      this.selectedSites.length &&
      this.selectedDevices.length
    ) {
      this.isContinueClickedOnce = true;
      this.formGroup.setErrors({ incorrect: true });
    }

    this.isAllSelected();
  }

  handleSiteSelect(isChecked: boolean, site: DeviceSummary) {
    if (isChecked) {
      this.selectedSites.push({ id: site.id });
      site.devices.forEach(device => {
        const isDevicePresent = this.selectedDevices.findIndex(
          selectedDevice => selectedDevice.id === device.id
        );
        if (isDevicePresent === -1) {
          this.selectedDevices.push({ id: device.id });
        }
      });
    } else {
      this.selectedSites = this.selectedSites.filter(
        (selectedSite: { id: string }) => selectedSite.id !== site.id
      );

      this.selectedDevices = this.selectedDevices.filter(
        (device: { id: number }) =>
          !site.devices.some(fileDevice => fileDevice.id === device.id)
      );
    }
  }

  handleDeviceSelect(
    isChecked: boolean,
    site: DeviceSummary,
    device: DevicesEntity
  ) {
    if (isChecked) {
      this.selectedDevices.push({ id: device.id });
      const allDevicesSelected = site.devices.every(target =>
        this.selectedDevices.some(
          selectedDevice => selectedDevice.id === target.id
        )
      );
      if (allDevicesSelected) this.selectedSites.push({ id: site.id });
    } else {
      this.selectedDevices = this.selectedDevices.filter(
        (selectedDevice: { id: number }) => selectedDevice.id !== device.id
      );
      const allDevicesSelected = site.devices.every(obj =>
        this.selectedDevices.some(d => d.id === obj.id)
      );
      if (!allDevicesSelected)
        this.selectedSites = this.selectedSites.filter(
          sites => sites.id !== site?.id
        );
    }
  }

  handleIsChecked(file: DeviceSummary | DevicesEntity) {
    if ('devices' in file) {
      const allDevicesSelected = file.devices.every(device =>
        this.selectedDevices.some(
          selectedDevice => selectedDevice.id === device.id
        )
      );
      return allDevicesSelected;
    }
    return this.selectedDevices.some(
      selectedDevice => selectedDevice.id === file.id
    );
  }

  clearInputFile() {
    this.searchedFile.nativeElement.value = '';
    this.searchTerms.next('');
  }

  someComplete(element: DeviceSummary) {
    const site = this.summaryData.find(item => item.id === element.id);
    return site?.devices.some(
      t =>
        this.selectedDevices.some(device => device.id === t.id) &&
        !this.handleIsChecked(element)
    );
  }

  onTagsFilterChange(value: boolean): void {
    this.applyStrictMatchTags = value;
    this.setParams();
  }

  onSelectAll(): void {
    this.isSelectAll = !this.isSelectAll;

    this.selectedSites = [];
    this.selectedDevices = [];

    if (this.isSelectAll) {
      this.summaryData.forEach(site => {
        if (!site.disableFileDownloads) {
          this.selectedSites.push({ id: site.id });

          site.devices.forEach(device => {
            this.selectedDevices.push({ id: device.id });
          });
        }
      });
    }

    this.isAllSelected();

    this.formGroup.get('targets')?.setValue(this.selectedDevices);
    this.formGroup.get('sites')?.setValue(this.selectedSites);

    this.selectedDevicesLength = this.selectedDevices.length;
  }

  continue(): void {
    // manually set form's state to valid and move to the next step
    this.formGroup.setErrors(null);
    this.stepper.next();
  }

  isAllSelected() {
    this.isSelectAll = false;

    if (this.summaryData.length) {
      for (let siteIdx = 0; siteIdx < this.summaryData.length; siteIdx++) {
        const site = this.summaryData[siteIdx];

        if (!site.disableFileDownloads) {
          const isSiteSelected = this.selectedSites.findIndex(
            ss => ss.id === site.id
          );
          if (isSiteSelected === -1) {
            return;
          }

          for (
            let deviceIdx = 0;
            deviceIdx < site.devices.length;
            deviceIdx++
          ) {
            const device = site.devices[deviceIdx];

            const isDeviceSelected = this.selectedDevices.findIndex(
              sd => sd.id === device.id
            );
            if (isDeviceSelected === -1) {
              return;
            }
          }
        }
      }

      this.isSelectAll = true;
    }
  }
}
