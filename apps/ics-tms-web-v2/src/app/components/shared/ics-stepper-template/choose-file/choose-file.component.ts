import {
  inject,
  Component,
  ElementRef,
  Input,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Observable, Subject, debounceTime } from 'rxjs';
import _ from 'lodash';
import { Store } from '@ngrx/store';
import { MatStepper } from '@angular/material/stepper';
import { CommonResponseData } from 'src/app/models/ics-common-response.model';
import {
  FILE_DOWNLOADS,
  FILE_DOWNLOADS_FILE_VIEW_PAGE_SIZE,
  MEDIA_DOWNLOADS,
} from 'src/app/constants/globalConstant';
import { selectSiteGroupsData } from 'src/app/store/selectors/site-groups.selectors';
import {
  Device,
  DeviceSummaryParams,
  SoftwareParams,
} from 'src/app/models/ics-stepper-template.model';
import { SOFTWARE } from 'src/app/modules/modules-remote/constants/appConstant';
import { MEDIA } from 'src/app/modules/module-media/constants/appConstants';
import { loadSoftwareData } from 'src/app/store/actions/software.actions';
import { selectSoftwareData } from 'src/app/store/selectors/software.selector';
import { PRESENT } from 'src/app/constants/appConstants';
import { loadDeviceSummaryData } from 'src/app/store/actions/device-summary.actions';

@Component({
  selector: 'app-choose-file',
  templateUrl: './choose-file.component.html',
  styleUrls: ['./choose-file.component.scss'],
})
export class ChooseFileComponent implements OnInit {
  @Input() stepper!: MatStepper;

  @Input() deviceFormGroup!: FormGroup;

  @Input() formGroup!: FormGroup;

  @Input() activeStep!: string;

  @Input() stepId!: string;

  @Input() data$!: Observable<CommonResponseData<Device>>;

  @Input() type!: string;

  @Input() selectedCompany!: { id: string; name: string };

  @Input() siteGroupCompanyMapping: Map<string, string[]> = new Map<
    string,
    string[]
  >();

  @Input() isCopy!: boolean;

  @Input() isSelectedCompanyChanged!: boolean;

  @Input() isDisabled!: boolean;

  @Input() isRetry!: boolean;

  @Input() isInstall!: boolean;

  @ViewChild('searchedFile') searchedFile!: ElementRef;

  originalDeviceList: Device[] = [];

  selectedFile!: Device | undefined;

  summaryParams!: DeviceSummaryParams;

  softwareParams!: SoftwareParams;

  searchTextChanged = new Subject<string>();

  prevSearchText = '';

  siteGroups: string[] = [];

  sortedField!: string;

  isAscending = false;

  searchedFileName = '';

  // meta data for client side pagination
  currentPage: number = 0;

  sortedDeviceList: Device[] = [];

  inViewDeviceList: Device[] = [];

  store = inject(Store);

  ngOnInit(): void {
    // set software params
    this.softwareParams = {
      pageSize: -1,
      type: this.type === FILE_DOWNLOADS ? SOFTWARE : MEDIA,
    };

    // load sitegroups
    this.store.select(selectSiteGroupsData).subscribe(data => {
      this.siteGroups = data.map(site => site.name);
    });

    this.data$.subscribe(data => {
      this.originalDeviceList = data.results;
      this.sortedField = 'uploadedDate';
      this.setSortedDevices();
    });

    this.sortedField = 'uploadedDate';
    this.setupSearch();

    if (this.formGroup.get('chooseFile')?.value?.name?.length > 0) {
      this.searchedFileName = this.formGroup.get('chooseFile')?.value?.name;
      this.searchTextChanged.next(this.searchedFileName);

      if (this.isCopy || this.isRetry || this.isInstall) {
        this.selectedFile = this.formGroup.get('chooseFile')?.value;
      }
    }
  }

  setupSearch() {
    this.searchTextChanged.pipe(debounceTime(400)).subscribe(model => {
      const value = model.trim();
      if (value !== this.prevSearchText) {
        this.prevSearchText = value;
        if (value !== '') {
          this.softwareParams = {
            ...this.softwareParams,
            name: value,
          };
          this.searchQuery();
        } else {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { name, ...rest } = this.softwareParams;
          this.softwareParams = { ...rest };
          this.searchQuery();
        }
      }
    });
  }

  searchQuery() {
    this.store.dispatch(loadSoftwareData({ params: this.softwareParams }));
    this.data$ = this.store.select(selectSoftwareData);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { name, ...rest } = this.softwareParams;
    this.softwareParams = rest;
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.searchTextChanged.next(filterValue);
    this.formGroup.controls['chooseFile'].setValue({});
  }

  isTypeMedia() {
    return this.type === MEDIA_DOWNLOADS;
  }

  selectRadioButton(device: Device): void {
    this.formGroup.controls['chooseFile'].setValue(device);

    // if the selected file is new, clear selected site(s)/device(s)
    if (this.selectedFile?.id !== device.id) {
      this.deviceFormGroup.patchValue({ targets: '', sites: '' });
    }
    // set new file
    this.selectedFile = device;

    // set form's state to invalid, to avoid navigation around the stepper
    this.formGroup.setErrors({ incorrect: true });
  }

  getStatusColor(text: string | undefined) {
    if (text === 'PREVIEW') {
      return '#2196f3';
    }
    return '#4caf50';
  }

  setSortedDevices(): void {
    if (this.sortedField) {
      this.sortedDeviceList = _.orderBy(
        this.originalDeviceList,
        [
          device =>
            this.getNestedPropertyValue(
              device,
              this.sortedField
            )?.toLowerCase() || '',
        ],
        [this.isAscending ? 'asc' : 'desc']
      );

      this.currentPage = 0;
      this.inViewDeviceList = [];
      this.appendSortedDevicesToView();
    }
  }

  sortData(field: string): void {
    if (this.sortedField === field) {
      this.isAscending = !this.isAscending;
    } else {
      this.isAscending = true;
      this.sortedField = field;
    }

    this.setSortedDevices();
  }

  private getNestedPropertyValue(obj: Device, path: string) {
    const result = path
      .split('[')
      .reduce((acc, key) => acc[key.replace(']', '')], obj);
    return result as unknown as string;
  }

  loadDeviceSummaryData() {
    const siteGroupsOfCompany =
      this.siteGroupCompanyMapping.get(this.selectedCompany.name) ?? [];

    this.summaryParams = {
      presence: PRESENT,
      ...(siteGroupsOfCompany?.length > 0
        ? {
            siteGroups: siteGroupsOfCompany.join(),
          }
        : {}),
      type: this.selectedFile?.deviceType?.id ?? '',
      isFileDownload: true,
      ...(this.selectedFile?.seqVersion
        ? { seqVersion: this.selectedFile.seqVersion }
        : {}),
      refererType: 'software',
    };

    // load summary data
    this.store.dispatch(loadDeviceSummaryData({ params: this.summaryParams }));

    // manually set form's state to valid and move to the next step
    this.formGroup.setErrors(null);

    if (!this.formGroup.value.chooseFile?.id) {
      this.formGroup.setValue({ chooseFile: this.selectedFile });
    }

    this.stepper.next();
  }

  clearInputFile() {
    this.searchedFile.nativeElement.value = '';
    this.searchQuery();
    this.searchTextChanged.next('');
    this.formGroup.controls['chooseFile'].setValue({});
  }

  highlightSearchText(text: string, searchText: string): string {
    const regex = new RegExp(searchText, 'gi');
    return text.replace(regex, match => `<strong>${match}</strong>`);
  }

  appendSortedDevicesToView(): void {
    const PAGE_SIZE = FILE_DOWNLOADS_FILE_VIEW_PAGE_SIZE;
    if (this.currentPage * PAGE_SIZE < this.sortedDeviceList.length) {
      const start = this.currentPage * PAGE_SIZE;
      const end = start + PAGE_SIZE;

      const nextDevices = this.sortedDeviceList.slice(start, end);
      this.inViewDeviceList = [...this.inViewDeviceList, ...nextDevices];

      this.currentPage += 1;

      // set selected file
      if (this.selectedFile) {
        const f = this.sortedDeviceList.find(
          d => d.id === this.selectedFile?.id
        );

        if (f) {
          this.formGroup.get('chooseFile')?.setValue(f);
        }
      }
    }
  }
}
