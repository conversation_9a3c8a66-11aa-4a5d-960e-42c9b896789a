import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewEncapsulation,
} from '@angular/core';

import {
  Observable,
  Subject,
  catchError,
  concat,
  distinctUntilChanged,
  map,
  of,
  switchMap,
  tap,
} from 'rxjs';
import { NAME_ONLY } from '../../../constants/appConstants';
import { generateInitialsAndColor } from 'src/app/modules/module-settings/utils/transform-data';
import { AuthService } from 'src/app/services/auth.service';

@Component({
  selector: 'app-ics-user-filter',
  templateUrl: './ics-user-filter.component.html',
  styleUrls: ['./ics-user-filter.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class IcsUserFilterComponent implements OnInit, OnChanges {
  @Input() dataSource$!: Observable<any[]>;

  @Input() selectedData: any = [];

  @Input() bindLabel!: string;

  @Input() placeholder!: string;

  @Input() type!: string;

  @Output() getData = new EventEmitter<any>();

  itemsData$!: Observable<any[]>;

  dataLoading = false;

  dataInput$ = new Subject<string>();

  isDropdownOpen = false;

  searchedText: any;

  selected!: any;

  protected readonly NAME_ONLY = NAME_ONLY;

  constructor() {
    // constructor code
  }

  ngOnInit(): void {
    this.loadItemsData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedData']) {
      this.selected = this.selectedData;
    }
  }

  loadItemsData() {
    this.itemsData$ = concat(
      of([]),
      this.dataInput$.pipe(
        distinctUntilChanged(),
        tap(() => {
          this.dataLoading = true;
        }),
        switchMap(term => this.filterData(term))
      )
    );
  }

  filterData(term: string) {
    const currentUserEmail = AuthService.getUser()?.email;
    return this.dataSource$.pipe(
      map((items: any) => {
        let data = items;
        data = data.filter((person: any) => !this.isSelected(person));

        // remove the logged-in user from the list
        data = data.filter((user: any) => user.email !== currentUserEmail);

        if (term) {
          data = data.filter(
            (item: any) =>
              item.fullName.toLocaleLowerCase().indexOf(term) > -1 ||
              item.email.toLocaleLowerCase().indexOf(term) > -1
          );
        }

        return data;
      }),
      catchError(() => of([])),
      tap(() => {
        this.dataLoading = false;
      })
    );
  }

  isSelected(item: any): boolean {
    return this.selectedData.some(
      (selectedItem: any) => selectedItem.id === item.id
    );
  }

  closeDropDown(event: Event) {
    const target = event.target as HTMLInputElement;
    target.value = '';
    this.isDropdownOpen = false;
  }

  updateSearchedText(event: any): void {
    const inputValue = event.term.trim().toLowerCase();
    this.searchedText = event.term.trim().toLowerCase();
    if (inputValue.length > 0) this.isDropdownOpen = true;
    else this.isDropdownOpen = false;
    this.dataInput$.next(inputValue);
  }

  handleAddItem(event: any) {
    this.selectedData = [...this.selectedData, event];
    this.getData.emit(this.selectedData);
    this.isDropdownOpen = false;
  }

  getAvatarImg(person: any) {
    const name = person[`${this.bindLabel}`];
    return generateInitialsAndColor(name);
  }

  highlightSearchText(text: string, searchText: string): string {
    const regex = new RegExp(searchText, 'gi');
    return text.replace(regex, match => `<strong>${match}</strong>`);
  }
}
