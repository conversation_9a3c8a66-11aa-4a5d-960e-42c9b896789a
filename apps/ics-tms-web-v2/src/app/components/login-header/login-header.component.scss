.header {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 1rem;
  height: 5rem;
  width: 100%;
  box-shadow:
    0 0 0.1rem 0.1rem var(--md-black-14),
    0 0 0.2rem 0.2rem rgba(0, 0, 0, 0.098),
    0 0 0.8rem 0.1rem rgba(0, 0, 0, 0.084);
  position: fixed;
  background-color: var(--white);
  z-index: 5;
  top: 0;

  .txt-build-version {
    position: absolute;
    top: 2px;
    right: 5px;
    font-size: 1rem;
    color: #999999;
    pointer-events: none;
  }

  .container-fluid {
    max-width: 1200px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0rem 1rem;

    .name {
      display: flex;
      align-items: center;
    }

    .logo-text {
      line-height: 2rem;
      float: left;
      margin-left: 1rem;
      color: var(--md-grey-800);
      font-size: 1.8rem;
    }

    .login {
      border-color: var(--md-indigo-600);
      position: relative;
      transition: all ease-in 0.1s;
      padding: 0.6rem 1.2rem;
      color: var(--white);
      background-color: var(--md-indigo-600);
      box-shadow:
        0 0.1rem 0.2rem var(--md-black-30),
        0 -0.1rem 0.3rem -0.2rem var(--md-black-20);

      &:hover {
        border-color: var(--md-dark-blue-100);
        background-color: var(--md-dark-blue-100);
      }
    }

    &:before {
      content: none;
    }

    &:after {
      content: none;
    }
  }
}
