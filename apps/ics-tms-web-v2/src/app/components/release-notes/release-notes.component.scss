.release-notes-container {
  .release-notes-content {
    font-size: 1.4rem;
    .no-data {
      color: var(--md-grey-1000);
      margin: 0 0 1rem;
    }
    .note-wrapper {
      padding-bottom: 4rem;
      .note {
        cursor: pointer;
        p {
          margin: 0;
        }
        .timestamp {
          font-size: 90%;
          margin-top: 0.2rem;
          word-wrap: break-word;
          color: var(--md-grey-700);
        }
        .left {
          position: relative;
          float: left;
          width: 17rem;
          padding-right: 3.8rem;
          transition: all 0.15s ease-in-out;
          text-align: right;
          .date {
            font-size: 1.4rem;
            font-weight: bold;
            margin-top: -0.5rem;
            &.latest {
              font-size: 1.7rem;
            }
          }

          .circle {
            position: absolute;
            right: 0;
            top: 0;
            background-color: var(--color-white);
            width: 1.5rem;
            height: 1.5rem;
            border-radius: 100%;
            border: 0.2rem solid var(--md-grey-800);
            transition: all 0.15s ease-in-out;
            z-index: 1;
            &.latest {
              background-color: var(--btn-light-gray-border);
              height: 2rem;
              width: 2rem;
              border: 0.4rem solid var(--md-grey-800);
              right: -0.2rem;
            }
          }
        }
        .right {
          position: relative;
          float: left;
          width: calc(100% - 17rem);
          padding-left: 4rem;
          transition: all 0.15s ease-in-out;
          &:before {
            content: '';
            position: absolute;
            background-color: var(--md-grey-400);
            width: 0.3rem;
            height: calc(100% + 4rem);
            z-index: 0;
            left: -0.9rem;
          }
          .speech-bubble {
            background-color: var(--color-white);
            border-radius: 1rem;
            position: relative;
            padding: 1rem 1.5rem;
            &:focus {
              outline: none;
            }
            box-shadow: none;
            transition: all 0.15s ease-in-out;
            .title {
              font-weight: bold;
              font-size: 1.5rem;
              margin-right: 2rem;
              margin-bottom: 0.5rem;
              min-width: 30rem;
              display: inline-block;
            }
            .expand-btn {
              font-size: 2rem;
              color: var(--md-grey-400);
              position: absolute;
              right: 1.1rem;
              top: 1.1rem;
              &:focus {
                outline: none;
              }
            }
            .version {
              clear: both;
              margin-bottom: 0.5rem;
            }
            .desc {
              margin-bottom: 1rem;
              clear: both;
            }
            .timestamp {
              margin-bottom: 1rem;
            }
            .extra-info {
              opacity: 0;
              height: 0;
              width: 0;
              transition: all 0.15s ease-in-out;
              .desc {
                opacity: 0;
              }
              &.visible {
                opacity: 1;
                height: auto;
                width: auto;
                .desc {
                  opacity: 1;
                }
              }
            }
            &:after {
              content: '';
              position: absolute;
              left: 0;
              top: 0;
              width: 0;
              height: 0;
              border: 2.5rem solid transparent;
              border-right-color: var(--color-white);
              border-left: 0;
              border-top: 0;
              margin-left: -1.5rem;
            }
          }
        }
        &:hover {
          .left {
            .circle:not(.latest) {
              width: 1.5rem;
              height: 1.5rem;
              border: 0.5rem solid var(--md-grey-800);
            }
          }
          .right {
            .speech-bubble {
              box-shadow: 0.2rem 0.2rem 0.5rem 0 rgba(0, 0, 0, 0.17);
            }
          }
        }
      }
    }
    .clearfix {
      &::before {
        display: table;
        content: ' ';
      }
    }
  }
  .notes-loading {
    padding-left: 15rem;
  }
  .initial-loading {
    padding: 0;
  }

  .ics-release-notes-title {
    font-size: 2rem;
    font-weight: 400;
    margin: 0.7rem 0 2rem 0;
    padding-bottom: 1.6rem;
    border-bottom: 0.1rem solid var(--md-red-grey-600);
    line-height: 1;
  }
}
