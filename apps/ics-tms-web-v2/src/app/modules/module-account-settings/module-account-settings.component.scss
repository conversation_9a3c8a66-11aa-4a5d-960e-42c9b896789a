.main-container {
  float: left;
  width: 100%;
  position: relative;
  min-height: 0.1rem;
  padding: 0rem 1.6rem;

  .screen {
    margin: 0rem -1.6rem;

    .side-list {
      width: 25%;
      float: left;
      position: relative;
      min-height: 0.1rem;
      padding: 0rem 1.6rem;

      .list {
        margin-bottom: 2rem;
        border-radius: 0.3rem;
        box-shadow:
          0 0.1rem 0.8rem 0 var(--md-black-20),
          0 0.1rem 0.1rem 0 var(--md-black-14),
          0 0.2rem 0.1rem -0.1rem var(--md-black-12);
        word-wrap: break-word;
        font-size: 1.4rem;

        .list-item {
          color: var(--md-grey-1000);
          position: relative;
          display: block;
          margin-bottom: -0.1rem;
          padding: 1rem 1.5rem;
          background-color: var(--color-white);
          cursor: pointer;

          &:hover {
            background: var(--color-white-shade-two);
          }

          &:first-child {
            border-top-left-radius: 0.3rem;
            border-top-right-radius: 0.3rem;
          }

          &:last-child {
            border-bottom-left-radius: 0.3rem;
            border-bottom-right-radius: 0.3rem;
          }

          &:not(:last-child) {
            border-bottom: 0.2rem solid var(--md-red-grey-600);
          }
        }
      }

      .selected::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 0.3rem;
        height: 2rem;
        margin-top: -1rem;
        content: '';
        background-color: var(--md-indigo-600-shade-one);
      }

      .selected {
        font-weight: 500;
        color: var(--md-grey-1000);
        border-color: var(--md-red-grey-600);
        background-color: var(--color-white-shade-two) !important;
      }
    }

    .main-ui {
      width: 50%;
      float: left;
      position: relative;
      min-height: 0.1rem;
      padding: 0rem 1.6rem;
      margin-bottom: 5.688rem;
    }
  }
}
