.main-container {
  .main-card {
    border-radius: 0.3rem;
    box-shadow:
      0 0.1rem 0.8rem 0 var(--md-black-20),
      0 0.1rem 0.1rem 0 var(--md-black-14),
      0 0.2rem 0.1rem -0.1rem var(--md-black-12);
    background-color: var(--color-white);

    hr {
      margin: 0;
    }

    .header {
      color: var(--md-grey-1000);
      border-top-left-radius: 0.8rem;
      border-top-right-radius: 0.8rem;
      padding: 1rem 1.5rem;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .heading {
        font-size: 1.6rem;
        font-weight: 500;
        line-height: 1;
        display: inline-block;
        text-align: left;
      }
    }

    .panel-alert {
      padding: 1.5rem;
      background-color: var(--panel-alert);
      text-shadow: 0 0.1rem 0.1rem rgba(255, 255, 255, 0.25);
      border-bottom: 0.1rem solid var(--md-red-grey-600);
      font-size: 1.4rem;
    }

    .panel-body {
      padding: 1.5rem;
      flex-direction: column;

      .user-detail {
        margin-bottom: 1.5rem;
        font-size: 1.4rem;

        .col-sm-2 {
          padding: 0.7rem 1rem 0;
        }

        .col-sm-10 {
          padding-right: 1.6rem;
          padding-left: 1.6rem;
        }

        .detail-label {
          font-weight: 700;
          margin-bottom: 0;
          padding-top: 0.7rem;
          text-align: right;
        }

        .user-data {
          margin-bottom: 0;
          padding-top: 0.7rem;
          padding-bottom: 0.7rem;
        }
      }
    }
  }
}
