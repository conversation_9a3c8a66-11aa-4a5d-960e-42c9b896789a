import { MediaDownloadsResponse } from '../models/media-download.modal';
import { MediaLibrary } from '../models/media-library.modal';
import { AllUsers, Approver } from '../models/prompt-sets.modal';
import { FileTypePattern } from '../constants/appConstants';
import { CommonResponseData } from 'src/app/models/common';
import {
  MEDIA_DOWNLOADS,
  PRE_DEFINED_COLORS,
} from 'src/app/constants/globalConstant';

export function mediaDownloadsTransform(
  rawData: MediaDownloadsResponse
): MediaDownloadsResponse {
  return {
    resultsMetadata: rawData.resultsMetadata,
    results: rawData.results.map(data => ({
      ...data,
      details: {
        id: data.id,
        heading: data.name,
        contentFile: data.software.name,
        failed: data.deploymentsFailCount,
        inProgress: data.deploymentsInProgressCount,
        canceled: data.deploymentsCancelCount,
        completed: data.deploymentsCompleteCount,
        total: data.totalDeploymentsCount,
        type: MEDIA_DOWNLOADS,
        creator: data.createdBy.fullName,
        creator_tag: data.createdBy.company.name,
        installType: data.deploymentPolicy.name,
        promptSet: data.software.name,
        promptSet_label: data.media?.status,
        promptSet_version: data.media?.version,
        installWindow: data.installWindow,
        startTime: data.startTime,
        cancelled: data.cancelled,
        deleted: data.deleted,
        retry: data.retry,
        installFrequency: data.installFrequency,
        endTime: data.endTime,
        deploymentPolicy: data.deploymentPolicy,
        fileDownloadCancelCount: data.fileDownloadCancelCount,
        fileDownloadFailCount: data.fileDownloadFailCount,
        fileDownloadTotalCount: data.fileDownloadTotalCount,
        deploymentsDownloadedCount: data.deploymentsDownloadedCount,
        deploymentsCancelCount: data.deploymentsCancelCount,
        deploymentsCompleteCount: data.deploymentsCompleteCount,
        deploymentsFailCount: data.deploymentsFailCount,
        deploymentsInProgressCount: data.deploymentsInProgressCount,
        deploymentsPendingDownloadCount: data.deploymentsPendingDownloadCount,
        deploymentsPendingInstallCount: data.deploymentsPendingInstallCount,
        totalDeploymentsCount: data.totalDeploymentsCount,
      },
    })),
  };
}

export function transformUserData(rawData: AllUsers): AllUsers {
  return {
    resultsMetadata: rawData.resultsMetadata,
    results: rawData.results.map(data => ({
      ...data,
      letters: generateInitialsAndColor(data.fullName).initials,
      color: generateInitialsAndColor(data.fullName).color,
    })),
  };
}

export function transformApproversData(rawData: Approver[]): Approver[] {
  return rawData.map(data => ({
    ...data,
    letters: generateInitialsAndColor(data.name).initials,
    color: generateInitialsAndColor(data.name).color,
  }));
}

export function generateInitialsAndColor(fullName: string) {
  const words = fullName.trim().split(' ');

  const firstInitial = words.length > 0 ? extractInitial(words[0]) : '';
  const lastInitial =
    words.length > 1 ? extractInitial(words[words.length - 1]) : '';

  const color = getColorFromString(fullName);

  return {
    initials: firstInitial + lastInitial,
    color,
  };
}

function extractInitial(word: string): string {
  const alphanumericRegex = /[a-zA-Z0-9]/;
  const initialChar = Array.from(word).find(char =>
    alphanumericRegex.test(char)
  );
  return initialChar ? initialChar.toUpperCase() : '';
}

export function getColorFromString(str: string) {
  let hash = 0;

  Array.from(str).forEach(char => {
    hash += char.charCodeAt(0);
    hash = (hash * 31) % PRE_DEFINED_COLORS.length;
  });

  const index = Math.abs(hash) % PRE_DEFINED_COLORS.length;
  return PRE_DEFINED_COLORS[index];
}

export function resolveCamelCase(text: string): string {
  return text
    .replace(/([A-Z])/g, ' $1') // Add space before each capital letter
    .replace(/^./, str => str.toUpperCase()); // Capitalize the first letter
}

export function transformMediaData(
  rawData: CommonResponseData<MediaLibrary>
): CommonResponseData<MediaLibrary> {
  const { results: mediaResults } = rawData;
  const transformedMediaResults = mediaResults.map(media => ({
    ...media,
    properties: transformProperties(media?.properties),
  }));

  return {
    ...rawData,
    results: transformedMediaResults,
  };
}

function transformProperties(
  properties: Record<string, string> = {}
): Record<string, string> {
  let transformedProperties = properties;
  if (typeof transformedProperties === 'string') {
    try {
      transformedProperties = JSON.parse(transformedProperties);
    } catch {
      transformedProperties = {};
    }
  }
  return transformedProperties;
}

export function getRegexFromPattern(pattern: FileTypePattern): RegExp {
  return new RegExp(pattern, 'i');
}

export function formatMessage(
  template: string,
  data: Record<string, string | number>
): string {
  return template.replace(
    /\{(\w+)\}/g,
    (_, key) => data[key]?.toString() ?? ''
  );
}
