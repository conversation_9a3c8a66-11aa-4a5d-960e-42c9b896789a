import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { getApiConstants } from '../constants/api';
import {
  Filters,
  LibraryParams,
  MediaLibrary,
  PublishStatus,
  Size,
  User,
} from '../models/media-library.modal';
import { CommonResponseData } from 'src/app/models/ics-common-response.model';
import { AuthService } from 'src/app/services/auth.service';
import { MEDIA_DESIGNER, WRITE_MEDIA } from 'src/app/constants/authConstants';

@Injectable({
  providedIn: 'root',
})
export class MediaLibraryService {
  public isDisplayMediaListView = new BehaviorSubject<boolean>(false);

  private selectedTab = new BehaviorSubject<string>('IMAGES');

  private searchedText = new BehaviorSubject<string>('');

  private filters = new BehaviorSubject<Filters>({
    user: { id: '', user: '' },
    size: { name: '', minHeight: 0, minWidth: 0 },
  });

  private selectedOrder = new BehaviorSubject<string>('uploaded');

  private loadMoreSubject = new Subject<void>();

  loadMore$ = this.loadMoreSubject.asObservable();

  triggerLoadMore() {
    this.loadMoreSubject.next();
  }

  http = inject(HttpClient);

  updateMediaDisplayType() {
    const currentValue = this.isDisplayMediaListView.value;
    this.isDisplayMediaListView.next(!currentValue);
  }

  isDisplayMediaListView$ = this.isDisplayMediaListView.asObservable();

  setSelectedTab(tab: string) {
    this.selectedTab.next(tab);
  }

  getSelectedTab() {
    return this.selectedTab.asObservable();
  }

  setSearchedText(text: string) {
    this.searchedText.next(text);
  }

  getSearchedText() {
    return this.searchedText.asObservable();
  }

  setFilters(size: Size, user: User) {
    this.filters.next({ user, size });
  }

  getFilters() {
    return this.filters.asObservable();
  }

  setSelectedOrder(order: string) {
    this.selectedOrder.next(order);
  }

  getSelectedOrder() {
    return this.selectedOrder.asObservable();
  }

  getSelectedMediaData(
    params: LibraryParams
  ): Observable<CommonResponseData<MediaLibrary>> {
    const httpParams = new HttpParams({ fromObject: params });
    const url = `${getApiConstants().library.getLibraryListing}`;
    return this.http.get<CommonResponseData<MediaLibrary>>(url, {
      params: httpParams,
    });
  }

  uploadAsset(payload: FormData) {
    const url = `${getApiConstants().library.getLibraryListing}`;
    return this.http.post(url, payload, {
      reportProgress: true,
      observe: 'events',
    });
  }

  uploadAssetPackage(payload: FormData) {
    const url = `${getApiConstants().library.uploadAssetPackage}`;
    return this.http.post(url, payload, {
      reportProgress: true,
      observe: 'events',
    });
  }

  updateMediaName(payload: MediaLibrary, id: string) {
    const url = `${getApiConstants().library.getLibraryListing}/${id}`;
    return this.http.put(url, payload);
  }

  deletePop(mediaId: string) {
    const url = `${
      getApiConstants().library.getLibraryListing
    }/${mediaId}/pubstatus`;
    return this.http.get<PublishStatus>(url);
  }

  deleteMedia(mediaId: string) {
    const url = `${getApiConstants().library.getLibraryListing}/${mediaId}`;
    return this.http.delete(url);
  }

  mayEditMedia(): boolean {
    return AuthService.isAllowedAccess(WRITE_MEDIA);
  }

  isMediaDesigner(): boolean {
    return AuthService.hasRole(MEDIA_DESIGNER);
  }
}
