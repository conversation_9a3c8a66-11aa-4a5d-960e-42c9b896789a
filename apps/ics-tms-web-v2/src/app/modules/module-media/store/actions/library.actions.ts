import { createAction, props } from '@ngrx/store';
import { LibraryParams, MediaLibrary } from '../../models/media-library.modal';
import { CommonResponseData } from 'src/app/models/ics-common-response.model';

export const loadSelectedMedia = createAction(
  '[File Library] Load Selected Media Data',
  props<{ params: LibraryParams; replace: boolean }>()
);

export const loadSelectedMediaSuccess = createAction(
  '[File Library] Load Selected Media Data Success',
  props<{
    selectedMediaData: CommonResponseData<MediaLibrary>;
    replace: boolean;
  }>()
);

export const loadSelectedMediaFailure = createAction(
  '[File Library] Load Selected Media Data Failure',
  props<{ error: string }>()
);

export const updateMediaName = createAction(
  '[File Library] Update Media Name',
  props<{ mediaId: string; mediaName: string }>()
);

export const deleteMedia = createAction(
  '[File Library] Delete Media',
  props<{ mediaId: string }>()
);

export const clearMediaData = createAction('[File Library] Clear Media Data');
