import { inject, Injectable } from '@angular/core';
import { Actions, ofType, createEffect } from '@ngrx/effects';
import { of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import * as mediaDownloadsActions from '../actions/media-downloads.actions';
import { MediaDownloadsService } from '../../services/media-downloads.service';
import { mediaDownloadsTransform } from '../../utils/media-utils';

@Injectable()
export class MediaDownloadsEffect {
  actions$ = inject(Actions);

  mediaDownloadsService = inject(MediaDownloadsService);

  loadMediaDownloads$ = createEffect(() =>
    this.actions$.pipe(
      ofType(mediaDownloadsActions.loadMediaDownloads),
      switchMap(action =>
        this.mediaDownloadsService.getMediaDownloadsData(action.params).pipe(
          map(data =>
            mediaDownloadsActions.loadMediaDownloadsSuccess({
              mediaDownloadsData: mediaDownloadsTransform(data),
            })
          ),
          catchError(error =>
            of(mediaDownloadsActions.loadMediaDownloadsFailure({ error }))
          )
        )
      )
    )
  );
}
