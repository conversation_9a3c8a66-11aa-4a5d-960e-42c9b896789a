import { inject, Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { catchError, filter, map, mergeMap } from 'rxjs/operators';
import { MediaLibraryService } from '../../services/media-library.service';
import * as LibraryActions from '../actions/library.actions';
import { transformMediaData } from '../../utils/media-utils';

@Injectable()
export class LibraryEffects {
  actions$ = inject(Actions);

  mediaLibraryService = inject(MediaLibraryService);

  clearMediaData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LibraryActions.loadSelectedMedia),
      filter(action => action.replace),
      map(() => LibraryActions.clearMediaData())
    )
  );

  loadSelectedMediaData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(LibraryActions.loadSelectedMedia),
      mergeMap(action =>
        this.mediaLibraryService.getSelectedMediaData(action.params).pipe(
          map(mediaData =>
            LibraryActions.loadSelectedMediaSuccess({
              selectedMediaData: transformMediaData(mediaData),
              replace: action.replace,
            })
          ),
          catchError(error =>
            of(
              LibraryActions.loadSelectedMediaFailure({
                error: error.message,
              })
            )
          )
        )
      )
    )
  );
}
