import { createReducer, on } from '@ngrx/store';
import { uniqBy } from 'lodash';
import {
  clearMediaData,
  deleteMedia,
  loadSelectedMedia,
  loadSelectedMediaFailure,
  loadSelectedMediaSuccess,
  updateMediaName,
} from '../actions/library.actions';
import { MediaLibraryResponse } from '../../models/media-library.modal';

export interface LibraryState {
  data: MediaLibraryResponse;
  loading: boolean;
  error: string;
}

export const initialState: LibraryState = {
  data: {
    resultsMetadata: {
      totalResults: 0,
      pageIndex: 0,
      pageSize: 0,
    },
    results: [],
  },
  loading: false,
  error: '',
};

export const LibraryReducer = createReducer(
  initialState,
  on(loadSelectedMedia, state => ({
    ...state,
    loading: true,
    error: '',
  })),
  on(loadSelectedMediaSuccess, (state, { selectedMediaData, replace }) => ({
    ...state,
    data: {
      resultsMetadata: selectedMediaData.resultsMetadata,
      results: replace
        ? selectedMediaData.results
        : uniqBy([...state.data.results, ...selectedMediaData.results], 'id'),
    },
    loading: false,
  })),

  on(loadSelectedMediaFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error,
  })),
  on(updateMediaName, (state, { mediaId, mediaName }) => ({
    ...state,
    data: {
      ...state.data,
      results: state.data.results.map(media =>
        media.id === mediaId ? { ...media, name: mediaName } : media
      ),
    },
  })),
  on(deleteMedia, (state, { mediaId }) => ({
    ...state,
    data: {
      ...state.data,
      resultsMetadata: {
        ...state.data.resultsMetadata,
        totalResults: state.data.resultsMetadata.totalResults - 1,
      },
      results: state.data.results.filter(media => media.id !== mediaId),
    },
  })),
  on(clearMediaData, state => ({
    ...state,
    data: {
      results: [],
      resultsMetadata: {
        totalResults: 0,
        pageIndex: 0,
        pageSize: 0,
      },
    },
    loading: true,
  }))
);
