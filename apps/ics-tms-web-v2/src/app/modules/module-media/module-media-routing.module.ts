import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MediaLibraryComponent } from './components/media-library/media-library.component';
import { ModuleMediaComponent } from './module-media.component';
import { EmptyComponent } from 'src/app/components/empty-component/empty.component';

const routes: Routes = [
  {
    path: '',
    component: ModuleMediaComponent,
    children: [
      { path: 'library', component: MediaLibraryComponent },
      {
        path: '**',
        component: EmptyComponent,
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ModuleMediaRoutingModule {}
