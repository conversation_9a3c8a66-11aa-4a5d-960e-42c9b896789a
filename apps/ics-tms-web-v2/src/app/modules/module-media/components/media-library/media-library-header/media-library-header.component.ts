import {
  Component,
  inject,
  OnD<PERSON>roy,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { isEmpty } from 'lodash';
import { BehaviorSubject, Observable } from 'rxjs';
import { getSelectedMediaDataItemsCount } from '../../../store/selectors/library.selectors';
import { MediaLibraryService } from '../../../services/media-library.service';
import {
  OVERFLOW_VISIBLE,
  WINDOW_CLASSES,
} from '../../../constants/appConstants';
import { MediaUploadDetailsComponent } from './media-upload-details/media-upload-details.component';
import { ToastService } from 'src/app/services/toast.service';
import { ModalConstants } from 'src/app/constants/appConstants';

@Component({
  selector: 'app-media-library-header',
  templateUrl: './media-library-header.component.html',
  styleUrls: ['./media-library-header.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class MediaLibraryHeaderComponent implements OnInit, OnDestroy {
  itemsCount$!: Observable<number>;

  isUploading$ = new BehaviorSubject<boolean>(false);

  private readonly store = inject(Store);

  private readonly mediaLibraryService = inject(MediaLibraryService);

  private readonly modalService = inject(NgbModal);

  private readonly toastService = inject(ToastService);

  ngOnInit(): void {
    this.itemsCount$ = this.store.select(getSelectedMediaDataItemsCount);
  }

  ngOnDestroy(): void {
    document.body.classList.remove(OVERFLOW_VISIBLE);
  }

  handleFileChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    const { files } = input;
    if (isEmpty(files)) return;

    this.isUploading$.next(true);
    document.body.classList.add(OVERFLOW_VISIBLE);

    const modalRef = this.modalService.open(MediaUploadDetailsComponent, {
      windowClass: WINDOW_CLASSES.UPLOAD_DETAILS_MODAL,
      backdrop: false,
      container: ModalConstants.CONTAINER_SELECTOR,
    });

    modalRef.componentInstance.files = files;

    modalRef.result.then(() => {
      this.isUploading$.next(false);
      input.value = '';
    });
  }

  mayEditMedia(): boolean {
    return this.mediaLibraryService.mayEditMedia();
  }
}
