import { Component, inject, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  FACE,
  MEDIA_DELETE_MESSAGE,
  MediaTypes,
  PROMPT_SET_MEDIA_DELETE_MESSAGE,
} from 'src/app/modules/module-media/constants/appConstants';
import { PublishStatus } from 'src/app/modules/module-media/models/media-library.modal';
import { MediaLibraryService } from 'src/app/modules/module-media/services/media-library.service';

@Component({
  selector: 'app-delete-asset-popup',
  templateUrl: './delete-asset-popup.component.html',
  styleUrls: ['./delete-asset-popup.component.scss'],
})
export class DeleteAssetPopupComponent {
  @Input() name!: string;

  @Input() id!: string;

  @Input() type!: string;

  @Input() properties!: Record<string, string>;

  @Input() publishStatus!: PublishStatus;

  mediaLibraryService = inject(MediaLibraryService);

  activeModal = inject(NgbActiveModal);

  deleteMedia() {
    this.mediaLibraryService.deleteMedia(this.id).subscribe({
      next: () => {
        this.activeModal.close(true);
      },
      error: () => {
        this.activeModal.close();
      },
    });
  }

  deleteMessage() {
    const assetName =
      this.type === MediaTypes.FONT
        ? `${this.properties?.[FACE] ?? ''}/${this.name}`
        : this.name;
    const message =
      assetName +
      (this.publishStatus.unpublished > 0
        ? PROMPT_SET_MEDIA_DELETE_MESSAGE
        : MEDIA_DELETE_MESSAGE);
    return message;
  }

  cancel() {
    this.activeModal.close();
  }
}
