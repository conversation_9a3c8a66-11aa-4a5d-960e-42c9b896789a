<header class="media-library-header">
  <div class="media-library-header-left-panel">
    <h1>Recent Media</h1>
    <p class="text-secondary">
      <ng-container *ngIf="itemsCount$ | async as count">
        <span *ngIf="count !== 0">{{ count }} items</span>
      </ng-container>
    </p>
  </div>

  <div *ngIf="mayEditMedia()" class="media-library-header-right-panel">
    <button
      type="button"
      color="primary"
      class="btn btn-sm btn-primary btn-box-shadow"
      (click)="fileInput.click()"
      [disabled]="isUploading$ | async"
    >
      Upload
    </button>
    <input
      type="file"
      #fileInput
      hidden
      (change)="handleFileChange($event)"
      multiple
    />
  </div>
</header>
