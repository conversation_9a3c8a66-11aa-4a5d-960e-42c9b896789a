<div
  ngbDropdown
  class="ics-group-filter-dropdown"
  #filterDropdown="ngbDropdown"
>
  <button
    class="btn btn-default-icon navbar-btn no-focus"
    ngbDropdownToggle
    [ngbTooltip]="tooltipText"
    placement="bottom"
    triggers="hover"
  >
    <span class="icon" [ngClass]="iconClass"></span>
  </button>
  <ul ngbDropdownMenu>
    <li class="ics-filter-heading">{{ title }}</li>

    <ng-container *ngFor="let group of filterGroups">
      <li class="ics-group-heading">{{ group.name }}</li>
      <li
        *ngFor="let opt of group.options"
        class="d-flex align-items-center justify-content-between"
        [ngClass]="{ 'ics-filter-selected': opt.selected }"
        (click)="setSelectedItem(group, opt.value)"
        (keyup)="setSelectedItem(group, opt.value)"
        tabindex="0"
      >
        <span class="text-truncate">{{ opt.value }}</span>
        <i
          *ngIf="opt.selected"
          class="fa-solid fa-circle-check ms-2 flex-shrink-0"
        ></i>
      </li>
    </ng-container>
  </ul>
</div>
