.media-display-container {
  .media-list-container {
    margin: 0;
    padding: 0;
    border: none;
    background-color: inherit;

    .bg-light-blue {
      background-color: var(--md-blue-600);
    }

    .bg-orange-yellow {
      background-color: var(--md-lime-800);
    }

    .bg-dark-blue {
      background-color: var(--md-indigo-600);
    }
    .card {
      box-shadow:
        0 0.1rem 0.3rem 0 var(--md-black-12),
        0 0.1rem 0.1rem 0 var(--md-black-14),
        0 0.2rem 0.1rem -0.1rem var(--md-black-12);
      margin: 0;
    }

    .media-grid-item {
      .card {
        border-width: 0;
        transition: all 0.15s ease-in-out;
        overflow: hidden;

        &:hover {
          cursor: pointer;
          transform: translate(0, -0.5rem);
          box-shadow: 0 1.2rem 0.7rem 0 var(--md-black-37);
        }

        .image-type-badge {
          font-size: 1.05rem;
          font-weight: bold;
          color: var(--white);
          position: absolute;
          z-index: 2;
          bottom: 0;
          left: 0;
          padding: 0.2rem 1rem;
        }

        .media-img {
          position: relative;
          background-color: var(--md-red-grey-700);
          border-radius: 0.2rem 0.2rem 0 0;

          &::before {
            display: block;
            width: 100%;
            padding-top: 75%;
            content: '';
          }

          img {
            position: absolute;
            top: 50%;
            left: 50%;
            max-width: 100%;
            max-height: 100%;
            transition: all 0.15s ease-in-out;
            transform: translate(-50%, -50%);
            background: 0 0;
          }
        }

        .media-font {
          position: relative;
          height: 27.1rem;
          display: flex;
          background-color: var(--md-red-grey-600);
          .pre-font {
            width: 100%;
            pre {
              position: relative;
              top: 50%;
              overflow: hidden;
              width: 100%;
              transform: translateY(-50%);
              text-align: center;
              white-space: pre-line;
              border: none;
              background: none;
              font-size: 1.3rem;
              display: block;
              margin: 0 !important;
              padding: 0.95rem;
              color: var(--md-grey-900);
            }
          }
        }

        .media-tag {
          height: 2.1rem;
        }

        .card-content {
          height: 12.5rem;
          padding: 1rem;

          .content {
            display: flex;
            flex-direction: column;
            gap: 0.32rem;

            .media-name-grid {
              overflow: hidden;
              max-height: 3.8rem;
            }

            p {
              margin: 0;
              color: var(--md-grey-800);
              font-size: 1.2rem;

              &:first-child {
                color: var(--black);
                font-weight: 600;
                font-size: 1.4rem;
              }
            }
          }
        }

        .footer {
          height: 0.3rem;
        }
      }
    }

    .media-list {
      padding: 0;
      margin: 0;
      box-shadow:
        0 0.1rem 0.3rem 0 var(--md-black-20),
        0 0.1rem 0.1rem 0 var(--md-black-14),
        0 0.2rem 0.1rem -0.1rem var(--md-black-12);
      .media-list-item {
        background-color: var(--white);
        padding: 0.8rem 2.4rem 0.8rem;
        align-items: center;
        border-top: 0.1rem solid var(--md-red-grey-600);
        display: flex;

        &:hover {
          cursor: pointer;
          background-color: var(--md-blue-grey-100);
        }

        &:first-child {
          border: 0;
          border-radius: 0.4rem 0.4rem 0 0;
        }

        &:last-child {
          border-radius: 0 0 0.4rem 0.4rem;
        }

        .content {
          display: flex;
          flex-direction: column;
          gap: 0.3rem;
          flex-grow: 1;

          .media-name-list {
            overflow: hidden;
            max-height: 2rem;
          }

          &:first-child {
            font-size: 1.4rem;
          }

          .image-type-badge {
            padding: 0.2em 0.6em 0.3em;
            font-size: 1.05rem;
            color: var(--white);
            align-self: flex-start;
            border-radius: 0.4rem;
            font-weight: bold;
          }
        }

        .uploaded-details {
          font-size: 1.2rem;
          color: var(--md-grey-800);
          flex-grow: 2;
        }
      }
    }
  }

  .back-to-top {
    display: flex;
    justify-content: center;
    padding: 3.5rem 1.2rem;

    .scroll-to-top {
      border: none;
      font-weight: 500;
      font-size: 1.4rem;
      background-color: transparent;
      color: var(--md-grey-700);
    }
  }
  .no-media-found {
    color: var(--md-grey-700);
    font-weight: bold;
    text-align: center;
    transition: none !important;
    border: none;
    padding: 2.5rem 0;
    > i {
      color: var(--md-grey-400);
      margin-bottom: 1rem;
      font-size: 7rem;
    }
    > small {
      font-weight: 400;
    }
  }
}

.media-modal-popup {
  .modal-dialog {
    width: 95%;
    margin: 3rem auto;
    .modal-content {
      width: 100% !important;
      background-clip: padding-box;
      outline: 0;
    }
  }
}

.media-modal-backdrop {
  opacity: 0.9 !important;
}
