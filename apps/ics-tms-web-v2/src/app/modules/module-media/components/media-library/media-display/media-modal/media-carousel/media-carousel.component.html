<div class="media-carousel-container">
  <div class="media-carousel-inner">
    <ng-container *ngFor="let item of items; let i = index">
      <div class="media-carousel-item" [ngClass]="getSlideClass(i)">
        <div class="media-embed-responsive">
          <div class="media-embed-responsive-item">
            <div
              class="media-carousel-item-flex"
              [ngClass]="{ font: type === 'FONT' }"
            >
              <ng-container
                *ngTemplateOutlet="
                  item.template;
                  context: { $implicit: item.data, index: i }
                "
              ></ng-container>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
  </div>

  <!-- Navigation Arrows -->
  <button
    type="button"
    class="media-carousel-control left"
    (click)="prev()"
    [class.disabled]="isPrevDisabled()"
    *ngIf="showControlers()"
  >
    <span aria-hidden="true" class="icon prev gicon-keyboard_arrow_left"></span>
    <span class="sr-only">previous</span>
  </button>

  <button
    type="button"
    class="media-carousel-control right"
    (click)="next()"
    [class.disabled]="isNextDisabled()"
    *ngIf="showControlers()"
  >
    <span
      aria-hidden="true"
      class="icon next gicon-keyboard_arrow_right"
    ></span>
    <span class="sr-only">next</span>
  </button>
</div>
