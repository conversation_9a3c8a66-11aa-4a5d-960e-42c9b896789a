import {
  Component,
  inject,
  OnD<PERSON>roy,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { Store } from '@ngrx/store';
import { isEqual } from 'lodash';
import { debounceTime, Subject, takeUntil } from 'rxjs';
import {
  CREATED_BY_ME_LABEL,
  DEFAULT_SIZE,
  DEFAULT_SORT,
  DEFAULT_USER,
  MEDIA_LIBRARY_SORTING_OPTIONS,
  NAME,
  SIZE,
  SIZES,
  USER,
  USER_PROP,
} from '../../../constants/appConstants';
import {
  MediaLibraryFilterGroup,
  Size,
  User,
} from '../../../models/media-library.modal';
import { MediaLibraryService } from '../../../services/media-library.service';
import { getLoginUserDetailsData } from 'src/app/store/selectors/globalStore.selectors';

@Component({
  selector: 'app-media-library-filter',
  templateUrl: './media-library-filter.component.html',
  styleUrls: ['./media-library-filter.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class MediaLibraryFilterComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  private readonly debounceTimeMs = 300;

  private readonly mediaLibraryService = inject(MediaLibraryService);

  private readonly store = inject(Store);

  searchControl = new FormControl<string>('');

  selectedTab!: string;

  isDisplayMediaListView!: boolean;

  readonly sizes = SIZES;

  readonly sortingOptions = MEDIA_LIBRARY_SORTING_OPTIONS;

  selectedSort = DEFAULT_SORT;

  uploader: User = DEFAULT_USER;

  selectedSize: Size = DEFAULT_SIZE;

  selectedUser: User = DEFAULT_USER;

  filterGroups: MediaLibraryFilterGroup[] = [];

  ngOnInit(): void {
    this.getUserInfo();
    this.initializeFilterGroups();
    this.setupSubscriptions();
    this.setupSearch();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private getUserInfo(): void {
    this.store
      .select(getLoginUserDetailsData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        if (data?.userId) {
          this.uploader = { id: data.userId, user: CREATED_BY_ME_LABEL };
          this.updateUserFilterGroup();
        }
      });
  }

  private initializeFilterGroups(): void {
    this.filterGroups = [
      {
        name: SIZE,
        options: this.sizes.map(size => ({
          group: SIZE,
          value: size.name,
          selected: this.selectedSize.name === size.name,
          obj: size,
        })),
      },
      {
        name: USER,
        options: [
          {
            group: USER,
            value: CREATED_BY_ME_LABEL,
            selected: this.selectedUser.user !== '',
            obj: this.uploader,
          },
        ],
      },
    ];
  }

  private setupSubscriptions(): void {
    this.mediaLibraryService.isDisplayMediaListView$
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => {
        this.isDisplayMediaListView = value;
      });

    this.mediaLibraryService
      .getSelectedTab()
      .pipe(takeUntil(this.destroy$))
      .subscribe(tab => {
        this.selectedTab = tab;
      });
  }

  private updateUserFilterGroup(): void {
    const userGroup = this.filterGroups.find(group => group.name === USER);
    if (userGroup) {
      userGroup.options = [
        {
          group: USER,
          value: CREATED_BY_ME_LABEL,
          selected: this.selectedUser.user !== '',
          obj: this.uploader,
        },
      ];
    }
  }

  modifyDisplayMediaView() {
    this.isDisplayMediaListView = !this.isDisplayMediaListView;
    this.mediaLibraryService.updateMediaDisplayType();
  }

  get isMediaListView(): boolean {
    return this.isDisplayMediaListView;
  }

  clearSearchedText(): void {
    this.searchControl.setValue('');
  }

  private setupSearch(): void {
    this.searchControl.valueChanges
      .pipe(debounceTime(this.debounceTimeMs), takeUntil(this.destroy$))
      .subscribe((value: string | null) => {
        const searchedValue = value?.trim() ?? '';
        this.performSearch(searchedValue);
      });
  }

  private performSearch(searchedValue: string): void {
    this.mediaLibraryService.setSearchedText(searchedValue);
  }

  onSortChange(sortByFilter: string) {
    if (isEqual(sortByFilter, this.selectedSort)) return;
    this.selectedSort = sortByFilter;
    this.mediaLibraryService.setSelectedOrder(sortByFilter);
  }

  private extractSelectedSize(groups: MediaLibraryFilterGroup[]): Size {
    const group = groups.find(g => g.name === SIZE);
    const selected = group?.options.find(opt => opt.selected);
    return selected?.obj && NAME in selected.obj
      ? (selected.obj as Size)
      : DEFAULT_SIZE;
  }

  private extractSelectedUser(groups: MediaLibraryFilterGroup[]): User {
    const group = groups.find(g => g.name === USER);
    const selected = group?.options.find(opt => opt.selected);
    return selected?.obj && USER_PROP in selected.obj
      ? (selected.obj as User)
      : DEFAULT_USER;
  }

  onFilterChange(filterGroups: MediaLibraryFilterGroup[]): void {
    this.selectedSize = this.extractSelectedSize(filterGroups);
    this.selectedUser = this.extractSelectedUser(filterGroups);
    this.initializeFilterGroups();
    this.mediaLibraryService.setFilters(this.selectedSize, this.selectedUser);
  }
}
