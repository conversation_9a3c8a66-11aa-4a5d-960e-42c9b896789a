.media-tabs-container {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  margin: -0.2rem;
  padding: 0;

  .selected {
    font-weight: bold;
    border-bottom: 0.2rem solid var(--color-primary) !important;
  }

  .media-item {
    text-transform: uppercase;
    font-size: 1.4rem;
    color: var(--color-primary);
    padding: 1.28rem 1.6rem;
    margin-right: 0.32rem;
    border-bottom: 0.2rem solid var(--md-red-grey-600);

    &:hover {
      cursor: pointer;
      color: var(--link-hover-color);
    }
  }
}
