.media-upload {
  box-shadow: none;
  font-size: 1.4rem;

  .header {
    border: none;
    background-color: var(--color-toast-bg);
    padding: 1rem 1.5rem;
    border-top-left-radius: 0.3rem;
    border-top-right-radius: 0.3rem;
    color: var(--color-white);
  }

  .body {
    padding: 0;
    margin: 0;
    overflow-x: hidden;
    overflow-y: auto;
    max-height: 32.8rem;

    li:not(:first-child) {
      border-top: 0.1rem solid var(--md-red-grey-600);
    }

    .media-item {
      padding: 0.6rem 0;
      display: flex;
      align-items: center;

      .media-type {
        color: var(--text-color-gray);
        padding-right: 1.5rem;
        padding-left: 1.5rem;
        text-align: center;
        i {
          font-size: 1.6rem;
        }

        .gicon-error,
        .video-uploaded {
          color: var(--label-danger);
        }

        .image-uploaded {
          color: var(--color-folder-icon);
        }

        .file-uploaded {
          color: var(--color-icon-default);
        }
      }

      .media-description {
        flex-direction: column;
        width: 73%;

        .media-name {
          color: var(--text-color-gray);
          word-wrap: break-word;
        }

        .upload-progress {
          color: var(--color-primary);
          position: relative;
          height: 0.4rem;
          margin-top: 0.5rem;
          margin-bottom: 0.3rem;
          border-radius: 0;
          transition: width 0.6s ease;
          background-color: var(--progress-bar-bg-shade-one);

          .progress-bar {
            box-shadow: inset 0 -0.1rem 0 rgba(0, 0, 0, 0.15);
          }
        }

        .error-file {
          color: var(--color-error-text);
          font-size: 83%;
        }
      }

      .check-item {
        margin-left: auto;
        padding: 0 0.5rem 0 1.5rem;
        color: var(--label-success);
        font-size: 2rem;
      }
    }

    .error-item {
      background-color: var(--color-white);
    }
  }
}
