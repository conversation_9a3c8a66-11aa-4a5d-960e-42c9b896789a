import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { MEDIA_TABS } from '../../../constants/appConstants';
import { MediaLibraryService } from '../../../services/media-library.service';

@Component({
  selector: 'app-media-tabs',
  templateUrl: './media-tabs.component.html',
  styleUrls: ['./media-tabs.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MediaTabsComponent implements OnInit, OnDestroy {
  readonly mediaTabs = MEDIA_TABS;

  selectedMediaTab$: Observable<string>;

  private readonly destroy$ = new Subject<void>();

  private readonly mediaLibraryService = inject(MediaLibraryService);

  constructor() {
    this.selectedMediaTab$ = this.mediaLibraryService.getSelectedTab();
  }

  ngOnInit(): void {
    // Initialize with first tab if no tab is currently selected
    this.selectedMediaTab$
      .pipe(takeUntil(this.destroy$))
      .subscribe(currentTab => {
        if (!currentTab || !this.mediaTabs.includes(currentTab)) {
          this.mediaLibraryService.setSelectedTab(this.mediaTabs[0]);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSelectionChange(tab: string): void {
    this.mediaLibraryService.setSelectedTab(tab);
  }
}
