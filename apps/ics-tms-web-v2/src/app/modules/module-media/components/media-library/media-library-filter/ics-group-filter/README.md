# ICS Group Filter Component

A reusable Angular component that provides a dropdown filter interface with grouped filter options.

## Features

- **Grouped Filters**: Organize filter options into logical groups
- **Single Selection Per Group**: Radio button behavior - only one option can be selected per group
- **Customizable**: Configurable title, tooltip, and icon

## Usage

### Basic Example

```html
<app-ics-group-filter
  [filterGroups]="myFilterGroups"
  [title]="'Filter By:'"
  [tooltipText]="'Filter Options'"
  [iconClass]="'gicon-filter'"
  (filterChange)="onFilterChange($event)"
>
</app-ics-group-filter>
```

### Component Properties

| Property       | Type                   | Default            | Description                       |
| -------------- | ---------------------- | ------------------ | --------------------------------- |
| `filterGroups` | `GenericFilterGroup[]` | `[]`               | Array of filter groups to display |
| `title`        | `string`               | `'Filter By:'`     | Title text at the top of dropdown |
| `tooltipText`  | `string`               | `'Filter Options'` | Tooltip text for the button       |
| `iconClass`    | `string`               | `'gicon-filter'`   | CSS class for the filter icon     |

### Events

| Event          | Type                                 | Description                           |
| -------------- | ------------------------------------ | ------------------------------------- |
| `filterChange` | `EventEmitter<GenericFilterGroup[]>` | Emitted when filter selections change |

### Data Structure

The component uses a generic `GenericFilterGroup` interface that accepts any type:

```typescript
interface GenericFilterGroup<T> {
  name: string;
  options: Array<FilterItem<T>>;
}

interface FilterItem<T> {
  group: string;
  value: string;
  selected: boolean;
  obj?: T;
}
```

### Example Data

```typescript
export class MyComponent {
  myFilterGroups: GenericFilterGroup[] = [
    {
      name: 'Size',
      options: [
        { group: 'Size', value: 'Small', selected: false },
        { group: 'Size', value: 'Medium', selected: false },
        { group: 'Size', value: 'Large', selected: true },
      ],
    },
    {
      name: 'User',
      options: [
        { group: 'User', value: 'Created by me', selected: false },
        { group: 'User', value: 'Shared with me', selected: false },
      ],
    },
  ];

  onFilterChange(filterGroups: GenericFilterGroup[]): void {
    // Handle filter changes - only one option can be selected per group
    const selectedFilters = filterGroups.flatMap(group =>
      group.options.filter(option => option.selected)
    );
    console.log('Selected filters:', selectedFilters);

    // Access the underlying object data if needed
    selectedFilters.forEach(filter => {
      console.log('Filter object:', filter.obj);
    });
  }
}
```

### Single Selection Per Group

The component enforces **radio button behavior** - only one option can be selected per group at a time:

- Clicking a selected option will deselect it
- Clicking an unselected option will deselect all other options in that group and select the clicked option
- This prevents conflicting filter selections within the same category
