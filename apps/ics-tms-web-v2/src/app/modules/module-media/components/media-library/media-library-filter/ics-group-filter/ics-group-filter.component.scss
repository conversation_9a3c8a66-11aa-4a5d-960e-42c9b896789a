.ics-group-filter-dropdown {
  .btn-default-icon {
    background-color: transparent;
    padding: 0 0.8rem;
    font-size: 2rem;

    &:focus,
    &:active {
      color: var(--md-grey-700);
    }

    &:hover {
      background-color: var(--md-black-26);
      color: var(--md-grey-900);
    }
  }

  .dropdown-toggle {
    &::after {
      display: none;
    }
  }

  .tooltip {
    transform: translate(-1.5rem, 3.6rem) !important;
    min-width: max-content;
    .tooltip-arrow {
      transform: translate(3rem, 0rem) !important;
    }
  }

  &.show {
    .btn-default-icon {
      background-color: var(--md-black-26);
      color: var(--md-grey-900);
    }
    .tooltip {
      opacity: 0 !important;
      .tooptip-inner {
        opacity: 0 !important;
      }
    }
  }

  .dropdown-menu {
    font-size: 1.4rem;
    top: 100%;
    left: 0;
    min-width: 18rem;
    padding: 0.313rem 0 !important;
    border: 0.1rem solid transparent;
    border-radius: 0.3rem;
    background-color: var(--white);
    background-clip: padding-box;
    box-shadow:
      0 0.1rem 0.3rem 0 var(--md-black-20),
      0 0.1rem 0.8rem 0 var(--md-black-14),
      0 0.2rem 0.1rem -0.1rem var(--md-black-12);
    cursor: pointer;
    list-style-type: none;
    padding: 0;
    margin: 0;

    @media (max-width: 767.98px) {
      min-width: 16rem;
      font-size: 1.3rem;
      left: auto;
      right: 0;
    }

    @media (max-width: 575.98px) {
      min-width: 14rem;
      font-size: 1.2rem;
      max-width: calc(100vw - 2rem);
    }

    .ics-filter-heading {
      font-size: 1.2rem;
      display: block;
      white-space: nowrap;
      color: var(--md-grey-700);
      padding: 0.3rem 2rem;

      &:hover {
        background-color: transparent;
        cursor: auto;
      }
    }

    .ics-group-heading {
      font-size: 1.2rem;
      color: var(--md-grey-600);
      margin-top: 0.2rem;
      display: block;
      white-space: nowrap;
      padding: 0.3rem 2rem;

      &:first-of-type {
        border-top: none;
        margin-top: 0;
      }

      &:hover {
        background-color: transparent;
        cursor: auto;
      }
    }

    .ics-filter-selected {
      background-color: var(--md-indigo-600);
      color: var(--white);
      &:hover {
        background-color: var(--md-indigo-600);
      }
      &:active {
        background-color: var(--md-indigo-600);
      }
    }

    li {
      position: relative;
      padding: 0.3rem 2rem;

      &:not(.ics-filter-heading):not(.ics-group-heading) {
        &:hover:not(.ics-filter-selected) {
          background-color: var(--md-blue-grey-500);
        }

        @media (max-width: 575.98px) {
          word-break: break-word;
        }
      }
    }
  }
}
