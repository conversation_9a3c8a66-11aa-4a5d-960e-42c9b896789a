.media-library-header {
  display: flex;
  justify-content: space-between;

  h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 400;
  }

  p {
    font-size: 1.3rem;
    min-height: 2.4rem;
  }
}

.upload-details-modal {
  width: 37rem !important;
  max-height: 37rem;
  margin-left: auto;
  margin-top: auto;
  bottom: 2rem;
  right: 2rem;
  overflow: visible;

  .modal-dialog {
    position: absolute;
    margin: 0;
    bottom: 0;
    width: 37rem !important;

    .modal-content {
      width: 100% !important;
      border: none;
      border-radius: 0.3rem !important;
      box-shadow:
        var(--md-black-14) 0rem 1.6rem 2.4rem 0.2rem,
        var(--md-black-12) 0rem 0.6rem 3rem 0.5rem,
        var(--md-black-20) 0rem 0.8rem 1rem -0.5rem;
      overflow: hidden;
    }
  }
}
