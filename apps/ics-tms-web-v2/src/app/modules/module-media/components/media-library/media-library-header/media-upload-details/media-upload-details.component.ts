import {
  HttpEvent,
  HttpEventType,
  HttpProgressEvent,
} from '@angular/common/http';
import {
  Component,
  ElementRef,
  HostListener,
  inject,
  Input,
  OnInit,
  ViewChild,
} from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { finalize, forkJoin, Observable, take, tap } from 'rxjs';
import {
  $EVENT,
  ASSET_NAME,
  ASSET_PACKAGE_NAME,
  DEVICE_TYPE,
  DOCUMENT_CLICK,
  ERROR_FILE,
  ERROR_ICON,
  ERROR_MSG,
  FILE,
  FILE_CONTENT,
  FILE_ICON,
  FILE_UPLOADED_ICON,
  FileTypePattern,
  FontTypes,
  IMAGE_ICON,
  IMAGE_UPLOADED_ICON,
  MediaErrorMessages,
  MediaTypes,
  NULL,
  TOAST_MESSAGES,
  VIDEO_ICON,
  VIDEO_UPLOADED_ICON,
} from 'src/app/modules/module-media/constants/appConstants';
import { UploadingFile } from 'src/app/modules/module-media/models/media-library.modal';
import { MediaLibraryService } from 'src/app/modules/module-media/services/media-library.service';
import {
  formatMessage,
  getRegexFromPattern,
} from 'src/app/modules/module-media/utils/media-utils';
import { UploadEvent } from 'src/app/modules/modules-remote/models/file-library.model';
import { ToastService } from 'src/app/services/toast.service';

@Component({
  selector: 'app-media-upload-details',
  templateUrl: './media-upload-details.component.html',
  styleUrls: ['./media-upload-details.component.scss'],
})
export class MediaUploadDetailsComponent implements OnInit {
  @ViewChild('mediaUpload') mediaUpload!: ElementRef;

  @Input() files!: File[];

  allFiles: UploadingFile[] = [];

  private readonly mediaLibraryService = inject(MediaLibraryService);

  private readonly activeModal = inject(NgbActiveModal);

  private readonly modalService = inject(NgbModal);

  private readonly toastService = inject(ToastService);

  private isToastShown = false;

  ngOnInit() {
    this.processFiles(this.files);
  }

  private processFiles(files: File[]): void {
    const normalizedFiles = Array.from(files ?? []);
    const uploadTasks = normalizedFiles
      .map((file, index) => this.prepareUpload(file, index))
      .filter(Boolean) as Observable<HttpEvent<UploadEvent>>[];

    if (uploadTasks.length === 0) {
      return;
    }

    forkJoin(uploadTasks)
      .pipe(
        finalize(() => {
          const successfullUploads = this.uploadedFiles();
          this.showToast(successfullUploads);
        })
      )
      .subscribe({
        next: () => {
          this.mediaLibraryService
            .getSelectedTab()
            .pipe(take(1))
            .subscribe(tab => {
              this.mediaLibraryService.setSelectedTab(tab);
            });
        },
      });
  }

  private prepareUpload(file: File, index: number) {
    let fileEntry = this.initializeFile(file, index);
    this.allFiles.push(fileEntry);

    if (fileEntry.isErrorFile) {
      fileEntry.uploadInProgress = false;
      return null;
    }

    const isAsset = getRegexFromPattern(FileTypePattern.ASSET).test(file.name);
    const isPackage = getRegexFromPattern(FileTypePattern.PACKAGE).test(
      file.name
    );
    const isFont = getRegexFromPattern(FileTypePattern.FONT).test(file.name);

    const fileToUpload = isFont ? this.createFontFileWithMime(file) : file;

    if (!isAsset && !isPackage) {
      fileEntry = this.markFileAsError(fileEntry);
      return null;
    }

    const formData = new FormData();
    formData.append(FILE_CONTENT, fileToUpload, fileToUpload.name);

    formData.append(
      isAsset ? ASSET_NAME : ASSET_PACKAGE_NAME,
      fileToUpload.name
    );

    if (isPackage) {
      formData.append(DEVICE_TYPE, NULL);
    }

    const upload$ = (
      isAsset
        ? this.mediaLibraryService.uploadAsset(formData)
        : this.mediaLibraryService.uploadAssetPackage(formData)
    ).pipe(
      tap((event: HttpEvent<UploadEvent>) =>
        this.trackUploadProgress(event, fileEntry)
      )
    );

    return upload$;
  }

  private createFontFileWithMime(file: File): File {
    const mimeType = getRegexFromPattern(FileTypePattern.TTF).test(file.name)
      ? FontTypes.TTF
      : FontTypes.OTF;

    const blob = new Blob([file], { type: mimeType });

    return new File([blob], file.name, {
      type: mimeType,
      lastModified: file.lastModified,
    });
  }

  private markFileAsError(fileEntry: UploadingFile): UploadingFile {
    const errorFile = fileEntry;
    errorFile.isErrorFile = true;
    errorFile.errorMsg = ERROR_MSG;
    errorFile.uploadProgress = 0;
    errorFile.uploadInProgress = false;
    return errorFile;
  }

  private initializeFile(file: File, id: number): UploadingFile {
    const fileName = file?.name?.replace(/\s+/g, '_') ?? '';
    const fileSizeGB = parseFloat((file.size / 1024 ** 3).toFixed(1));
    const isTooLarge = fileSizeGB > 1;
    const isFileNameCorrect = getRegexFromPattern(
      FileTypePattern.VALID_NAME
    ).test(fileName);
    const isErrorFile = isTooLarge || !isFileNameCorrect;

    let errorMsg = '';

    if (isTooLarge) {
      errorMsg = MediaErrorMessages.GB;
    } else if (!isFileNameCorrect) {
      errorMsg = `${file.name}${MediaErrorMessages.SPECIAL_CHARS}`;
    }
    return {
      id,
      isErrorFile,
      errorMsg,
      name: fileName,
      uploadProgress: isErrorFile ? 0 : 100,
      uploadInProgress: !isErrorFile,
      type: this.determineFileType(fileName),
    };
  }

  private determineFileType(fileName: string): string {
    if (getRegexFromPattern(FileTypePattern.IMAGE).test(fileName))
      return MediaTypes.IMAGE.toLowerCase();
    if (getRegexFromPattern(FileTypePattern.VIDEO).test(fileName))
      if (getRegexFromPattern(FileTypePattern.EXPANDED_FONT).test(fileName))
        return MediaTypes.FONT.toLowerCase();
    return FILE;
  }

  private trackUploadProgress(
    event: HttpEvent<UploadEvent>,
    file: UploadingFile
  ): void {
    if (event.type === HttpEventType.UploadProgress) {
      const progress = event as HttpProgressEvent;
      if (progress.loaded && progress.total) {
        const progressPercent = Math.round(
          (progress.loaded * 100) / progress.total
        );

        const fileToUpdate = this.allFiles.find(f => f.id === file.id);
        if (fileToUpdate) {
          fileToUpdate.uploadProgress = progressPercent;
          if (progressPercent === 100) {
            fileToUpdate.uploadInProgress = false;
          }
        }
      }
    }
  }

  uploadedFiles() {
    if (this.allFiles) {
      return this.allFiles.filter(file => file.uploadProgress === 100).length;
    }
    return 0;
  }

  getIcon(file: UploadingFile) {
    if (file.isErrorFile) return ERROR_ICON;
    const { type, uploadInProgress } = file;

    switch (type) {
      case MediaTypes.IMAGE.toLowerCase():
        return uploadInProgress ? IMAGE_ICON : IMAGE_UPLOADED_ICON;
      case MediaTypes.VIDEO.toLowerCase():
        return uploadInProgress ? VIDEO_ICON : VIDEO_UPLOADED_ICON;
      case FILE:
        return uploadInProgress ? FILE_ICON : FILE_UPLOADED_ICON;
      default:
        return '';
    }
  }

  getError(file: UploadingFile) {
    return file.isErrorFile ? ERROR_FILE : '';
  }

  @HostListener(DOCUMENT_CLICK, [$EVENT])
  onDocumentClick(event: MouseEvent): void {
    const clickedInside =
      this.modalService.hasOpenModals() &&
      event.target instanceof Node &&
      this.mediaUpload.nativeElement.contains(event.target);
    if (!clickedInside) {
      const successfullUploads = this.uploadedFiles();
      const totalValid = this.allFiles.filter(f => !f.isErrorFile).length;
      if (successfullUploads === totalValid && !this.isToastShown) {
        this.showToast(successfullUploads);
      }
      this.activeModal.close();
    }
  }

  private showToast(successfullUploads: number) {
    this.isToastShown = true;
    const message = formatMessage(TOAST_MESSAGES.UPLOAD_SUCCESS, {
      count: successfullUploads,
      total: this.allFiles?.length ?? 0,
    });

    this.toastService.show({
      message,
    });
  }
}
