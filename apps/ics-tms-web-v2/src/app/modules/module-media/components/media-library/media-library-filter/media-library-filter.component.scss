.media-filter-container {
  display: flex;
  align-items: center;
  gap: 0 1.12rem;

  .btn-default-icon {
    background-color: transparent;
    padding: 0 0.8rem;
    font-size: 2rem;

    &:focus,
    &:active {
      color: var(--md-grey-700);
    }

    &:hover {
      background-color: var(--md-black-26);
      color: var(--md-grey-900);
    }
  }
  .tooltip {
    transform: translate(-1.5rem, 3.6rem) !important;
    min-width: max-content;
  }
  .search-container {
    position: relative;
    .clear-button {
      position: absolute;
      z-index: 1;
      top: 0.2rem;
      right: 0.2rem;
      width: 2.4rem;
      height: 2.4rem;
      border: none;
      background-color: var(--white);
      color: var(--md-grey-400);
      font-size: 1.6rem;
      padding: 0.5rem;
    }
  }
}
