import {
  Component,
  EventEmitter,
  Input,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { GenericFilterGroup } from '../../../../models/media-library.modal';
import { GroupFilterDefaults } from 'src/app/modules/module-media/constants/appConstants';

@Component({
  selector: 'app-ics-group-filter',
  templateUrl: './ics-group-filter.component.html',
  styleUrls: ['./ics-group-filter.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class IcsGroupFilterComponent<T> {
  @Input() filterGroups: GenericFilterGroup<T>[] = [];

  @Input() title: string = GroupFilterDefaults.TITLE;

  @Input() tooltipText: string = GroupFilterDefaults.TOOLTIP_TEXT;

  @Input() iconClass: string = GroupFilterDefaults.ICON_CLASS;

  @Output() filterChange = new EventEmitter<GenericFilterGroup<T>[]>();

  setSelectedItem(group: GenericFilterGroup<T>, value: string): void {
    const updatedOptions = group.options.map(opt => ({
      ...opt,
      selected: opt.value === value ? !opt.selected : false,
    }));

    const updatedGroup: GenericFilterGroup<T> = {
      ...group,
      options: updatedOptions,
    };

    this.filterGroups = this.filterGroups.map(g =>
      g.name === group.name ? updatedGroup : g
    );

    this.filterChange.emit(this.filterGroups);
  }
}
