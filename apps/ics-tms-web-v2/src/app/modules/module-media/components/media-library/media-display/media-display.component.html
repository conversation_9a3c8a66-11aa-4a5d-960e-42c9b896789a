<ng-container *ngIf="vm$ | async as vm">
  <div class="media-display-container">
    <div
      class="media-list-container"
      [ngClass]="{ card: vm.isListView }"
      *ngIf="(vm.mediaData?.length ?? 0) > 0 || !vm.isLoading"
    >
      <!-- grid view  -->
      <div class="row row-gap-3" *ngIf="!vm.isListView">
        <div
          class="media-grid-item col-lg-3 col-md-4 col-sm-6"
          *ngFor="let mediaItem of vm.mediaData; trackBy: trackById"
          (click)="openMediaModal(mediaItem?.id)"
        >
          <div class="card">
            <!-- Image  -->
            <div
              [ngClass]="{
                'media-img': mediaItem?.type !== 'FONT',
                'media-font': mediaItem?.type === 'FONT',
              }"
            >
              <img
                *ngIf="
                  (mediaItem?.thumbnailUrl || mediaItem?.sourceUrl) &&
                  mediaItem?.type !== 'FONT'
                "
                [src]="imageSrc(mediaItem)"
                [ngClass]="{
                  'd-none': !mediaItem?.thumbnailUrl && !mediaItem?.sourceUrl,
                }"
                [alt]="mediaItem?.name"
                (error)="onImageError($event)"
                loading="lazy"
              />
              <div *ngIf="mediaItem?.type === 'FONT'" class="pre-font">
                <pre
                  [id]="mediaItem?.id"
                  contenteditable="false"
                  [style.font-family]="'\'' + mediaItem?.id + '\''"
                >
              {{ fontPreviewChars }}</pre
                >
              </div>
              <!-- Media Tag : Image, Video  -->
              <div class="media-tag">
                <div
                  class="image-type-badge"
                  [ngClass]="mediaTypeBadgeMap[mediaItem?.type!]"
                >
                  {{ mediaItem?.type }}
                </div>
              </div>
            </div>
            <!-- content  -->
            <div class="card-content">
              <div class="content">
                <p class="media-name-grid">{{ mediaItem?.name }}</p>
                <p>{{ mediaItem?.uploader?.fullName }}</p>
                <p>
                  {{ formatDate(mediaItem?.uploaded) }}
                  (UTC+05:30)
                </p>
              </div>
            </div>
            <footer
              class="footer"
              [ngClass]="mediaTypeBadgeMap[mediaItem?.type!]"
            ></footer>
          </div>
        </div>
      </div>
      <!-- list view  -->
      <ul class="media-list" *ngIf="vm.isListView">
        <li
          class="media-list-item"
          *ngFor="let mediaItem of vm.mediaData; trackBy: trackById"
          (click)="openMediaModal(mediaItem?.id)"
        >
          <div class="content w-25">
            <p class="m-0 fw-bold media-name-list">{{ mediaItem.name }}</p>
            <div
              class="image-type-badge"
              [ngClass]="mediaTypeBadgeMap[mediaItem?.type!]"
            >
              <p class="m-0 fw-medium">{{ mediaItem?.type }}</p>
            </div>
          </div>
          <div class="uploaded-details">
            <p class="m-0">{{ mediaItem?.uploader?.fullName }}</p>
            <p class="m-0">
              {{ formatDate(mediaItem?.uploaded) }}
              (UTC+05:30)
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div class="clearfix col-xs-12" *ngIf="!vm.isLoading">
      <div class="back-to-top" *ngIf="vm.totalResults > 29">
        <button class="scroll-to-top" (click)="scrollToTop()">
          Back to top
        </button>
      </div>
      <div class="no-media-found" *ngIf="vm.totalResults === 0">
        <i [ngClass]="noMediaFoundText.icon"></i>
        <div aria-label="No Media Found">
          {{ noMediaFoundText.title }}
        </div>
        <small>{{ noMediaFoundText.desc }}</small>
      </div>
    </div>
    <div *ngIf="vm.isLoading">
      <app-ics-loader></app-ics-loader>
    </div>
  </div>
</ng-container>
