import { Component, Input, Output, EventEmitter, inject } from '@angular/core';
import { Store } from '@ngrx/store';
import { BehaviorSubject } from 'rxjs';
import { getSelectedMediaDataItemsCount } from 'src/app/modules/module-media/store/selectors/library.selectors';
import { MediaLibraryService } from 'src/app/modules/module-media/services/media-library.service';
import { SlideTemplate } from 'src/app/modules/module-media/models/media-library.modal';
import { MediaTypes } from 'src/app/modules/module-media/constants/appConstants';

@Component({
  selector: 'app-media-carousel',
  templateUrl: './media-carousel.component.html',
  styleUrls: ['./media-carousel.component.scss'],
})
export class MediaCarouselComponent {
  @Input() items: SlideTemplate[] = [];

  @Input() activeIndex = 0;

  @Input() type: string = MediaTypes.IMAGE;

  @Output() activeIndexChange = new EventEmitter<number>();

  private readonly store = inject(Store);

  private readonly mediaLibraryService = inject(MediaLibraryService);

  public readonly mediaCount$ = new BehaviorSubject<number>(0);

  ngOnInit() {
    this.store.select(getSelectedMediaDataItemsCount).subscribe(count => {
      this.mediaCount$.next(count);
    });
  }

  next(): void {
    if (
      this.activeIndex === this.items.length - 4 &&
      this.mediaCount$.getValue() > this.items.length
    ) {
      this.mediaLibraryService.triggerLoadMore();
    }
    this.activeIndex = (this.activeIndex + 1) % this.items.length;
    this.activeIndexChange.emit(this.activeIndex);
  }

  prev(): void {
    if (this.activeIndex <= 0) return;
    this.activeIndex =
      (this.activeIndex - 1 + this.items.length) % this.items.length;
    this.activeIndexChange.emit(this.activeIndex);
  }

  isPrevDisabled(): boolean {
    return this.activeIndex <= 0;
  }

  isNextDisabled(): boolean {
    return (
      this.mediaCount$.getValue() <= this.items.length &&
      this.activeIndex >= this.items.length - 1
    );
  }

  showControlers(): boolean {
    return this.items.length > 1;
  }

  getSlideClass(index: number): { [key: string]: boolean } {
    return {
      active: index === this.activeIndex,
      prev: index === this.activeIndex - 1,
      next: index === this.activeIndex + 1,
      hidden: index < this.activeIndex - 1 || index > this.activeIndex + 1,
    };
  }
}
