<div class="media-upload" #mediaUpload>
  <div class="header">
    <span>{{ uploadedFiles() }} of {{ files.length }} items uploaded</span>
  </div>
  <ul class="body">
    <li
      class="media-item"
      *ngFor="let file of allFiles"
      [ngClass]="{
        'error-item': file.isErrorFile && !file.uploadInProgress,
      }"
    >
      <div class="media-type">
        <i [ngClass]="getIcon(file)"></i>
      </div>

      <div class="media-description">
        <span class="media-name">{{ file?.name }}</span>
        <ng-container
          *ngIf="!file?.isErrorFile && file.uploadInProgress; else errorBlock"
        >
          <ngb-progressbar
            class="upload-progress"
            [value]="file.uploadProgress"
            [striped]="true"
            [animated]="true"
          ></ngb-progressbar>
        </ng-container>

        <ng-template #errorBlock>
          <div [ngClass]="getError(file)">
            {{ file?.errorMsg }}
          </div>
        </ng-template>
      </div>
      <div
        *ngIf="!file.uploadInProgress && !file.isErrorFile"
        class="check-item"
      >
        <i class="gicon-check_circle"></i>
      </div>
    </li>
  </ul>
</div>
