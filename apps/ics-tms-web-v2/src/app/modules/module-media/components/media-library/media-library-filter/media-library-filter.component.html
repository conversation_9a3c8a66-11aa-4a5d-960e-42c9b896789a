<div class="media-filter-container">
  <app-ics-sort
    [sortByFilters]="sortingOptions"
    [defaultSortBy]="selectedSort"
    (sortByFilterChange)="onSortChange($event)"
  ></app-ics-sort>
  <ng-container *ngIf="selectedTab !== 'FONTS'">
    <app-ics-group-filter
      [filterGroups]="filterGroups"
      [title]="'Filter By:'"
      [tooltipText]="'Filter Options'"
      [iconClass]="'gicon-filter'"
      (filterChange)="onFilterChange($event)"
    >
    </app-ics-group-filter>
  </ng-container>
  <button
    class="btn btn-default-icon navbar-btn no-focus change-view"
    (click)="modifyDisplayMediaView()"
  >
    <span
      *ngIf="!isMediaListView"
      ngbTooltip="List View"
      placement="bottom"
      class="gicon-list"
    ></span>
    <span
      *ngIf="isMediaListView"
      ngbTooltip="Grid View"
      placement="bottom"
      class="gicon-view_gird"
    ></span>
  </button>

  <div class="search-container">
    <input
      type="text"
      placeholder="Search"
      class="ics-input"
      [formControl]="searchControl"
      [spellcheck]="false"
    />
    <button
      type="button"
      class="btn clear-button"
      *ngIf="(searchControl.value ?? '').trim().length > 0"
      (click)="clearSearchedText()"
    >
      <i class="gicon-cancel"></i>
    </button>
  </div>
</div>
