import { Component, inject, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { MEDIA_MANAGEMENT } from 'src/app/constants/appConstants';
import { setHeaderName } from 'src/app/store/actions/globalStore.actions';

@Component({
  selector: 'app-module-media',
  templateUrl: './module-media.component.html',
})
export class ModuleMediaComponent implements OnInit {
  store = inject(Store);

  ngOnInit(): void {
    setTimeout(() => {
      this.store.dispatch(setHeaderName({ name: MEDIA_MANAGEMENT }));
    });
  }
}
