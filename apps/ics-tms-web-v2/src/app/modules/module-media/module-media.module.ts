import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  NgbAccordionModule,
  NgbDropdownModule,
  NgbModalModule,
  NgbPaginationModule,
  NgbProgressbarModule,
  NgbToast,
  NgbTooltipModule,
} from '@ng-bootstrap/ng-bootstrap';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { SharedModuleModule } from '../../shared-module/shared-module.module';
import { MaterialModule } from '../material.module';
import { MediaLibraryFilterComponent } from './components/media-library/media-library-filter/media-library-filter.component';
import { MediaDisplayComponent } from './components/media-library/media-display/media-display.component';
import { DeleteAssetPopupComponent } from './components/media-library/media-display/media-modal/delete-asset-popup/delete-asset-popup.component';
import { MediaModalComponent } from './components/media-library/media-display/media-modal/media-modal.component';
import { MediaLibraryHeaderComponent } from './components/media-library/media-library-header/media-library-header.component';
import { MediaUploadDetailsComponent } from './components/media-library/media-library-header/media-upload-details/media-upload-details.component';
import { MediaLibraryComponent } from './components/media-library/media-library.component';
import { MediaTabsComponent } from './components/media-library/media-tabs/media-tabs.component';
import { ModuleMediaRoutingModule } from './module-media-routing.module';
import { ModuleMediaComponent } from './module-media.component';
import { LibraryEffects } from './store/effects/library.effects';
import { MediaDownloadsEffect } from './store/effects/media-downloads.effects';
import { PromptSetsEffects } from './store/effects/prompt-sets.effects';
import { reducers } from './store/media.store';
import { IcsGroupFilterComponent } from './components/media-library/media-library-filter/ics-group-filter/ics-group-filter.component';
import { MediaCarouselComponent } from './components/media-library/media-display/media-modal/media-carousel/media-carousel.component';

@NgModule({
  declarations: [
    ModuleMediaComponent,
    MediaLibraryComponent,
    MediaDisplayComponent,
    DeleteAssetPopupComponent,
    MediaLibraryHeaderComponent,
    MediaLibraryFilterComponent,
    MediaTabsComponent,
    MediaModalComponent,
    MediaUploadDetailsComponent,
    IcsGroupFilterComponent,
    MediaCarouselComponent,
  ],
  imports: [
    CommonModule,
    ModuleMediaRoutingModule,
    MaterialModule,
    StoreModule.forFeature('media', reducers),
    EffectsModule.forFeature([
      LibraryEffects,
      MediaDownloadsEffect,
      PromptSetsEffects,
    ]),
    SharedModuleModule,
    FormsModule,
    NgbDropdownModule,
    NgbProgressbarModule,
    NgbToast,
    NgbTooltipModule,
    NgbAccordionModule,
    NgbPaginationModule,
    NgbModalModule,
    ReactiveFormsModule,
  ],
})
export class ModuleMediaModule {}
