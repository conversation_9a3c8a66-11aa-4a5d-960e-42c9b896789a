import { getBaseUrl } from 'src/app/constants/api';
import { ID } from 'src/app/constants/appConstants';

export const getApiConstants = () => {
  const UI_BASE_URL = `${getBaseUrl()}/notifications/ui`;
  return {
    notifications: {
      ui: {
        url: UI_BASE_URL,
        unreadcount: `${UI_BASE_URL}/unreadcount`,
        markAllAsRead: `${UI_BASE_URL}/markallread`,
      },
    },
    offlinePackages: {
      getOfflinePackages: `${getBaseUrl()}/offlinepackage`,
      downloadPackage: `${getBaseUrl()}/${ID}/package`,
    },
    rki: {
      rkiDetails: {
        getRkiDetails: `${getBaseUrl()}/rki/requests`,
      },
    },
    devices: {
      getDevice: `${getBaseUrl()}/devices`,
    },
    fileUploadRequest: {
      getPackageName: `${getBaseUrl()}/fileuploadrequests/${ID}`,
      downloadPackage: `${getBaseUrl()}/fileuploadrequests/${ID}/package`,
    },
  };
};
