export interface Notification {
  created: string;
  id: string;
  level: string;
  message: string;
  read: boolean;
  relatedEntity: RelatedEntity;
  type: string;
  kind?: string;
  isDisabled?: boolean;
}

export interface RelatedEntity {
  type: string;
  id: string;
  href?: string;
  path?: string;
}

export interface ResultsMetadata {
  totalResults: number;
  pageIndex: number;
  pageSize: number;
}
export interface NotificationParams {
  [key: string]: any;
  autoPoll?: boolean;
  pageIndex?: number;
  pageSize?: number;
  read?: boolean;
}

export interface MarkAsReadPayload {
  read?: boolean;
}

export interface CategorizedNotification {
  name: string;
  data: Notification[];
}

export interface NotificationData {
  categorizedNotifications: CategorizedNotification[];
  resultsMetadata: ResultsMetadata;
}

export interface RkiDetail {
  id: string;
  name: string;
  creator: CreatorOrAuthorizer;
  authorizer: CreatorOrAuthorizer;
  secondAuthorizer?: null;
  created: string;
  expires: string;
  status: number;
  progress: Progress;
  keyGroupRef: string;
  companyId: string;
  approvers?: ApproversEntity[] | null;
  deviceRequests?: DeviceRequestsEntity[] | null;
}

export interface CreatorOrAuthorizer {
  id: string;
  fullName: string;
}
export interface Progress {
  total: number;
  inProgress: number;
  completed: number;
  failed: number;
}
export interface ApproversEntity {
  id: string;
  fullName: string;
  email: string;
  status: number;
}
export interface DeviceRequestsEntity {
  id: string;
  device: Device;
  status: number;
  message: string;
  originalKeyGroupRef: string;
}

export interface Device {
  id: number;
  deviceType: OwnerOrDeviceType;
  serialNumber: string;
  name: string;
  keyGroupRef: string;
  site: Site;
}
export interface OwnerOrDeviceType {
  id: string;
  name: string;
}
export interface Site {
  id: string;
  name: string;
  latitude: string;
  longitude: string;
  owner: OwnerOrDeviceType;
}
export interface DateComparison {
  isToday: boolean;
  isYesterday: boolean;
  isThisMonth: boolean;
  monthName: string;
}

export interface JobFile {
  path?: string;
  applicationId?: string;
}

export interface PullFilePackage {
  cancelledJobs?: number;
  completeJobs?: number;
  completed?: string;
  failedJobs?: number;
  files?: JobFile[];
  id?: string;
  inProgressJobs?: number;
  jobsInNewState?: number;
  name?: string;
  packageUrl?: string;
  status?: number;
  totalJobs?: number;
}
