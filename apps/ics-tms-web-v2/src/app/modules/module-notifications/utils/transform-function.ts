import {
  ALARM,
  CONFIG_INSTANCE_TYPE,
  CUSTOM_ATTRIBUTE,
  FILE,
  MONTH_NAMES,
  NotificationCategoryType,
  NotificationEntityTypes,
  PackageNotificationTypes,
  PROMPT,
  RKI,
  THIS_MONTH,
  TODAY,
  YESTERDAY,
} from '../constants/appConstants';
import {
  CategorizedNotification,
  DateComparison,
  Notification,
} from '../models/notification.model';
import { CommonResponseData } from 'src/app/models/common';

export function transformNotificationData(
  notifications: CommonResponseData<Notification>
) {
  const updatedNotifications = categorizeNotifications(notifications.results);
  const categorizedNotifications = addTypeToNotifications(updatedNotifications);
  return {
    categorizedNotifications,
    resultsMetadata: notifications.resultsMetadata,
  };
}

export function categorizeNotifications(
  notifications: Notification[]
): CategorizedNotification[] {
  const categories = {
    today: [] as Notification[],
    yesterday: [] as Notification[],
    thisMonth: [] as Notification[],
    earlierMonths: new Map<string, Notification[]>(),
  };

  const now = new Date();
  const today = getDateOnly(now);
  const yesterday = getDateOnly(new Date(now.getTime() - 24 * 60 * 60 * 1000));

  notifications.forEach(notification => {
    const notificationDate = new Date(notification.created);
    const comparison = compareDates(notificationDate, now, today, yesterday);

    categorizeNotification(notification, comparison, categories);
  });

  return buildCategoryResults(categories);
}

function getDateOnly(date: Date): Date {
  return new Date(date.getFullYear(), date.getMonth(), date.getDate());
}

function compareDates(
  notificationDate: Date,
  now: Date,
  today: Date,
  yesterday: Date
): DateComparison {
  const notificationDateOnly = getDateOnly(notificationDate);
  const isCurrentYear = notificationDate.getFullYear() === now.getFullYear();

  return {
    isToday: notificationDateOnly.getTime() === today.getTime(),
    isYesterday: notificationDateOnly.getTime() === yesterday.getTime(),
    isThisMonth:
      isCurrentYear && notificationDate.getMonth() === now.getMonth(),
    monthName: getMonthNameWithYear(notificationDate, isCurrentYear),
  };
}

function getCategoryType(comparison: DateComparison): NotificationCategoryType {
  if (comparison.isToday) return NotificationCategoryType.TODAY_CATEGORY;
  if (comparison.isYesterday)
    return NotificationCategoryType.YESTERDAY_CATEGORY;
  if (comparison.isThisMonth)
    return NotificationCategoryType.THIS_MONTH_CATEGORY;
  return NotificationCategoryType.EARLIER_MONTH_CATEGORY;
}

function categorizeNotification(
  notification: Notification,
  comparison: DateComparison,
  categories: {
    today: Notification[];
    yesterday: Notification[];
    thisMonth: Notification[];
    earlierMonths: Map<string, Notification[]>;
  }
): void {
  const categoryType = getCategoryType(comparison);

  switch (categoryType) {
    case NotificationCategoryType.TODAY_CATEGORY:
      categories.today.push(notification);
      break;

    case NotificationCategoryType.YESTERDAY_CATEGORY:
      categories.yesterday.push(notification);
      break;

    case NotificationCategoryType.THIS_MONTH_CATEGORY:
      categories.thisMonth.push(notification);
      break;

    case NotificationCategoryType.EARLIER_MONTH_CATEGORY:
      if (!categories.earlierMonths.has(comparison.monthName)) {
        categories.earlierMonths.set(comparison.monthName, []);
      }
      categories.earlierMonths.get(comparison.monthName)!.push(notification);
      break;

    default:
      break;
  }
}

function buildCategoryResults(categories: {
  today: Notification[];
  yesterday: Notification[];
  thisMonth: Notification[];
  earlierMonths: Map<string, Notification[]>;
}): CategorizedNotification[] {
  const result: CategorizedNotification[] = [
    { name: TODAY, data: categories.today },
    { name: YESTERDAY, data: categories.yesterday },
    { name: THIS_MONTH, data: categories.thisMonth },
  ];

  // Add earlier months in chronological order (most recent first)
  const sortedMonths = Array.from(categories.earlierMonths.entries()).sort(
    ([, a], [, b]) => {
      // Sort by the most recent notification in each month
      const latestA = Math.max(...a.map(n => new Date(n.created).getTime()));
      const latestB = Math.max(...b.map(n => new Date(n.created).getTime()));
      return latestB - latestA;
    }
  );

  sortedMonths.forEach(([monthName, notifications]) => {
    result.push({ name: monthName, data: notifications });
  });

  return result;
}

function getMonthNameWithYear(date: Date, isCurrentYear: boolean): string {
  const monthName = getMonthName(date.getMonth());
  return isCurrentYear ? monthName : `${monthName} ${date.getFullYear()}`;
}

export function getMonthName(monthIndex: number): string {
  return MONTH_NAMES[monthIndex];
}

function addTypeToNotifications(notifications: CategorizedNotification[]) {
  return notifications.map(notification => ({
    ...notification,
    data: mapTypeToNotificationData(notification.data),
  }));
}

function mapTypeToNotificationData(data: Notification[]) {
  return data.map(item => {
    const type = item.type.toLowerCase();
    let kind: string;

    switch (true) {
      case type.startsWith(RKI):
        kind = RKI;
        break;
      case type.startsWith(FILE):
        kind = PackageNotificationTypes.DOWNLOAD;
        break;
      case type.startsWith(ALARM):
        kind = ALARM;
        break;
      case type.startsWith(NotificationEntityTypes.PROMPT_SET):
        kind = PROMPT;
        break;
      case type.startsWith(PackageNotificationTypes.OFFLINE):
        kind = PackageNotificationTypes.OFFLINE;
        break;
      case type.startsWith(CUSTOM_ATTRIBUTE):
        kind = CUSTOM_ATTRIBUTE;
        break;
      case type.startsWith(CONFIG_INSTANCE_TYPE):
        kind = CONFIG_INSTANCE_TYPE;
        break;
      default:
        kind = type;
    }

    return { ...item, kind, isDisabled: false };
  });
}
