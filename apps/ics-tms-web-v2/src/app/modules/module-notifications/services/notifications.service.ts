import { HttpClient, HttpContext, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { OfflinePackage } from '../../modules-remote/models/OfflinePackage.model';
import { getApiConstants } from '../constants/api';
import {
  BLOB,
  FileDownloadConstants,
  LINK_TAG,
  Messages,
} from '../constants/appConstants';
import {
  MarkAsReadPayload,
  Notification,
  NotificationParams,
  RkiDetail,
} from '../models/notification.model';
import { ToastService } from 'src/app/services/toast.service';
import { CommonResponseData } from 'src/app/models/ics-common-response.model';
import { ID, SKIP_ERROR_TOAST } from 'src/app/constants/appConstants';

@Injectable({
  providedIn: 'root',
})
export class NotificationsService {
  httpClient = inject(HttpClient);

  toastService = inject(ToastService);

  getNotifications(
    params: NotificationParams
  ): Observable<CommonResponseData<Notification>> {
    const httpParams = new HttpParams({ fromObject: params });
    const url = `${getApiConstants().notifications.ui.url}`;
    return this.httpClient.get<CommonResponseData<Notification>>(url, {
      params: httpParams,
    });
  }

  getUnreadcounts(params: NotificationParams): Observable<number> {
    const httpParams = new HttpParams({ fromObject: params });
    const url = `${getApiConstants().notifications.ui.unreadcount}`;
    return this.httpClient.get<number>(url, { params: httpParams });
  }

  markNotificationAsRead(
    notificationId: string,
    payload: MarkAsReadPayload
  ): Observable<Notification> {
    const url = `${getApiConstants().notifications.ui.url}/${notificationId}`;
    return this.httpClient.put<Notification>(url, payload);
  }

  markAllAsRead() {
    const url = `${getApiConstants().notifications.ui.markAllAsRead}`;
    return this.httpClient.post(url, {});
  }

  getOfflinePackage(notification: Notification) {
    const url = `${getApiConstants().offlinePackages.getOfflinePackages}/${
      notification.relatedEntity.id
    }`;
    return this.httpClient.get<OfflinePackage>(url, {
      context: new HttpContext().set(SKIP_ERROR_TOAST, true),
    });
  }

  downloadOfflinePackage(offlinePackage: OfflinePackage) {
    const newurl = `${getApiConstants().offlinePackages.getOfflinePackages}/${
      offlinePackage.id
    }/content`;
    return this.httpClient.get(newurl, { responseType: BLOB });
  }

  getRkiDetail(rkiDetailsId: string) {
    const url = `${
      getApiConstants().rki.rkiDetails.getRkiDetails
    }/${rkiDetailsId}`;
    return this.httpClient.get<string | RkiDetail>(url);
  }

  getPackageUrl(id: string) {
    return this.httpClient.get(
      `${getApiConstants().fileUploadRequest.getPackageName.replace(ID, id)}`
    );
  }

  downloadPackage(id: string, packageName?: string) {
    const url = `${getApiConstants().fileUploadRequest.downloadPackage.replace(
      ID,
      id
    )}`;
    this.httpClient
      .get(url, {
        responseType: BLOB,
        context: new HttpContext().set(SKIP_ERROR_TOAST, true),
      })
      .subscribe({
        next: response => {
          const blob = new Blob([response], {
            type: FileDownloadConstants.ZIP_MIME_TYPE,
          });
          const href = window.URL.createObjectURL(blob);
          const link = document.createElement(LINK_TAG);
          link.href = href;
          link.download = `${packageName}.${FileDownloadConstants.ZIP_EXTENSION}`;
          link.click();
        },
        error: err => {
          if (err.status === 403)
            this.toastService.show({
              message: Messages.NO_PERMISSION_TO_DOWNLOAD,
            });
        },
      });
  }

  getDevice(id: string) {
    const url = `${getApiConstants().devices.getDevice}/${id}`;
    return this.httpClient.get(url);
  }
}
