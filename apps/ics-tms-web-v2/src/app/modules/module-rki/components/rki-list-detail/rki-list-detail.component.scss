.rki-list-details-container {
  padding: 24px;
  margin-bottom: 20px;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.2),
    0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 2px 1px -1px rgba(0, 0, 0, 0.12);

  h3 {
    margin-bottom: 10px;
    font-size: 24px;
    font-weight: 300;
    line-height: 1.1;
    hyphens: auto;
    word-wrap: break-word;
    word-break: break-word;
  }

  .table > :not(caption) > * > * {
    padding: 0;
  }

  .p-10px {
    padding-right: 10px;
    padding-left: 10px;
  }

  .rki-table-detail {
    margin-bottom: 20px;

    tr {
      th {
        font-size: 14px;
        padding: 2px;
        vertical-align: top;
      }

      td {
        font-size: 14px;
        padding: 2px;
        vertical-align: top;
      }
    }
  }

  .device-details-container {
    max-height: 350px;
    display: flex;
    flex-direction: column;
    padding: 0;
    margin-bottom: 20px;
    list-style: none;
    overflow-x: hidden;
    overflow-y: auto;
    border-radius: 3px;
    box-shadow:
      0 1px 2px rgba(0, 0, 0, 0.3),
      0 -1px 3px -2px rgba(0, 0, 0, 0.2);

    .device-detail-item {
      padding: 10px 15px;
      border-top: none;
      border: 1px solid var(--md-red-grey-600);
      border-width: 0 0 1px 0;

      &:first-child {
        font-size: 1.2rem;
        color: var(--color-black-shade-two);
        font-weight: 700;
        border-top-right-radius: 3px;
        border-top-left-radius: 3px;
      }

      .name-serial {
        display: flex;
        flex-direction: column;
        font-size: 1.4rem;
        gap: 0.2rem;

        span:first-child {
          font-weight: 700;
        }

        &:hover {
          text-decoration: underline;
          cursor: pointer;
        }
      }

      .location-container {
        font-size: 1.4rem;

        &:hover {
          text-decoration: underline;
          cursor: pointer;
        }
      }

      .current-key-group-container {
        span {
          font-size: 1.4rem;
        }
      }

      .status-container {
        display: flex;
        flex-direction: column;
        gap: 0.2rem;
        font-size: 1.4rem;

        span:last-child {
          font-size: 1.2rem;
          color: var(--color-icon-default);
        }
      }
    }
  }

  .rki-details-btn-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;

    .accept-reject-btn-container {
      .approve-container {
        display: flex;
        gap: 1.5rem;
        align-items: center;

        .rki-approve-btn {
          padding: 0.6rem 1.2rem;
          border: none;
          border-radius: 0.3rem;
          color: var(--color-white);
          font-weight: 600;
          transition: all ease-in 0.1s;
          box-shadow:
            0 0.1rem 0.2rem rgba(0, 0, 0, 0.3),
            0 -0.1rem 0.3rem -0.2rem rgba(0, 0, 0, 0.2);
          font-size: 1.4rem;
        }

        .rki-btn-reject {
          color: var(--btn-color-red);
          font-weight: 600;
          background-color: var(--dropdown-border-color);
          padding: 0.6rem 1.2rem;
          font-size: 1.4rem;
          border-color: var(--dropdown-border-color);

          &:hover {
            background-color: var(--btn-color-red);
            color: var(--color-white);
            border-color: var(--btn-border-color-red);
          }
        }
      }
    }

    .rki-back-btn {
      .back-btn {
        display: block;
        margin-left: auto;
        background-color: var(--btn-cancel-color);
        font-weight: 500;

        &:hover {
          background-color: var(--btn-cancel-hover-color);
        }

        &:active {
          border-color: transparent;
        }
      }
    }
  }
}
