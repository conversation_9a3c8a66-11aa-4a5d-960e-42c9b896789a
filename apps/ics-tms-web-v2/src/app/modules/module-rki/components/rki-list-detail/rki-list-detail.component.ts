import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable, Subject, takeUntil } from 'rxjs';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';

import { RKI_TYPE } from '../../constants/appConstants';
import { RkiRequest, DeviceType } from '../../models/rki-detail.model';
import { KeyGroupsData } from '../../models/key-groups.model';
import { RkiUser } from '../../models/users-rki.model';
import { CreateUpdateRKIRequestService } from '../../services/create-update-rki.service';
import {
  loadRkiDetail,
  loadRkiDetailSuccess,
} from '../../store/actions/rki-detail.actions';
import { loadDeviceTypes } from '../../../../store/actions/device-types.actions';
import { loadRkiUsers } from '../../store/actions/rki-users.actions';
import {
  getRkiDetailData,
  isFetchingRkiRequest,
} from '../../store/selectors/rki-detail.selectors';
import { getLoginUserDetailsData } from '../../../../store/selectors/globalStore.selectors';
import { RkiFeeModalComponent } from '../create-rki-request/fee-modal/fee-modal.component';
import { RkiAuthModalComponent } from '../create-rki-request/auth-modal/auth-modal.component';
import { Company } from 'src/app/models/common';
import { fetchCompanyById } from 'src/app/store/actions/company.actions';
import { loadConsumers } from 'src/app/store/actions/consumers.actions';
import { getCompanyById } from 'src/app/store/selectors/company.selector';
import { getConsumersData } from 'src/app/store/selectors/consumers.selector';
import { ToastService } from 'src/app/services/toast.service';
import { AuthService } from 'src/app/services/auth.service';

@Component({
  selector: 'ics-rki-details',
  templateUrl: './rki-list-detail.component.html',
  styleUrls: ['./rki-list-detail.component.scss'],
})
export class RkiListDetailComponent implements OnInit, OnDestroy {
  // TO-DO : Rename 'rkiDeviceData' to 'rkiRequest'
  rkiDeviceData: RkiRequest | null = null;

  keyGroupsData: KeyGroupsData[] = [];

  rkiDeviceTypes$!: Observable<DeviceType[]>;

  rkiUsersData: RkiUser = { results: [] };

  selfUserName!: string;

  selfUserId!: string;

  selfCompanyId!: string;

  isServiceCompany: boolean = false;

  rkiTenant: Company | null = null;

  canEdit: boolean = false;

  isFetching: boolean = false;

  RKI_TYPE = RKI_TYPE;

  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    public route: ActivatedRoute,
    private store: Store,
    private modalService: NgbModal,
    private toast: ToastService,
    private rkiService: CreateUpdateRKIRequestService
  ) {
    const rkiRequestId = this.route.snapshot.params['id'];
    if (rkiRequestId) {
      // TO-DO : Change 'rkiDeviceId' to 'rkiRequestId'
      this.store.dispatch(loadRkiDetail({ rkiDeviceId: rkiRequestId }));
    }
    this.store
      .select(getLoginUserDetailsData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        if (data.fullName) this.selfUserName = data.fullName;
        if (data.sub) this.selfUserId = data.sub;
        if (data.company?.id) this.selfCompanyId = data.company?.id;
      });
  }

  ngOnInit(): void {
    this.store.dispatch(loadDeviceTypes({}));

    this.store
      .select(isFetchingRkiRequest)
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => {
        this.isFetching = value;
      });

    this.store
      .select(getRkiDetailData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(data => {
        if (data) {
          this.rkiDeviceData = data;

          const userHasRKI = AuthService.hasRole('RKI');
          const userIsApprover = this.rkiDeviceData.approvers?.some(
            member => member.id === this.selfUserId
          );
          const isUserCreator =
            this.selfUserId === this.rkiDeviceData.creator.id;
          // can only edit when user has the role, is the creator, and is not an approver
          this.canEdit = userHasRKI && isUserCreator && !userIsApprover;

          if (
            this.selfCompanyId != null &&
            this.rkiUsersData.results.length < 1
          ) {
            this.store.dispatch(
              loadRkiUsers({ companyId: this.selfCompanyId })
            );
          }
          if (this.rkiDeviceData?.companyId) {
            this.store.dispatch(
              fetchCompanyById({ companyId: this.rkiDeviceData.companyId })
            );
          }
          // get service companies
          this.store.dispatch(loadConsumers());
        }
      });
    // if service based companies exist then get rki tenant details
    this.store
      .select(getConsumersData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(consumers => {
        if (consumers?.length) {
          this.isServiceCompany = true;
        } else {
          this.isServiceCompany = false;
        }
      });
    this.store
      .select(getCompanyById)
      .pipe(takeUntil(this.destroy$))
      .subscribe(company => {
        this.rkiTenant = company;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    // clean the state
    this.store.dispatch(loadRkiDetailSuccess({ rkiDetailData: null }));
  }

  getStatusMessage(status: number): string {
    const message: { [key: number]: string } = {
      0: 'Receive Key Bundle Success',
      1: 'Receive Key Bundle Failed',
      2: 'Success',
      3: 'Failed',
      7: 'Job Ready',
    };
    return message[status];
  }

  navigate(siteId: string, deviceId?: number): void {
    let url = '';
    if (siteId && deviceId) {
      url = this.router
        .createUrlTree(['/sites', siteId, deviceId, 'overview'])
        .toString();
    } else {
      url = this.router.createUrlTree(['/sites', siteId]).toString();
    }
    window.open(url, '_blank');
  }

  getStatus(): string {
    if (this.rkiDeviceData?.status !== undefined) {
      const message: { [key: number]: string } = {
        0: 'Pending Approval',
        1: 'Cancelled',
        2: 'Rejected',
        5: 'In Progress',
        6: 'Completed',
        8: 'Pending Second Authorization',
      };
      return message[this.rkiDeviceData.status];
    }
    return '';
  }

  openRkiFeeModal(): NgbModalRef {
    return this.modalService.open(RkiFeeModalComponent, {
      container: '#ng-modal-container',
      windowClass: 'modal-primary-fee in',
      size: 'sm',
    });
  }

  openRkiAuthModal(rkiType: RKI_TYPE): NgbModalRef {
    const refRkiAuthModal = this.modalService.open(RkiAuthModalComponent, {
      container: '#ng-modal-container',
      windowClass: 'modal-primary in',
      size: 'sm',
    });
    refRkiAuthModal.componentInstance.rkiType = rkiType;

    return refRkiAuthModal;
  }

  handleApprove(): void {
    // create RKI package
    this.openRkiFeeModal().result.then((proceed: boolean) => {
      if (proceed) {
        this.openRkiAuthModal(RKI_TYPE.APPROVE).result.then(
          ({ confirmed, mfaCode }: { confirmed: boolean; mfaCode: string }) => {
            if (confirmed && this.rkiDeviceData) {
              this.rkiService
                .approveRejectRkiRequest(this.rkiDeviceData.id, {
                  approved: true,
                  mfaCode,
                })
                .subscribe({
                  next: () => {
                    this.toast.show({
                      message: 'RKI request has been approved',
                    });
                    this.router.navigate(['/rki']);
                  },
                  error: error => {
                    if (error?.status === 406) {
                      this.toast.show({ message: error?.error?.message });
                    } else {
                      console.error(
                        'Error while approving RKI request: ',
                        error
                      );
                    }
                  },
                });
            }
          }
        );
      }
    });
  }

  handleReject(): void {
    this.openRkiAuthModal(RKI_TYPE.REJECT).result.then(
      ({ confirmed, mfaCode }: { confirmed: boolean; mfaCode: string }) => {
        if (confirmed && this.rkiDeviceData) {
          this.rkiService
            .approveRejectRkiRequest(this.rkiDeviceData.id, {
              approved: false,
              mfaCode,
            })
            .subscribe({
              next: () => {
                this.toast.show({ message: 'RKI request has been rejected' });
                this.router.navigate(['/rki']);
              },
              error: error => {
                if (error?.status === 406) {
                  this.toast.show({ message: error?.error?.message });
                } else {
                  console.error('Error while rejecting RKI request: ', error);
                }
              },
            });
        }
      }
    );
  }

  isPendingForApproval(): boolean {
    if (this.rkiDeviceData) {
      return this.rkiDeviceData.status === 0;
    }
    return false;
  }

  needsUserApproval(): boolean {
    if (this.rkiDeviceData?.status !== 0 && this.rkiDeviceData?.status !== 8) {
      return false;
    }
    if (this.rkiDeviceData?.authorizer?.id === this.selfUserId) {
      return false;
    }

    return true;
  }

  isPendingForSecondApproval(): boolean {
    return (
      (this.rkiDeviceData?.status === 8 && this.needsUserApproval()) ||
      !this.rkiDeviceData
    );
  }
}
