<div class="row">
  <div class="col-lg-11">
    <div class="row">
      <div class="col-lg-10 col-md-offset-1">
        <app-ics-loader *ngIf="isFetching; else content"></app-ics-loader>

        <ng-template #content>
          <div
            *ngIf="!(canEdit && isPendingForApproval())"
            class="container card rki-list-details-container"
          >
            <h3>{{ rkiDeviceData?.name }}</h3>

            <div class="row">
              <div class="col-md-8 col-lg-6 p-10px">
                <table
                  class="rki-table-detail table"
                  aria-describedby="Rki Requests Detail"
                >
                  <tr>
                    <th>Creator</th>
                    <td *ngIf="rkiDeviceData?.creator">
                      {{ rkiDeviceData?.creator?.fullName }}
                    </td>
                  </tr>
                  <tr>
                    <th>Date Requested</th>
                    <td>
                      {{
                        rkiDeviceData?.created
                          | date: "MMM d, yyyy 'at' h:mm a (UTCZZZZZ)"
                      }}
                    </td>
                  </tr>
                  <tr
                    *ngIf="
                      rkiDeviceData?.status === 0 || rkiDeviceData?.status === 8
                    "
                  >
                    <th>Expires</th>
                    <td>
                      {{
                        rkiDeviceData?.expires
                          | date: "MMM d, yyyy 'at' h:mm a (UTCZZZZZ)"
                      }}
                    </td>
                  </tr>
                  <tr>
                    <th>Status</th>
                    <td>{{ getStatus() }}</td>
                  </tr>

                  <tr *ngIf="isServiceCompany && rkiTenant">
                    <th>RKI Tenant</th>
                    <td>{{ rkiTenant.name }}</td>
                  </tr>

                  <tr>
                    <th>New Key Group</th>
                    <td>{{ rkiDeviceData?.keyGroupRef }}</td>
                  </tr>

                  <tr *ngIf="rkiDeviceData?.authorizer?.fullName">
                    <th>
                      <span *ngIf="rkiDeviceData?.secondAuthorizer?.fullName"
                        >First</span
                      >
                      Approver
                    </th>
                    <td>{{ rkiDeviceData?.authorizer?.fullName }}</td>
                  </tr>
                  <tr *ngIf="rkiDeviceData?.secondAuthorizer?.fullName">
                    <th>Second Approver</th>
                    <td>{{ rkiDeviceData?.secondAuthorizer?.fullName }}</td>
                  </tr>
                </table>
              </div>
            </div>

            <ul class="device-details-container">
              <li class="device-detail-item">
                <div class="row">
                  <div class="col-lg-3">Name and serial</div>
                  <div class="col-lg-3">Location</div>
                  <div class="col-lg-3">Current Key Group</div>
                  <div class="col-lg-3">Status</div>
                </div>
              </li>

              <li
                class="device-detail-item"
                *ngFor="let data of rkiDeviceData?.deviceRequests"
              >
                <div class="row">
                  <div
                    class="col-lg-3"
                    (click)="navigate(data.device.site.id, data.device.id)"
                  >
                    <div class="name-serial">
                      <span>{{ data.device.name }}</span>
                      <span
                        >{{ data.device.deviceType.name }} -
                        {{ data.device.serialNumber }}</span
                      >
                    </div>
                  </div>

                  <div
                    class="col-lg-3 location-container"
                    (click)="navigate(data.device.site.id)"
                  >
                    {{ data.device.site.name }}
                  </div>

                  <div class="col-lg-3 current-key-group-container">
                    <span
                      *ngIf="
                        data.originalKeyGroupRef === null;
                        else originalKeyGroupRef
                      "
                      >None</span
                    >
                    <ng-template #originalKeyGroupRef>
                      <span>{{ data.originalKeyGroupRef }}</span>
                    </ng-template>
                  </div>

                  <div class="col-lg-3 status-container">
                    <div
                      *ngIf="
                        data.status === null && data.message === null;
                        else status
                      "
                    >
                      <span> - </span>
                    </div>
                    <ng-template #status>
                      <span>{{ getStatusMessage(data.status) }}</span>
                      <span>{{ data.message }}</span>
                    </ng-template>
                  </div>
                </div>
              </li>
            </ul>

            <div class="rki-details-btn-container">
              <div class="accept-reject-btn-container">
                <div
                  *ngIf="isPendingForApproval() || isPendingForSecondApproval()"
                  class="approve-container"
                >
                  <button
                    class="btn-primary rki-approve-btn btn-wide"
                    (click)="handleApprove()"
                  >
                    Approve
                  </button>
                  <button
                    class="btn rki-btn-reject btn-wide"
                    (click)="handleReject()"
                  >
                    Reject
                  </button>
                </div>
              </div>

              <div class="rki-back-btn">
                <button class="btn back-btn" [routerLink]="['/rki']">
                  Back
                </button>
              </div>
            </div>
          </div>
        </ng-template>
      </div>
    </div>
  </div>
</div>

<div *ngIf="canEdit && isPendingForApproval()">
  <ics-create-update-rki-request
    [rkiType]="RKI_TYPE.UPDATE"
    [isServiceCompany]="isServiceCompany"
    [rkiRequest]="rkiDeviceData"
  ></ics-create-update-rki-request>
</div>
