import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewEncapsulation,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { Store } from '@ngrx/store';

import { loadRkiUsers } from '../../../store/actions/rki-users.actions';
import { loadRkiKeyGroups } from '../../../store/actions/key-groups-rki.actions';
import { Company } from 'src/app/models/common';
import { getConsumersData } from 'src/app/store/selectors/consumers.selector';
import { AuthService } from 'src/app/services/auth.service';
import { loadDeviceTypes } from 'src/app/store/actions/device-types.actions';

@Component({
  selector: 'ics-rki-tenant',
  templateUrl: './tenant.component.html',
  styleUrls: ['./tenant.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class TenantComponent implements OnInit, OnChanges, OnDestroy {
  @Input() fgTenant!: FormGroup;

  @Input() fgDevices!: FormGroup;

  @Input() fgApprovers!: FormGroup;

  @Input() isStepSelected!: boolean;

  @Input() isStepComplete!: boolean;

  @Output() tenantChanged = new EventEmitter<void>();

  @Output() validate = new EventEmitter<void>();

  arrRkiTenants: Company[] = [];

  showHelpText: boolean = false;

  private destroy$ = new Subject<void>();

  constructor(private store: Store) {
    // constructor code
  }

  ngOnInit(): void {
    this.store
      .select(getConsumersData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(consumers => {
        this.arrRkiTenants = [];
        if (consumers?.length) {
          const userCompany = AuthService.getCompany();
          if (userCompany?.id && userCompany?.name) {
            this.arrRkiTenants.push({
              id: userCompany?.id,
              name: userCompany.name,
            });
          }
          this.arrRkiTenants = this.arrRkiTenants.concat(consumers);
        }
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isStepSelected']?.currentValue) {
      this.showHelpText =
        this.fgDevices.get('ctrlDevices')?.value.length > 0 ||
        this.fgApprovers.get('ctrlApprovers')?.value.length > 0;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  handleSearch(term: string, item: Company): boolean {
    return item.name.toLocaleLowerCase().indexOf(term.toLocaleLowerCase()) > -1;
  }

  highlightSearchText(text: string, searchText: string): string {
    const regex = new RegExp(searchText, 'gi');
    return text.replace(regex, match => `<strong>${match}</strong>`);
  }

  onTenantChange({ id }: Company): void {
    if (id) {
      this.store.dispatch(loadRkiKeyGroups({ serviceRecipientId: id }));
      this.store.dispatch(loadRkiUsers({ companyId: id }));
      this.store.dispatch(loadDeviceTypes({ serviceRecipientId: id }));

      this.showHelpText = false;
      this.fgTenant.markAllAsTouched();
      this.tenantChanged.emit();
    }
  }

  getFormControlValue(): string {
    return this.fgTenant.get('ctrlTenant')?.value?.name || '';
  }

  onContinue(): void {
    this.validate.emit();
  }
}
