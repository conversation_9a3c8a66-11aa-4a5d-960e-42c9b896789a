import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';

import { Approver } from '../../../models/create-rki.model';
import { RkiUserResults } from '../../../models/users-rki.model';
import { getRkiUsersResults } from '../../../store/selectors/rki-users.selectors';
import { generateInitialsAndColor } from 'src/app/modules/module-settings/utils/transform-data';

@Component({
  selector: 'ics-rki-approvers',
  templateUrl: './approvers.component.html',
  styleUrls: ['./approvers.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class ApproversComponent implements OnInit {
  @Input() fgApprovers!: FormGroup;

  @Input() isStepSelected!: boolean;

  rkiUsers$!: Observable<RkiUserResults[]>;

  constructor(private store: Store) {
    // constructor code
  }

  ngOnInit(): void {
    this.rkiUsers$ = this.store.select(getRkiUsersResults);
  }

  get selectedApprovers(): Approver[] {
    return this.fgApprovers.get('ctrlApprovers')?.value || [];
  }

  handleRemovePerson({ id }: Approver): void {
    const approvers = this.selectedApprovers.filter(
      approver => approver.id !== id
    );
    this.fgApprovers.controls['ctrlApprovers'].setValue(approvers);
  }

  getAvatarImg({ fullName }: Approver): { initials: string; color: string } {
    return generateInitialsAndColor(fullName);
  }

  onGetData(approvers: Approver[]): void {
    const newApprovers = approvers.map(approver => {
      if (approver.isNew === undefined) {
        return { ...approver, isNew: true };
      }
      return approver;
    });
    this.fgApprovers.controls['ctrlApprovers'].setValue(newApprovers);
    this.fgApprovers.markAsTouched();
  }
}
