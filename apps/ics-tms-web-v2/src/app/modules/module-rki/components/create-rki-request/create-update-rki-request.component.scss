.rki-stepper-container {
  display: block;
  padding: 24px;
  margin-bottom: 20px;
  background-color: white;
  border-radius: 3px;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.2),
    0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 2px 1px -1px rgba(0, 0, 0, 0.12);

  .rki-stepper {
    .mat-step {
      cursor: default;

      .mat-vertical-content-container {
        margin-left: 16px;

        .mat-vertical-stepper-content {
          margin-bottom: 16px;
          padding-top: 12px;
          border-left: 1px solid var(--md-red-grey-600);

          .mat-vertical-content {
            padding-bottom: 10px;
          }
        }
      }
    }

    .mat-step-icon {
      margin-right: 0;

      .mat-step-icon-content {
        .check-circle {
          width: 30px !important;
          height: 30px !important;
          font-size: 30px !important;
          color: #4caf50;
        }
      }
    }

    .mat-step-icon-selected {
      font-weight: bold;
    }

    .mat-vertical-stepper-header {
      padding-top: 6px;
      padding-left: 4px;
      gap: 20px;

      .mat-step-text-label {
        line-height: 1;
        color: #212121;
      }
    }
  }
}

.button-container {
  display: flex;
  justify-content: space-between;
  padding: 0;
  margin: 20px 0 0 0;

  .font-weight-normal {
    font-weight: normal;
  }

  .font-weight-bold {
    font-weight: bold;
  }

  .btn-back {
    background-color: var(--btn-light-gray-border);

    &:hover {
      background-color: var(--btn-cancel-hover-color);
    }
  }

  .rki-btn-reject {
    color: var(--btn-color-red);
    background-color: var(--dropdown-border-color);
    padding: 0.6rem 1.8rem;
    font-size: 1.4rem;
    border-color: var(--dropdown-border-color);

    &:hover {
      background-color: var(--btn-color-red);
      color: var(--color-white);
      border-color: var(--btn-border-color-red);
    }
  }
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-not-allowed {
  cursor: not-allowed;
}
