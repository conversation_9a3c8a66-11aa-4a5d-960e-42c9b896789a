import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { Subject, takeUntil } from 'rxjs';

import { RkiDevice } from '../../../models/create-rki.model';
import { RkiDeviceInfoService } from '../../../services/rki-device-info.service';
import { DeviceType } from 'src/app/models/common';
import { selectDeviceTypesData } from 'src/app/store/selectors/device-types.selector';

@Component({
  selector: 'ics-rki-select-devices',
  templateUrl: './devices.component.html',
  styleUrls: ['./devices.component.scss'],
})
export class DevicesComponent implements OnInit, OnDestroy {
  @Input() tenantId!: string;

  @Input() fgKeyGroup!: FormGroup;

  @Input() fgDevices!: FormGroup;

  @Input() isStepSelected!: boolean;

  @Input() isStepComplete!: boolean;

  @Output() validate = new EventEmitter<void>();

  arrDeviceTypes: DeviceType[] = [];

  serialNumberForm: FormGroup;

  deviceSerialError = { hasError: false, message: '' };

  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private fb: FormBuilder,
    private store: Store,
    private rkiDeviceInfoService: RkiDeviceInfoService
  ) {
    this.serialNumberForm = this.fb.group({
      ctrlSerialNumber: this.fb.control('', Validators.required),
    });
  }

  ngOnInit(): void {
    this.store
      .select(selectDeviceTypesData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(deviceTypes => {
        this.arrDeviceTypes = deviceTypes || [];
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getCtrlDevicesValue(): RkiDevice[] {
    return this.fgDevices.get('ctrlDevices')?.value || [];
  }

  setCtrlDevicesValue(devices: RkiDevice[]): void {
    this.fgDevices.get('ctrlDevices')?.setValue(devices);
    this.fgDevices.markAsTouched();
  }

  get isDeviceSelected(): boolean {
    return this.getCtrlDevicesValue().length > 0;
  }

  deviceTypeHasFeatureFlag(id: string): boolean {
    return this.arrDeviceTypes.some(
      type => type.id === id && type.featureflags.indexOf('RKI') >= 0
    );
  }

  handleAddDevice(): void {
    this.deviceSerialError = { hasError: false, message: '' };
    const isDeviceAlreadyAdded = this.getCtrlDevicesValue()
      .map(data => data.serialNumber)
      .includes(this.getQuery().toUpperCase());

    if (isDeviceAlreadyAdded) {
      this.deviceSerialError = {
        hasError: true,
        message: 'This device is already added',
      };
    } else if (this.tenantId) {
      this.rkiDeviceInfoService
        .getRkiDeviceData(this.getQuery().toUpperCase(), this.tenantId)
        .subscribe({
          next: (device: RkiDevice) => {
            const keyGroup = this.fgKeyGroup.get('ctrlKeyGroup')?.value;
            if (device.presence === 'OUT_OF_INSTANCE') {
              this.deviceSerialError = {
                hasError: true,
                message: 'This device is not in this instance',
              };
            } else if (!this.deviceTypeHasFeatureFlag(device.deviceType.id)) {
              this.deviceSerialError = {
                hasError: true,
                message: 'Device type does not allow RKI',
              };
            } else if (
              !device.certificateIssuerCodes.includes(keyGroup?.issuerCode)
            ) {
              this.deviceSerialError = {
                hasError: true,
                message:
                  'This device is does not have certificate for this key group issuer code',
              };
            } else {
              const devices = [...this.getCtrlDevicesValue(), device];
              this.setCtrlDevicesValue(devices);
            }

            this.serialNumberForm.controls['ctrlSerialNumber'].setValue('');
          },
          error: () => {
            this.deviceSerialError = {
              hasError: true,
              message: 'This device does not exist',
            };
            this.serialNumberForm.controls['ctrlSerialNumber'].setValue('');
          },
        });
    }
  }

  handleRemoveDevice(device: RkiDevice): void {
    const devices = this.getCtrlDevicesValue().filter(
      item => item.id !== device.id
    );
    this.setCtrlDevicesValue(devices);
  }

  navigate(siteId: string, deviceId?: number): void {
    if (!this.isStepSelected) {
      if (siteId && deviceId) {
        const url = this.router
          .createUrlTree(['/sites', siteId, deviceId, 'overview'])
          .toString();
        window.open(url, '_blank');
      } else {
        const url = this.router.createUrlTree(['/sites', siteId]).toString();
        window.open(url, '_blank');
      }
    }
  }

  getQuery(): string {
    return this.serialNumberForm.get('ctrlSerialNumber')?.value || '';
  }

  onContinue(): void {
    this.validate.emit();
  }
}
