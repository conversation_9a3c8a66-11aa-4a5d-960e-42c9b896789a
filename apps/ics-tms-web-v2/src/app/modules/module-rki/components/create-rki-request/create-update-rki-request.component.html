<div class="row">
  <div class="col-lg-11">
    <div class="row">
      <div class="col-lg-10 col-md-offset-1">
        <div class="rki-stepper-container">
          <mat-stepper
            #stepper
            class="stepper rki-stepper"
            linear="true"
            orientation="vertical"
            [disableRipple]="true"
          >
            <ng-template matStepperIcon="edit">
              <mat-icon class="check-circle">check_circle</mat-icon>
            </ng-template>

            <!-- REQUEST NAME -->
            <mat-step
              [label]="steps.RKI_REQUEST_NAME.label"
              [completed]="steps.RKI_REQUEST_NAME.isComplete"
            >
              <div
                [ngClass]="setCursor(steps.RKI_REQUEST_NAME.label)"
                (click)="handleStepContentClick(steps.RKI_REQUEST_NAME.label)"
              >
                <ics-rki-request-name
                  [fgRequestName]="getFormGroup('fgRequestName')"
                  [isStepSelected]="
                    isStepSelected(steps.RKI_REQUEST_NAME.label)
                  "
                  [isStepComplete]="steps.RKI_REQUEST_NAME.isComplete"
                  (validate)="validateStep(true, stepper?.selected?.label)"
                ></ics-rki-request-name>
              </div>
            </mat-step>

            <!-- TENANT -->
            <mat-step
              *ngIf="isServiceCompany"
              [label]="steps.RKI_TENANT.label"
              [completed]="steps.RKI_TENANT.isComplete"
            >
              <div
                [ngClass]="setCursor(steps.RKI_TENANT.label)"
                (click)="handleStepContentClick(steps.RKI_TENANT.label)"
              >
                <ics-rki-tenant
                  [fgTenant]="getFormGroup('fgTenant')"
                  [fgDevices]="getFormGroup('fgDevices')"
                  [fgApprovers]="getFormGroup('fgApprovers')"
                  [isStepSelected]="isStepSelected(steps.RKI_TENANT.label)"
                  [isStepComplete]="steps.RKI_TENANT.isComplete"
                  (tenantChanged)="clearOnTenantChange()"
                  (validate)="validateStep(true, stepper?.selected?.label)"
                ></ics-rki-tenant>
              </div>
            </mat-step>

            <!-- KEY GROUP -->
            <mat-step
              [label]="steps.RKI_KEY_GROUP.label"
              [completed]="steps.RKI_KEY_GROUP.isComplete"
            >
              <div
                [ngClass]="setCursor(steps.RKI_KEY_GROUP.label)"
                (click)="handleStepContentClick(steps.RKI_KEY_GROUP.label)"
              >
                <ics-key-group
                  [fgKeyGroup]="getFormGroup('fgKeyGroup')"
                  [fgDevices]="getFormGroup('fgDevices')"
                  [isStepSelected]="isStepSelected(steps.RKI_KEY_GROUP.label)"
                  [isStepComplete]="steps.RKI_KEY_GROUP.isComplete"
                  (keyGroupChanged)="clearOnKeyGroupChange()"
                  (validate)="validateStep(true, stepper?.selected?.label)"
                ></ics-key-group>
              </div>
            </mat-step>

            <!-- DEVICES -->
            <mat-step
              [label]="steps.DEVICES.label"
              [completed]="steps.DEVICES.isComplete"
            >
              <div
                [ngClass]="setCursor(steps.DEVICES.label)"
                (click)="handleStepContentClick(steps.DEVICES.label)"
              >
                <ics-rki-select-devices
                  [tenantId]="getCtrlTenantValue()"
                  [fgKeyGroup]="getFormGroup('fgKeyGroup')"
                  [fgDevices]="getFormGroup('fgDevices')"
                  [isStepSelected]="isStepSelected(steps.DEVICES.label)"
                  [isStepComplete]="steps.DEVICES.isComplete"
                  (validate)="validateStep(true, stepper?.selected?.label)"
                ></ics-rki-select-devices>
              </div>
            </mat-step>

            <!-- APPROVERS  -->
            <mat-step
              [label]="steps.APPROVERS.label"
              [completed]="getFormGroup('fgApprovers')?.valid"
            >
              <div
                [ngClass]="setCursor(steps.APPROVERS.label)"
                (click)="handleStepContentClick(steps.APPROVERS.label)"
              >
                <ics-rki-approvers
                  [fgApprovers]="getFormGroup('fgApprovers')"
                  [isStepSelected]="isStepSelected(steps.APPROVERS.label)"
                ></ics-rki-approvers>
              </div>
            </mat-step>
          </mat-stepper>

          <div class="button-container">
            <button
              *ngIf="rkiType === RKI_TYPE.CREATE"
              class="btn btn-primary btn-box-shadow font-weight-bold"
              [disabled]="!rkiForm.valid || !rkiForm.touched"
              (click)="handleCreate()"
            >
              Create Request
            </button>

            <button
              *ngIf="rkiType === RKI_TYPE.UPDATE"
              class="btn btn-primary btn-box-shadow font-weight-bold"
              (click)="handleUpdate()"
              [disabled]="!rkiForm.valid || !rkiForm.touched"
            >
              Update Request
            </button>

            <div>
              <button
                *ngIf="rkiType === RKI_TYPE.UPDATE"
                class="btn rki-btn-reject font-weight-bold"
                (click)="handleDelete()"
              >
                Delete Request
              </button>

              <button
                class="btn-back btn font-weight-bold"
                (click)="navigateBack()"
              >
                Back
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
