import {
  AfterViewInit,
  Component,
  Input,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Subject, takeUntil } from 'rxjs';
import { MatStep, MatStepper } from '@angular/material/stepper';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';

import { RKI_TYPE } from '../../constants/appConstants';
import { RkiRequest } from '../../models/rki-detail.model';
import { RkiCreatePackagePayload } from '../../models/rki-create-package.model';
import { loadDeviceTypes } from '../../../../store/actions/device-types.actions';
import { loadRkiKeyGroups } from '../../store/actions/key-groups-rki.actions';
import { loadRkiUsers } from '../../store/actions/rki-users.actions';
import { getRkiKeyGroupsData } from '../../store/selectors/rki-key-groups.selectors';
import { CreateUpdateRKIRequestService } from '../../services/create-update-rki.service';
import { RkiFeeModalComponent } from './fee-modal/fee-modal.component';
import { RkiAuthModalComponent } from './auth-modal/auth-modal.component';
import { fetchCompanyByIdSuccess } from 'src/app/store/actions/company.actions';
import { loadConsumers } from 'src/app/store/actions/consumers.actions';
import { getConsumersData } from 'src/app/store/selectors/consumers.selector';
import { getCompanyById } from 'src/app/store/selectors/company.selector';
import { AuthService } from 'src/app/services/auth.service';
import { ToastService } from 'src/app/services/toast.service';

@Component({
  selector: 'ics-create-update-rki-request',
  templateUrl: './create-update-rki-request.component.html',
  styleUrls: ['./create-update-rki-request.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class CreateUpdateRkiRequestComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  @Input() rkiType: RKI_TYPE = RKI_TYPE.CREATE;

  @Input() isServiceCompany: boolean = false;

  @Input() rkiRequest: RkiRequest | null = null;

  @ViewChild('stepper', { static: false }) stepper!: MatStepper;

  // step related data
  steps = {
    RKI_REQUEST_NAME: {
      label: 'RKI Request Name',
      isComplete: false,
    },
    RKI_TENANT: {
      label: 'RKI Tenant',
      isComplete: false,
    },
    RKI_KEY_GROUP: {
      label: 'RKI Key Group',
      isComplete: false,
    },
    DEVICES: {
      label: 'Devices',
      isComplete: false,
    },
    APPROVERS: {
      label: 'Approvers',
      isComplete: false,
    },
  };

  rkiForm: FormGroup;

  isUpdate: boolean = false;

  RKI_TYPE = RKI_TYPE;

  private destroy$ = new Subject<void>();

  constructor(
    public route: Router,
    private store: Store,
    private fb: FormBuilder,
    private modalService: NgbModal,
    private toast: ToastService,
    private rkiService: CreateUpdateRKIRequestService
  ) {
    this.rkiForm = this.fb.group({
      fgRequestName: this.fb.group({
        ctrlRequestName: ['', Validators.required],
      }),
      fgTenant: this.fb.group({ ctrlTenant: [null, Validators.required] }),
      fgKeyGroup: this.fb.group({ ctrlKeyGroup: [null, Validators.required] }),
      fgDevices: this.fb.group({ ctrlDevices: [[], Validators.required] }),
      fgApprovers: this.fb.group({ ctrlApprovers: [[], Validators.required] }),
    });
  }

  ngOnInit(): void {
    this.isUpdate = this.rkiType === RKI_TYPE.UPDATE;
    this.prefillAndValidateRkiForm();

    // get device types
    this.store.dispatch(loadDeviceTypes({}));
    // get service company tenants
    this.store.dispatch(loadConsumers());

    this.store
      .select(getConsumersData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(consumers => {
        if (consumers?.length) {
          this.isServiceCompany = true;
          this.modifyStepper();
        } else {
          this.isServiceCompany = false;
          const company = AuthService.getCompany();
          if (company?.id) {
            this.store.dispatch(
              loadRkiKeyGroups({ serviceRecipientId: company.id })
            );
            this.store.dispatch(loadRkiUsers({ companyId: company.id }));

            if (!this.isUpdate) {
              this.getFormGroup('fgTenant')
                .get('ctrlTenant')
                ?.setValue(company);
            }
          }
        }
      });
  }

  ngAfterViewInit(): void {
    this.modifyStepper();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    // clean-up state
    this.store.dispatch(fetchCompanyByIdSuccess({ company: null }));
  }

  prefillAndValidateRkiForm(): void {
    if (this.isUpdate && this.rkiRequest) {
      const devices =
        this.rkiRequest.deviceRequests?.map(
          deviceRequest => deviceRequest.device
        ) || [];
      const approvers =
        this.rkiRequest.approvers?.map(member => ({
          ...member,
          isNew: false,
        })) || [];
      this.getFormGroup('fgRequestName')
        .get('ctrlRequestName')
        ?.setValue(this.rkiRequest.name);
      this.getFormGroup('fgDevices').get('ctrlDevices')?.setValue(devices);
      this.getFormGroup('fgApprovers')
        .get('ctrlApprovers')
        ?.setValue(approvers);

      this.validateStep(false, this.steps.RKI_REQUEST_NAME.label);
      this.validateStep(false, this.steps.DEVICES.label);
      this.validateStep(false, this.steps.APPROVERS.label);

      this.store
        .select(getCompanyById)
        .pipe(takeUntil(this.destroy$))
        .subscribe(company => {
          if (company) {
            this.getFormGroup('fgTenant').get('ctrlTenant')?.setValue(company);
            this.validateStep(false, this.steps.RKI_TENANT.label);
            this.store.dispatch(
              loadRkiKeyGroups({ serviceRecipientId: company.id })
            );
          }
        });

      this.store
        .select(getRkiKeyGroupsData)
        .pipe(takeUntil(this.destroy$))
        .subscribe(keygroups => {
          const selectedKeyGroup = keygroups.find(
            kg =>
              kg.ref === this.rkiRequest?.keyGroupRef &&
              kg.owner.id === this.rkiRequest?.companyId
          );
          if (selectedKeyGroup) {
            this.getFormGroup('fgKeyGroup')
              .get('ctrlKeyGroup')
              ?.setValue(selectedKeyGroup);
            this.validateStep(false, this.steps.RKI_KEY_GROUP.label);
          }
        });
    }
  }

  modifyStepper(): void {
    setTimeout(() => {
      if (this.isUpdate) {
        this.stepper['_updateSelectedItemIndex'](-1);

        // if it's an update, disable the step(s) itself and modify the stepper's next function to skip,
        // tenant and key group steps if at least one service company is present
        // otherwise, onky key group step
        const stepsToSkip = [
          this.steps.RKI_TENANT.label,
          this.steps.RKI_KEY_GROUP.label,
        ];
        this.stepper.next = () => {
          if (this.isUpdate && this.stepper.selected?.label) {
            let nextStepIndex = this.stepper.selectedIndex + 1;

            while (
              nextStepIndex < this.stepper.steps.length &&
              stepsToSkip.includes(
                // eslint-disable-next-line no-underscore-dangle
                this.stepper._steps.toArray()[nextStepIndex].label
              )
            ) {
              nextStepIndex += 1;
            }

            this.stepper.selectedIndex = nextStepIndex;
          }
        };

        // eslint-disable-next-line no-underscore-dangle
        this.stepper._steps.forEach(step => {
          if (stepsToSkip.includes(step.label)) {
            // eslint-disable-next-line no-param-reassign
            step.select = () => {};
          }
        });
        // eslint-disable-next-line no-underscore-dangle
        this.stepper._stepIsNavigable = (
          index: number,
          step: MatStep
        ): boolean => {
          if (stepsToSkip.includes(step.label)) {
            return false;
          }
          return step.completed || this.stepper.selectedIndex === index || true;
        };
      }
    });
  }

  getFormGroup(formGroupName: string): FormGroup {
    return this.rkiForm.get(formGroupName) as FormGroup;
  }

  isStepSelected(stepLabel: string): boolean {
    if (this.stepper) {
      return this.stepper.selected?.label === stepLabel;
    }
    return false;
  }

  validateStep(moveNext: boolean, label: string = ''): void {
    switch (label) {
      case this.steps.RKI_REQUEST_NAME.label:
        if (this.getFormGroup('fgRequestName').valid) {
          this.steps.RKI_REQUEST_NAME.isComplete = true;
          if (moveNext) {
            this.nextStep();
          }
        }
        break;
      case this.steps.RKI_TENANT.label:
        if (this.getFormGroup('fgTenant').valid) {
          this.steps.RKI_TENANT.isComplete = true;
          if (moveNext) {
            this.nextStep();
          }
        }
        break;
      case this.steps.RKI_KEY_GROUP.label:
        if (this.getFormGroup('fgKeyGroup').valid) {
          this.steps.RKI_KEY_GROUP.isComplete = true;
          if (moveNext) {
            this.nextStep();
          }
        }
        break;
      case this.steps.DEVICES.label:
        if (this.getFormGroup('fgDevices').valid) {
          this.steps.DEVICES.isComplete = true;
          if (moveNext) {
            this.nextStep();
          }
        }
        break;
      case this.steps.APPROVERS.label:
        if (this.getFormGroup('fgApprovers').valid) {
          this.steps.APPROVERS.isComplete = true;
          if (moveNext) {
            this.nextStep();
          }
        }
        break;
      default:
        console.error(`The step ${label} is not defined.`);
        break;
    }
  }

  nextStep(): void {
    setTimeout(() => {
      this.stepper.next();
    }, 0);
  }

  setCursor(stepLabel: string): string {
    if (!this.isStepSelected(stepLabel)) {
      let className = 'cursor-pointer';
      const stepsToSkip = [
        this.steps.RKI_TENANT.label,
        this.steps.RKI_KEY_GROUP.label,
      ];
      if (this.isUpdate && stepsToSkip.includes(stepLabel)) {
        className = 'cursor-not-allowed';
      }
      return className;
    }
    return '';
  }

  handleStepContentClick(stepLabel: string): void {
    if (!this.isStepSelected(stepLabel)) {
      this.stepper.steps.forEach((step, index) => {
        if (step.label === stepLabel) {
          const stepsToSkip = [
            this.steps.RKI_TENANT.label,
            this.steps.RKI_KEY_GROUP.label,
          ];
          if (!this.isUpdate) {
            this.stepper.selectedIndex = index;
          } else if (this.isUpdate && !stepsToSkip.includes(stepLabel)) {
            this.stepper.selectedIndex = index;
          }
        }
      });
    }
  }

  navigateBack(): void {
    this.route.navigate(['rki']);
  }

  openRkiFeeModal(): NgbModalRef {
    return this.modalService.open(RkiFeeModalComponent, {
      container: '#ng-modal-container',
      windowClass: 'modal-primary-fee in',
      size: 'sm',
    });
  }

  openRkiAuthModal(modalType: RKI_TYPE): NgbModalRef {
    const refRkiAuthModal = this.modalService.open(RkiAuthModalComponent, {
      container: '#ng-modal-container',
      windowClass: 'modal-primary in',
      size: 'sm',
    });
    refRkiAuthModal.componentInstance.rkiType = modalType;

    return refRkiAuthModal;
  }

  getRkiRequestPayload(mfaCode: string): RkiCreatePackagePayload {
    const rkiRequestPayload: RkiCreatePackagePayload = {
      name: this.getFormGroup('fgRequestName').get('ctrlRequestName')?.value,
      companyId: this.getFormGroup('fgTenant').get('ctrlTenant')?.value.id,
      keyGroupRef:
        this.getFormGroup('fgKeyGroup').get('ctrlKeyGroup')?.value.ref,
      deviceRequests: this.getFormGroup('fgDevices')
        .get('ctrlDevices')
        ?.value.map((device: { id: string }) => ({
          device: { id: device.id },
        })),
      approvers: this.getFormGroup('fgApprovers')
        .get('ctrlApprovers')
        ?.value.map((approver: { id: string }) => ({ id: approver.id })),
      mfaCode,
    };

    return rkiRequestPayload;
  }

  handleCreate(): void {
    this.openRkiFeeModal().result.then((proceed: boolean) => {
      if (proceed) {
        this.openRkiAuthModal(RKI_TYPE.CREATE).result.then(
          ({ confirmed, mfaCode }: { confirmed: boolean; mfaCode: string }) => {
            if (confirmed) {
              this.rkiService
                .createRkiRequest(this.getRkiRequestPayload(mfaCode))
                .subscribe({
                  next: () => {
                    this.toast.show({
                      message: 'RKI request has been created',
                    });
                    this.route.navigate(['/rki']);
                  },
                  error: error => {
                    if (error?.status === 406) {
                      this.toast.show({ message: error?.error?.message });
                    } else {
                      console.error(
                        'Error while creating RKI request: ',
                        error
                      );
                    }
                  },
                });
            }
          }
        );
      }
    });
  }

  handleUpdate(): void {
    this.openRkiAuthModal(RKI_TYPE.UPDATE).result.then(
      ({ confirmed, mfaCode }: { confirmed: boolean; mfaCode: string }) => {
        if (confirmed) {
          if (this.rkiRequest?.id) {
            const payload = this.getRkiRequestPayload(mfaCode);
            this.rkiService
              .editRkiRequest(this.rkiRequest.id, payload)
              .subscribe({
                next: () => {
                  this.toast.show({ message: 'RKI request has been updated' });
                  this.route.navigate(['/rki']);
                },
                error: error => {
                  if (error?.status === 406) {
                    this.toast.show({ message: error?.error?.message });
                  } else {
                    console.error('Error while approving RKI request: ', error);
                  }
                },
              });
          }
        }
      }
    );
  }

  handleDelete(): void {
    this.openRkiAuthModal(RKI_TYPE.DELETE).result.then(
      ({ confirmed, mfaCode }: { confirmed: boolean; mfaCode: string }) => {
        if (confirmed) {
          if (this.rkiRequest?.id) {
            this.rkiService
              .deleteRkiRequest(this.rkiRequest.id, mfaCode)
              .subscribe({
                next: () => {
                  this.toast.show({ message: 'RKI request has been deleted' });
                  this.route.navigate(['/rki']);
                },
                error: error => {
                  if (error?.status === 406) {
                    this.toast.show({ message: error?.error?.message });
                  } else {
                    console.error('Error while deleting RKI request: ', error);
                  }
                },
              });
          }
        }
      }
    );
  }

  clearOnTenantChange(): void {
    this.getFormGroup('fgKeyGroup').get('ctrlKeyGroup')?.setValue(null);
    this.steps.RKI_KEY_GROUP.isComplete = false;

    this.getFormGroup('fgDevices').get('ctrlDevices')?.setValue([]);
    this.steps.DEVICES.isComplete = false;

    this.getFormGroup('fgApprovers').get('ctrlApprovers')?.setValue([]);
    this.steps.APPROVERS.isComplete = false;
  }

  clearOnKeyGroupChange(): void {
    this.getFormGroup('fgDevices').get('ctrlDevices')?.setValue([]);
    this.steps.DEVICES.isComplete = false;
  }

  getCtrlTenantValue(): string {
    return this.getFormGroup('fgTenant').get('ctrlTenant')?.value?.id || '';
  }
}
