.rki-devices-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 0;

  .rki-devices-form-container {
    margin-bottom: 25px;
    border-radius: 3px;
    box-shadow:
      0 1px 3px 0 rgba(0, 0, 0, 0.2),
      0 1px 1px 0 rgba(0, 0, 0, 0.14),
      0 2px 1px -1px rgba(0, 0, 0, 0.12);
  }
}

.rki-devices-form {
  .custom-padding {
    padding: 16px 16px 0;
  }

  .serial-input-container {
    .serial-input {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .serial-error-container {
        display: flex;
        flex-direction: column;

        .serial-error-message {
          margin-bottom: 5px;
          font-size: 13px;
          line-height: 16px;
          color: #dd2c00;
        }

        .no-device-selected {
          font-size: 13px;
          line-height: 16px;
          color: #616161;
        }
      }
    }
  }

  .add-btn {
    transition: all ease-in 0.1s;
    font-size: 1.4rem;
    border-radius: 0.3rem;
    padding: 0.6rem 1.2rem;
    text-align: center;
    border: 0.1rem solid transparent;
    outline: none;
  }

  .disabled-add-btn {
    cursor: not-allowed;
    opacity: 0.65;
  }
}

.added-devices-container {
  .row {
    --bs-gutter-x: 0 !important;
  }

  margin-bottom: 0;
  max-height: 350px;
  overflow-x: hidden;
  overflow-y: auto;
  list-style: none;
  box-sizing: border-box;
  padding: 0;

  .rki-device-list-header {
    padding: 10px 15px;
    color: #757575;
    font-size: 12px;
    font-weight: bold;
  }

  .rki-device-list-body {
    border-top: 1px solid var(--md-red-grey-600);
    padding: 10px 15px;
    font-size: 14px;

    .name-serial {
      p {
        margin: 0;

        &:first-child {
          font-size: 14px;
          font-weight: bold;
        }

        &:last-child {
          font-size: 13px;
        }
      }
    }

    .px-10 {
      padding-left: 10px;
      padding-right: 10px;
    }

    .remove-btn {
      background-color: var(--btn-cancel-color);
      font-weight: 500;
      font-size: 1.4rem;

      &:hover {
        background-color: var(--btn-cancel-hover-color);
      }
    }
  }
}
