.btn-add-new-rki {
  display: flex;
  align-items: center;
  gap: 3px;

  span {
    font-size: 1.2rem;
    font-weight: 500;
  }
}

.list-group-header {
  border-top: none;
  font-size: 12px;
  font-weight: bold;
  color: #757575 !important;
}

.list-group-header-item {
  position: relative;
  min-height: 1px;
  padding: 0px 7.5px;
}

@media (min-width: 992px) {
  .list-group-header-item {
    width: 20% !important;
    text-align: center;
    padding: 0px 5px;
    border-left: 1px solid var(--md-red-grey-600);
  }

  .list-group-header-item:last-child {
    border-right: 1px solid var(--md-red-grey-600);
  }
}

.list-group-item {
  padding: 10px 15px !important;
  border-width: 0px 0px 2px 0px !important;
  border: 2px solid var(--md-red-grey-600);

  .pr-20px {
    padding-right: 20px !important;
  }

  &:hover {
    background-color: #f6f7f9;
    cursor: pointer;
  }
}
