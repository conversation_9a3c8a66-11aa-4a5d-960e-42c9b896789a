.reboot-container {
  box-shadow: 0 0.8rem 2.4rem rgba(0, 0, 0, 0.5);
  width: fit-content;
  height: fit-content;
  background-color: var(--color-white);
  border-radius: 10px;
  width: 600px;
  padding: 3px;

  .header {
    border-bottom: 1px solid var(--md-red-grey-600);
    padding: 10px 15px;

    h1 {
      margin: 0;
      font-size: 1.8rem;
      font-weight: 500;
      line-height: 1.43;
    }
  }

  .error-message {
    padding: 1rem 1.5rem;
  }

  .reboot-time-component {
    display: flex;
    align-items: center;
    gap: 16px;
    border-top: 1px solid var(--md-red-grey-600);
    border-bottom: 1px solid var(--md-red-grey-600);
    padding: 10px 15px;

    .reboot-container-radio {
      display: flex;
      align-items: center;
      gap: 16px;

      .reboot-radio {
        width: 18px;
        height: 18px;
        cursor: pointer;
        accent-color: #3f51b5;
      }

      .reboot-label {
        margin-bottom: 0;
        font-size: 1.4rem;
        cursor: pointer;
        font-weight: bold;
      }
    }

    .cursor-not-allowed {
      cursor: not-allowed;
      opacity: 0.6;
    }

    .pointer-events-none {
      pointer-events: none;
    }

    .dp-container {
      height: 34px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .d-inline-block {
        margin-right: 1.6rem;

        .datepicker-btn {
          background-color: var(--dropdown-by-default);
          border: 0.1rem solid var(--dropdown-border-hover);
          font-weight: 500;

          &:hover {
            background-color: var(--dropdown-hover);
          }
        }

        .datepicker-container {
          padding: 0;
          border: none;
        }
      }

      .time-input {
        width: 3.125rem;
        height: inherit;
        padding-left: 0.938rem;
      }

      .ngb-tp-meridian {
        button {
          color: var(--color-black);
          outline: none;
          border: 0.1rem solid var(--dropdown-border-hover);

          &:hover {
            background-color: var(--dropdown-hover);
          }
        }
      }

      .ngb-tp {
        gap: 0.5rem;
      }

      .form-control:focus {
        border: 0.2rem solid var(--color-outline) !important;
        box-shadow: none;
      }

      .ngb-tp-spacer {
        font-size: 1.4rem;
      }

      .ngb-tp-input {
        width: 5rem;
      }

      .ngb-tp-chevron:before {
        display: none;
      }
    }
  }

  .reboot-footer {
    padding: 1rem 1.5rem;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    gap: 0.6rem;
    font-size: 1.4rem;

    .device-count {
      margin-right: auto;
      background-color: var(--btn-cancel-color);
      color: var(--md-grey-1000);
      font-size: 1.2rem;
      padding: 0.2rem 0.6rem 0.3rem;
      border-radius: 0.4rem;
      font-weight: bold;
    }

    #cancel-button {
      border: none;
      background-color: var(--color-white);
      font-weight: 500;
      color: var(--md-indigo-600);
      padding: 0.6rem 1.2rem;

      &:hover {
        color: var(--color-blue);
      }
    }

    #reboot-btn {
      padding: 0.6rem 1.2rem;
      border: none;
      color: var(--color-white);
      border-radius: 0.3rem;
      background-color: var(--md-indigo-600);
      box-shadow:
        0 0.1rem 0.2rem rgba(0, 0, 0, 0.3),
        0 -0.1rem 0.3rem -0.2rem var(--md-black-20);

      &:disabled {
        background-color: var(--btn-reboot-disabled);
        cursor: not-allowed;
      }

      &:not([disabled]):hover {
        background-color: var(--md-dark-blue-100);
      }
    }
  }
}
