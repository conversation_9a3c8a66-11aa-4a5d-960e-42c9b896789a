.list-group-header {
  border-top: none;
  font-size: 12px;
  font-weight: bold;
  color: #757575 !important;
}

.list-group-header-item {
  position: relative;
  min-height: 1px;
  padding: 0px 7.5px;
}

@media (min-width: 992px) {
  .list-group-header-item {
    width: 20% !important;
    text-align: center;
    padding: 0px 5px;
    border-left: 1px solid var(--md-red-grey-600);
  }

  .list-group-header-item:last-child {
    border-right: 1px solid var(--md-red-grey-600);
  }
}

.list-group-item {
  padding: 10px 15px !important;
  border-width: 0px 0px 2px 0px !important;
  border: 2px solid var(--md-red-grey-600);

  .pr-20px {
    padding-right: 20px !important;
  }
}

.custom-dropdown {
  margin-right: 3px;

  .tooltip {
    .tooltip-arrow {
      display: none;
    }

    .tooltip-inner {
      font-size: 1.1rem;
      width: max-content;
      border-radius: 0;
      border: 0.1rem solid var(--md-red-grey-600);
    }
  }

  &:focus-within {
    .tooltip {
      opacity: 0;
    }
  }

  .dropdown-toggle::after {
    display: none;
  }

  .menu-btn {
    background-color: transparent;
    width: 2.5rem;
    border-radius: 50%;
    border: none;
    color: var(--md-grey-700);
    position: static;
    font-size: 1.4rem;
  }

  .menu-btn:hover {
    color: var(--md-grey-550);
  }

  .menu-btn:focus {
    background-color: var(--md-grey-250);
    color: var(--md-grey-550);
  }

  .dropdown-content {
    box-shadow:
      0 0.1rem 0.3rem 0 var(--md-black-20),
      0 0.1rem 0.8rem 0 var(--md-black-14),
      0 0.2rem 0.1rem -0.1rem var(--md-black-12);
    border: 0.1rem solid transparent;
    min-width: 16rem;
    border-radius: 0.3rem;
    margin: 0.2rem 0 0;
    padding: 0.8rem 0;
    font-size: 1.4rem;
    background-color: var(--color-white);
    z-index: 1;
  }

  .dropdown-right {
    right: 0 !important;
    left: auto !important;
  }

  button {
    color: var(--md-grey-1000);
  }

  hr {
    margin: 0.8rem 0rem;
  }

  .dropdown-item {
    padding: 0.3rem 2rem;

    &:hover {
      background-color: var(--color-bg-default);
      color: var(--color-black);
    }
  }
}

.d-inline-block {
  > button {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    line-height: 1.5;
    padding: 0.5rem 1rem;
    border-radius: 0.3rem;
  }

  .custom-dropdown {
    box-shadow:
      0 0.1rem 0.3rem 0 var(--md-black-20),
      0 0.1rem 0.8rem 0 var(--md-black-14),
      0 0.2rem 0.1rem -0.1rem var(--md-black-12);
    min-width: 16rem;

    .dw-bulk-btn {
      margin-top: 0;
      font-size: 1.4rem;
      padding: 0.3rem 2rem;

      &:hover {
        background-color: var(--color-bg-default);
        color: var(--c olor-black);
      }
    }
  }
}

.dropdown-toggle::after {
  margin-left: 0 !important;
  vertical-align: 1px !important;
  border-top: 4px solid !important;
  border-right: 4px solid transparent !important;
  border-left: 4px solid transparent !important;
}
