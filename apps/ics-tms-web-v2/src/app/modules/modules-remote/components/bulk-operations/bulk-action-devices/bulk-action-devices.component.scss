@mixin df-below {
  display: flex;
  flex-direction: column;
}

@mixin df-side {
  display: flex;
  flex-direction: row;
}

.ng-select .ng-placeholder {
  font-size: 14px;
  color: var(--md-red-grey-600);
  opacity: 0.6;
}

.avatar {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  margin-right: 0.313rem;
}

.bulk-container {
  background-color: var(--color-white);

  .component-selector-button-area {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 10px 15px;
    border-bottom: 1px solid var(--md-red-grey-600);

    b {
      font-size: 1.4rem;
    }

    .selector-btn-group {
      width: fit-content;
      @include df-side;

      &:hover {
        cursor: pointer;
      }

      .selector-btn {
        padding: 0.6rem 1.2rem;
        text-align: center;
        font-weight: 500;
        font-size: 1.4rem;
        width: 7.5rem;
      }

      .selector-btn-1 {
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
        border-top: 1px solid var(--md-grey-300);
        border-bottom: 1px solid var(--md-grey-300);
        border-left: 1px solid var(--md-grey-300);
      }

      .selector-btn-2 {
        border-top-right-radius: 3px;
        border-bottom-right-radius: 3px;
        border: 1px solid var(--md-grey-300);
      }

      .btnActive-bg {
        background-color: #c7c7c7;
        color: #000000;
        border-color: #c7c7c7;

        &:hover {
          background-color: #bcbcbc;
          border-color: #c7c7c7;
        }
      }
    }
  }

  .devices-component-selected-container-2 {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px 15px 0 15px;

    .anchor-tags-bar {
      @include df-side;
      align-items: center;
      gap: 10px;
      font-size: 14px;

      .bulk-action-anchor-tag {
        color: #616161;
        text-decoration: none;
        cursor: pointer;

        &:hover {
          text-decoration: underline !important;
        }

        p {
          margin: 0;
          color: var(--text-color-gray);
          font-weight: 400;
        }
      }

      .bulk-action-anchor-tag.fw-bold {
        color: #000000;
        font-weight: bold;
      }
    }

    .separator {
      font-weight: bold;
    }

    .device-select-area {
      .ng-select {
        height: 34px;

        .ng-dropdown-panel .ng-dropdown-panel-items {
          margin: 2px 0 0;
          padding: 5px 0;
          font-size: 1.4rem;

          .ng-option {
            padding: 3px 20px;
            border-bottom: 0.1rem solid var(--md-red-grey-600);

            p {
              margin: 0;
              font-size: 12px;
            }

            &.ng-option-marked {
              color: white;
              background: #3f51b5;
            }
          }
        }

        .ng-select-container {
          height: 34px;
          min-height: 34px;
          border-radius: 3px;
          cursor: text;

          input {
            cursor: text !important;
          }

          .ng-value-container {
            padding: 6px 12px;

            .ng-placeholder {
              font-size: 14px;
              color: #757575;
            }
          }
        }
      }

      .ng-clear-wrapper,
      .ng-arrow-wrapper {
        display: none;
      }

      .ng-select.ng-select-focused:not(.ng-select-opened)
        > .ng-select-container {
        border-color: rgba(68, 138, 255, 0.75);
        box-shadow: inset 0 0 0 1px rgba(68, 138, 255, 0.75);
      }

      input,
      select {
        height: 1.8rem;
      }

      .selected-container {
        margin-top: 10px;
        margin-bottom: 10px;
        border: 0.1rem solid var(--md-red-grey-600);
        border-radius: 0.3rem;

        .selected-container-heading {
          padding: 1rem 1.5rem;
          @include df-side;
          border-bottom: 0.1rem solid var(--md-red-grey-600);

          .pr-0 {
            padding-right: 0;
          }

          .pr-10px {
            padding-right: 10px;
          }

          .pl-0 {
            padding-left: 0;
          }

          .pl-10px {
            padding-left: 10px;
          }

          span {
            font-size: 12px;
            font-weight: bold;
            color: #757575;
          }
        }

        .selected-container-elements {
          max-height: 350px;
          font-size: 1.4rem;
          overflow-y: auto;

          p {
            color: var(--md-grey-700);
            margin: 0;
            font-size: 1.15rem;
            padding: 1rem 1.5rem;
          }

          .selected-cards {
            @include df-side;
            padding: 10px 15px;

            &:not(:last-child) {
              border-bottom: 0.1rem solid var(--md-red-grey-600);
            }

            .pr-0 {
              padding-right: 0;
            }

            .pr-10px {
              padding-right: 10px;
            }

            .pl-0 {
              padding-left: 0;
            }

            .pl-10px {
              padding-left: 10px;
            }

            .card-left {
              display: flex;
              flex-direction: column;
              text-decoration: none;

              &:hover {
                text-decoration: underline;
                color: var(--color-black);
              }

              a {
                font-size: 13px;
                line-height: 1.4;
                text-decoration: none;
                color: var(--color-black);

                &:hover {
                  text-decoration: underline;
                }
              }

              a:first-child {
                font-size: 14px;
                line-height: 1.4;
                font-weight: bold;
              }
            }

            .card-middle {
              a {
                text-decoration: none;
                color: var(--color-black);

                &:hover {
                  text-decoration: underline;
                }
              }
            }

            .my-card-btn {
              background-color: var(--md-grey-300);
              border: 1px solid var(--md-grey-300);
              border-radius: 3px;
              padding: 6px 12px;

              &:hover {
                background-color: #c1c1c1;
                border: 1px solid #c1c1c1;
              }

              span {
                font-weight: 500;
              }
            }
          }
        }
      }
    }

    .list-selected-area {
      padding: 0;
      padding-bottom: 16px;

      p {
        margin-bottom: 0.8rem;
        font-size: 1.3rem;
        color: var(--color-icon-default);
        font-weight: 700;
      }

      input {
        height: 3.313rem;
        width: 100%;
      }

      textarea {
        resize: none;
        overflow-y: hidden;
      }
    }
  }

  .sites-component-selected-container-2 {
    padding: 1rem 1.5rem;
    font-size: 1.4rem;

    // tags input
    .tag-field-container,
    .site-groups {
      display: flex;
      align-items: center;
      gap: 0.7rem;
      justify-content: space-between;
      margin-bottom: 1rem;

      .ng-select.ng-select-multiple
        .ng-select-container
        .ng-value-container
        .ng-value
        .ng-value-label {
        border-radius: 0.3rem !important;
        border-color: var(--color-tags-blue) !important;
        background-color: var(--color-tags-blue) !important;
        color: var(--color-white);
        font-size: 1.2rem;
        padding: 0.2rem 0.8rem;
        font-weight: 700;
      }

      .site-groups {
        width: 100%;

        .ng-select.ng-select-multiple
          .ng-select-container
          .ng-value-container
          .ng-value
          .ng-value-label {
          border-radius: 0.3rem !important;
          border-color: var(--color-tags-orange) !important;
          background-color: var(--color-tags-orange) !important;
          color: var(--color-white);
          font-size: 1.2rem;
          padding: 0.2rem 0.8rem;
          font-weight: 700;
        }
      }

      .ng-dropdown-panel .ng-option:last-child {
        color: var(--color-black);
      }

      .fc-powered {
        display: flex;
        flex-direction: column;
        gap: 0.8rem;
      }

      .ng-dropdown-panel {
        box-shadow:
          0 1px 3px 0 var(--md-black-20),
          0 1px 8px 0 var(--md-black-14),
          0 2px 1px -1px var(--md-black-12);
        overflow-x: hidden;
        margin-top: 0;
        font-size: 14px;
        padding: 8px 0;
        border: 1px solid transparent;
      }

      .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
        padding: 4px 7px;
        font-size: 1.4rem;

        &.ng-option-marked {
          background: #3f51b5;
        }
      }

      .ng-select.ng-select-multiple
        .ng-select-container
        .ng-value-container
        .ng-input {
        font-size: 1.4rem;

        input {
          height: fit-content;
          cursor: text;
        }
      }

      .ng-select.ng-select-multiple
        .ng-select-container
        .ng-value-container
        .ng-placeholder {
        top: 7px;
        padding: 0 10px;
      }

      .ng-select.ng-select-multiple
        .ng-select-container
        .ng-value-container
        .ng-value
        .ng-value-icon.left {
        border: none;
        display: none;
      }

      .ng-select.ng-select-multiple
        .ng-select-container
        .ng-value-container
        .ng-value {
        background-color: var(--badge-bg-ng-select);
        border-radius: 5rem;
      }

      .ng-option-marked {
        span {
          display: flex;
          flex-direction: row-reverse;
          justify-content: flex-end;
        }
      }

      .ng-option span {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;

        .ng-tag-label {
          font-size: 1.4rem;
          margin-left: 0.313rem;
        }
      }

      .ng-value {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
      }

      .ng-select .ng-arrow-wrapper {
        display: none;
      }

      .ng-dropdown-panel {
        margin-top: 0.25rem;
        border-radius: 0.3rem;
      }

      .ng-dropdown-panel-items > div {
        display: flex;
        flex-direction: column;
        transform: scaleY(-1);
      }

      .ng-dropdown-panel-items > div > * {
        transform: scaleY(-1);
      }
    }

    .upper-card {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      p {
        font-weight: 500;
      }

      .checkbox-controllers {
        display: flex;
        align-items: center;
        gap: 0.2rem;

        span {
          font-size: 1.4rem;
        }

        .all-select-btn,
        .none-select-btn {
          border: none;
          outline: none;
          background: transparent;
          color: var(--md-indigo-600);
          font-weight: 500;

          &:hover {
            color: var(--color-blue);
          }

          &:disabled {
            cursor: not-allowed;
            opacity: 0.65;
          }
        }
      }
    }

    input[type='text']::placeholder {
      color: var(--input-placeholder-color);
    }

    > input {
      margin-bottom: 1rem;
    }

    .tree-items-list {
      border-radius: 0.3rem;

      .inner-tree-container {
        max-height: 36rem;
        overflow-x: hidden;
        overflow-y: auto;
        word-break: break-word;
        box-shadow:
          0 0.1rem 0.3rem 0 var(--md-black-20),
          0 0.1rem 0.1rem 0 var(--md-black-14),
          0 0.2rem 0.1rem -0.1rem var(--md-black-12);

        .sites-accordion {
          .accordion-item {
            border: 0;
            border-bottom: 1px solid #dee2e6;
            border-radius: 0;

            input {
              cursor: pointer;
            }

            .checkbox-container {
              width: 20px;
              height: 20px;
              position: relative;

              .checkbox-icon-helper {
                i {
                  position: absolute;
                  top: 1px;
                  left: 2px;
                  color: var(--color-white);
                  font-size: 0.9rem;
                }

                &::after {
                  width: 12px;
                  height: 12px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-weight: normal;
                  border-radius: 1.5px;
                  border: 1px solid var(--label-unknown);
                  margin-right: 5px;
                  cursor: pointer;
                }
              }
            }

            .accordion-header {
              width: 100%;
              height: 40px;

              .site-item {
                height: 40px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0;

                .device-name-info {
                  height: 40px;
                  display: flex;
                  align-items: center;
                  gap: 3px;
                  flex-grow: 1;

                  .expand-collapse-btn-container {
                    width: 30px;
                    height: 30px;
                    margin: 5px;
                    border-radius: 50%;
                    outline: none;
                    border: none;
                    background: transparent;

                    &:hover {
                      cursor: pointer;
                      background-color: var(--expand-btn-bg-color);
                    }

                    > i {
                      color: var(--md-grey-700);
                    }
                  }

                  input,
                  label {
                    cursor: pointer;
                  }

                  .device-present-info-container {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    margin: 0;

                    .device-info {
                      flex-grow: 1;
                      display: flex;
                      justify-content: space-between;
                      align-items: center;

                      span {
                        font-weight: normal;
                      }
                    }
                  }

                  .indeterminate .checkbox-icon-helper {
                    &::after {
                      content: '\ea8f';
                      color: #536dfe;
                    }

                    i {
                      color: var(--color-white);
                    }
                  }
                }

                .device-present-info {
                  margin-right: 8px;
                  font-size: 85%;
                  font-weight: 400;
                  color: var(--md-grey-700);
                  white-space: nowrap;
                }
              }
            }

            .accordion-body {
              margin-left: 68px;
              padding: 0;
              padding-right: 8px;

              .site-item-expand {
                border-top: 1px solid var(--dropdown-border-hover);
                padding: 10px 12px;

                span {
                  font-weight: normal;
                }

                .serial-text-gray {
                  color: var(--md-grey-700);
                }

                .nested-device {
                  width: 100%;
                  display: flex;
                  gap: 8px;
                  cursor: pointer;
                  align-items: center;
                  margin-bottom: 0;
                }
              }
            }
          }
        }

        .noSitesFound {
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          align-items: center;
          height: 18.125rem;
          background-color: var(--color-bg-fa);

          p {
            padding: 0.938rem;
            color: var(--md-grey-700);
            font-weight: 700;
            font-size: 1.4rem;
          }
        }

        h2 {
          margin: 0;
        }

        .accordion-button:focus {
          box-shadow: none;
        }

        .accordion-button.collapsed {
          background-color: white;
          box-shadow: none;
          color: var(--color-black);
        }

        .accordion-collapse.collapsing {
          background-color: var(--color-accordion-collapse);
        }

        .accordion-collapse.collapse.show {
          background-color: var(--color-accordion-collapse);
        }

        .accordion-button:not(.collapsed) {
          overflow: visible;
          background-color: var(--color-accordion-collapse);
          box-shadow: none;
          color: var(--color-black);
        }

        .accordion-button {
          padding: 0.313rem 0.313rem;
        }

        .accordion-button::after {
          content: none;
        }
      }
    }

    .tree-pagination {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      cursor: pointer;
      margin-top: 1rem;

      .block-pagination-arrow {
        opacity: 0.6;
        cursor: not-allowed !important;
      }

      .pointer-events-none {
        pointer-events: none;
      }

      .arrow {
        padding: 0.25rem 0.8rem;
        width: fit-content;
        border-radius: 0.25rem;

        &:hover {
          background-color: var(--color-white-shade-one);
        }
      }

      .left-arrow {
        margin-right: 0.313rem;
      }

      .right-arrow {
        margin-right: 0;
      }
    }
  }

  .ng-clear-wrapper {
    display: none;
  }
}
