import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatStepper } from '@angular/material/stepper';
import { Store } from '@ngrx/store';
import { Observable, Subject, takeUntil } from 'rxjs';
import { SOFTWARE } from '../../../constants/appConstant';
import { RemoteState } from '../../../store/remote.store';
import { DeviceType } from 'src/app/models/common';
import { loadDeviceTypes } from 'src/app/store/actions/device-types.actions';
import { loadSoftwareData } from 'src/app/store/actions/software.actions';
import { selectDeviceTypesData } from 'src/app/store/selectors/device-types.selector';
import { selectSoftwareIsLoading } from 'src/app/store/selectors/software.selector';

@Component({
  selector: 'app-device-type',
  templateUrl: './device-type.component.html',
  styleUrls: ['./device-type.component.scss'],
})
export class DeviceTypeComponent implements OnInit {
  @ViewChild('stepper') stepper!: MatStepper;

  @Input() formGroup!: FormGroup;

  @Input() activeStepIndex!: number;

  @Output() goToNextStep = new EventEmitter<void>();

  deviceTypes$!: Observable<DeviceType[]>;

  isLoading$!: Observable<boolean>;

  deviceTypesData!: DeviceType[];

  selectedDeviceType!: DeviceType;

  private continueSubject = new Subject<void>();

  store = inject(Store<{ remote: RemoteState }>);

  ngOnInit(): void {
    this.store.dispatch(loadDeviceTypes({}));
    this.deviceTypes$ = this.store.select(selectDeviceTypesData);
    this.deviceTypes$.subscribe(deviceTypes => {
      this.deviceTypesData = deviceTypes;
    });
    this.isLoading$ = this.store.select(selectSoftwareIsLoading);
  }

  onContinue() {
    this.loadDeviceFiles();
    this.isLoading$
      .pipe(takeUntil(this.continueSubject))
      .subscribe(isLoading => {
        if (!isLoading) {
          this.goToNextStep.emit();
          this.continueSubject.next();
        }
      });
  }

  onSelectionChange(device: DeviceType) {
    this.selectedDeviceType = device;
    this.formGroup.get('deviceType')?.setValue(device);
    this.formGroup.get('deviceType')?.setErrors({ invalidFormat: true });
  }

  loadDeviceFiles() {
    this.formGroup.get('deviceType')?.setErrors(null);
    this.formGroup.updateValueAndValidity();
    this.store.dispatch(
      loadSoftwareData({
        params: {
          deviceType: this.selectedDeviceType.id,
          type: SOFTWARE,
          pageIndex: 0,
          pageSize: 50,
        },
      })
    );
  }
}
