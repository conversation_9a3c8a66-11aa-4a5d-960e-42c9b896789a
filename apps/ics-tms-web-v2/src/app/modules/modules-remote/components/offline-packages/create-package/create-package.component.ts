import { Component, ElementRef, Renderer2, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { MatStepper } from '@angular/material/stepper';
import {
  OfflinePackage,
  OfflinePackagePayload,
} from '../../../models/OfflinePackage.model';
import { OfflinePackagesService } from '../../../services/offline-packages.service';
import { PackageNameComponent } from '../package-name/package-name.component';
import { loadDeviceTypes } from 'src/app/store/actions/device-types.actions';
import { ToastService } from 'src/app/services/toast.service';

@Component({
  selector: 'app-create-package',
  templateUrl: './create-package.component.html',
  styleUrls: ['./create-package.component.scss'],
})
export class CreatePackageComponent {
  @ViewChild('stepper') stepper!: MatStepper;

  @ViewChild('createOfflinePackage', { static: true })
  createOfflinePackage!: ElementRef;

  parentForm: FormGroup;

  createPackageLoading = false;

  activeStepIndex = 0;

  payload!: OfflinePackagePayload;

  constructor(
    private fb: FormBuilder,
    private store: Store,
    private router: Router,
    private offlinePackageService: OfflinePackagesService,
    private renderer: Renderer2,
    private toastService: ToastService
  ) {
    this.parentForm = this.fb.group({
      packageNameForm: this.fb.group({
        packageName: new FormControl<string>('', {
          validators: [
            Validators.required,
            PackageNameComponent.packageNameValidator(),
          ],
          nonNullable: false,
        }),
      }),

      deviceTypeForm: this.fb.group({
        deviceType: ['', Validators.required],
      }),

      fileSelectForm: this.fb.group({
        fileSelect: [null, Validators.required],
      }),
    });
    this.store.dispatch(loadDeviceTypes({}));
  }

  ngOnInit() {
    this.deviceTypeForm.statusChanges.subscribe(() => {
      this.updateStepState();
    });
    this.packageNameForm.statusChanges.subscribe(() => {
      this.updateStepState();
    });
    this.fileSelectForm.statusChanges.subscribe(() => {
      this.updateStepState();
    });
  }

  get packageNameForm() {
    return this.parentForm.get('packageNameForm') as FormGroup;
  }

  get deviceTypeForm() {
    return this.parentForm.get('deviceTypeForm') as FormGroup;
  }

  get fileSelectForm() {
    return this.parentForm.get('fileSelectForm') as FormGroup;
  }

  onStepChange(event: { selectedIndex: number }) {
    this.activeStepIndex = event.selectedIndex;
  }

  createPackage() {
    this.createPackageLoading = true;
    this.payload = {
      name: this.packageNameForm.value.packageName,
      softwareIds: this.fileSelectForm.value.fileSelect.map(
        (file: any) => file.id
      ),
    };
    this.offlinePackageService.createOfflinePackage(this.payload).subscribe({
      next: (data: OfflinePackage) => {
        // TO-DO - remove timeout when api is fixed
        setTimeout(() => {
          this.createPackageLoading = false;
          this.router.navigate(['/remote/packages'], {
            queryParams: { expanded: data.id },
          });
        }, 1000);
      },
      error: () => {
        this.toastService.show({
          message: 'Failed to create package',
        });
        this.createPackageLoading = false;
      },
    });
  }

  selectedDeviceType() {
    return this.deviceTypeForm.value.deviceType.id;
  }

  isFileSelected(): boolean {
    return (this.fileSelectForm.value.fileSelect?.length ?? 0) > 0;
  }

  goToNextStep() {
    if (this.stepper.selected?.stepControl?.valid) {
      this.stepper.next();
    }
  }

  isPackageValid() {
    return this.packageNameForm.valid && this.isFileSelected();
  }

  ngAfterViewInit() {
    this.updateStepState();
  }

  updateStepState() {
    const steps =
      this.createOfflinePackage.nativeElement.querySelectorAll('.mat-step');

    if (!steps.length) return;

    this.updateStep(1, this.packageNameForm.invalid);
    this.updateStep(
      2,
      this.packageNameForm.invalid || this.deviceTypeForm.invalid
    );
    this.updateStep(3, this.packageNameForm.invalid || !this.isFileSelected());
  }

  updateStep(stepIndex: number, disableCondition: boolean) {
    const steps =
      this.createOfflinePackage.nativeElement.querySelectorAll('.mat-step');

    if (!steps[stepIndex]) return;

    if (disableCondition) {
      this.renderer.addClass(steps[stepIndex], 'disable-offline-package-step');
      return;
    }
    this.renderer.removeClass(steps[stepIndex], 'disable-offline-package-step');
  }

  getAriaLabel(...conditions: string[]): string {
    const formConditions: { [key: string]: () => boolean } = {
      packageNameInvalid: () => this.packageNameForm.invalid,
      deviceTypeInvalid: () => this.deviceTypeForm.invalid,
      fileNotSelected: () => !this.isFileSelected(),
    };

    const isAnyConditionInvalid = conditions.some(condition =>
      formConditions[condition]?.()
    );

    return isAnyConditionInvalid ? 'disable-mat-step' : '';
  }
}
