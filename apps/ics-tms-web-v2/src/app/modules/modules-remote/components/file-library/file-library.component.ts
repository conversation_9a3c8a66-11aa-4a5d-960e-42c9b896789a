import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  inject,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import {
  BehaviorSubject,
  debounceTime,
  fromEvent,
  Observable,
  Subject,
  takeUntil,
} from 'rxjs';
import { getApiConstants } from '../../constants/api';
import {
  OPTIC_12,
  OPTIC_DEVICE_SUB_TYPES,
  RESTRICTED_DEVICE_TYPES,
  SELECT_ALL_MESSAGE,
  SOFTWARE,
  UNDEFINED,
} from '../../constants/appConstant';
import { PaginationState } from '../../models/OfflinePackage.model';
import {
  DeviceFile,
  DeviceType,
  FileLibraryFilters,
  FileLibraryFilterTag,
  FileLibraryMetaData,
  FileLibraryParams,
  FileLibraryResponse,
} from '../../models/file-library.model';
import { FileLibraryService } from '../../services/file-library.service';
import {
  applyFilters,
  loadFileLibraryUsers,
} from '../../store/actions/file-library.actions';
import { RemoteState } from '../../store/remote.store';
import {
  getFileLibraryData,
  getFileLibraryLoading,
  getSelectedFilters,
} from '../../store/selectors/file-library.selectors';
import { DeleteFilePopupComponent } from './delete-file-popup/delete-file-popup.component';
import { FileLibraryFilterPopupComponent } from './file-library-filter-popup/file-library-filter-popup.component';
import { RegisterSoftwareComponent } from './register-software/register-software.component';
import {
  selectDeviceTypesData,
  selectDeviceTypesIsLoading,
} from 'src/app/store/selectors/device-types.selector';
import { loadDeviceTypes } from 'src/app/store/actions/device-types.actions';
import { ToastService } from 'src/app/services/toast.service';
import { AuthService } from 'src/app/services/auth.service';
import { SoftwareParams } from 'src/app/models/ics-stepper-template.model';
import { getAssets } from 'src/app/constants/appConstants';

@Component({
  selector: 'app-file-library',
  templateUrl: './file-library.component.html',
  styleUrls: ['./file-library.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class FileLibraryComponent implements OnInit {
  @ViewChild('tagsContainer', { static: false }) tagsContainer!: ElementRef;

  @ViewChild('selectAllCheckbox', { static: false })
  selectAllCheckbox!: ElementRef;

  private cdRef = inject(ChangeDetectorRef);

  private readonly destroy$ = new Subject<void>();

  deviceTypes$ = new BehaviorSubject<DeviceType[]>([]);

  selectedTab!: DeviceType;

  softwareParams!: SoftwareParams;

  currentPage!: number;

  loading$!: Observable<boolean>;

  readonly descLimit = { lower: 100, upper: 1000 };

  readonly showFullDescription: Record<number, boolean> = {};

  files$ = new BehaviorSubject<FileLibraryResponse>({
    results: [],
    resultsMetadata: {
      totalResults: 0,
      pageIndex: 0,
      pageSize: 20,
      maxFileDeleteLimit: 0,
    },
  });

  isFilesLoading$: Observable<boolean>;

  isDeviceTypesLoading$: Observable<boolean>;

  searchedText: string = '';

  previousSearchedText: string = '';

  visibleTags: FileLibraryFilterTag[] = [];

  hiddenTags: FileLibraryFilterTag[] = [];

  moreTagsCount = 0;

  selectAllText = SELECT_ALL_MESSAGE;

  selectedFiles: number[] = [];

  excludedFiles: number[] = [];

  getAssets = getAssets;

  currentParams$ = new BehaviorSubject<FileLibraryParams>({
    pageIndex: 0,
    deviceType: '',
    type: SOFTWARE,
    pageSize: 20,
    subDeviceType: '',
  });

  paginationState$ = new BehaviorSubject<PaginationState>({
    currentPage: 1,
    pageSize: 20,
    totalResults: 0,
    isPaginationVisible: false,
  });

  selectedFilters: FileLibraryFilters = {
    createdBy: [],
    createdBefore: 0,
    fileExtensions: [],
    isDuplicate: false,
  };

  private searchSubject$ = new Subject<string>();

  private readonly debounceTimeMs = 300;

  visibleTags$: BehaviorSubject<string[]> = new BehaviorSubject<string[]>([]);

  constructor(
    private store: Store<{ remote: RemoteState }>,
    public modalService: NgbModal,
    private router: Router,
    private route: ActivatedRoute,
    private fileLibraryService: FileLibraryService,
    private toastService: ToastService
  ) {
    this.store.select(getFileLibraryData).subscribe(files => {
      this.files$.next(files);
      if (this.selectAllText !== SELECT_ALL_MESSAGE) {
        this.selectedFiles = this.files$
          .getValue()
          .results.reduce((acc, file) => {
            if (!this.excludedFiles.includes(file.id)) {
              acc.push(file.id);
            }
            return acc;
          }, [] as number[]);
      }
    });
    this.isFilesLoading$ = this.store.select(getFileLibraryLoading);
    this.isDeviceTypesLoading$ = this.store.select(selectDeviceTypesIsLoading);
    this.loadFromCache();
  }

  ngOnInit(): void {
    this.setupDeviceTypesSubscription();
    this.setupFilesSubscription();
    this.handleSearchedTextSubscription();
    this.handleSelectedFiltersSubscription();
  }

  ngAfterViewInit() {
    this.updateTags();
    fromEvent(window, 'resize')
      .pipe(debounceTime(300), takeUntil(this.destroy$))
      .subscribe(() => this.updateTags());
  }

  private handleSelectedFiltersSubscription() {
    this.store.select(getSelectedFilters).subscribe(filters => {
      this.selectedFilters = filters;
    });
  }

  private setupDeviceTypesSubscription(): void {
    this.store.dispatch(loadDeviceTypes({}));
    this.store.dispatch(loadFileLibraryUsers());

    this.store
      .select(selectDeviceTypesData)
      .pipe(takeUntil(this.destroy$))
      .subscribe(deviceTypes => {
        this.deviceTypes$.next([...deviceTypes, ...OPTIC_DEVICE_SUB_TYPES]);
        if (!deviceTypes.length) return;

        const currentParams = this.currentParams$.getValue();

        if (!currentParams.deviceType) {
          this.currentParams$.next({
            ...currentParams,
            deviceType: deviceTypes[0]?.id || '',
          });
        }

        this.initializePageFromRoute();
      });
  }

  private initializePageFromRoute(): void {
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe(({ pageIndex = 0, filters, deviceType }) => {
        const parsedFilters = filters ? JSON.parse(filters) : {};
        const currentPageIndex = Math.max(+pageIndex, 0);
        const currentParams = this.currentParams$.getValue();
        this.currentParams$.next({
          ...currentParams,
          pageIndex: currentPageIndex,
          deviceType: deviceType || this.currentParams$.getValue().deviceType,
          ...(parsedFilters && { filters: { ...parsedFilters } }),
        });
        if (parsedFilters?.fileName) {
          this.searchedText = parsedFilters.fileName;
        }
        this.handleTabSelection();
        this.store.dispatch(applyFilters({ filters: parsedFilters }));
        this.selectedFilters = parsedFilters;
        this.updateTags();
        this.refetchFiles();
      });
  }

  private handleTabSelection() {
    const currentDeviceType = this.currentParams$.getValue().deviceType;
    this.selectedTab =
      this.deviceTypes$
        .getValue()
        .find(device => device.id === currentDeviceType) ||
      OPTIC_DEVICE_SUB_TYPES.find(device => device.id === currentDeviceType) ||
      this.deviceTypes$.getValue()[0];
  }

  private setupFilesSubscription(): void {
    this.files$.pipe(takeUntil(this.destroy$)).subscribe(files => {
      if (files?.resultsMetadata) {
        this.updatePaginationState(files.resultsMetadata);
        if (this.selectAllText !== SELECT_ALL_MESSAGE)
          this.updateSelectAllText();
      }
    });
  }

  private handleSearchedTextSubscription() {
    this.searchSubject$
      .pipe(debounceTime(this.debounceTimeMs), takeUntil(this.destroy$))
      .subscribe(searchedValue => {
        this.performSearch(searchedValue);
      });
  }

  private performSearch(searchedValue: string) {
    const currentFilters = this.currentParams$.getValue().filters;
    if (!searchedValue) {
      delete currentFilters?.fileName;
    }
    const currentParams = {
      ...this.currentParams$.getValue(),
      pageIndex: 0,
      filters: {
        ...currentFilters,
        ...(searchedValue && { fileName: searchedValue }),
      },
    };
    this.currentParams$.next(currentParams);
    this.updateRoute();
  }

  handlePageChange(pageNumber: number): void {
    const pageIndex = pageNumber - 1;
    this.currentParams$.next({ ...this.currentParams$.getValue(), pageIndex });
    this.updateRoute();
  }

  onSelectionChange(device: DeviceType): void {
    if (this.shouldTabActionDisabled(device.id)) {
      return;
    }

    if (this.selectedTab.id === device.id) {
      return;
    }

    this.selectedTab = device;
    const isSubType = OPTIC_DEVICE_SUB_TYPES.some(
      subType => subType.id === device.id
    );
    this.currentParams$.next({
      ...this.currentParams$.getValue(),
      pageIndex: 0,
      subDeviceType: isSubType ? device.id : null,
      deviceType: device.id,
    });
    this.clearCache();
    this.loadFromCache();
    this.updateRoute();
  }

  private updatePaginationState(metadata: FileLibraryMetaData): void {
    this.paginationState$.next({
      currentPage: metadata.pageIndex + 1,
      pageSize: 20,
      totalResults: metadata.totalResults,
      isPaginationVisible: metadata.totalResults > 20,
    });
  }

  private getDeviceIdAndSubType() {
    const deviceId = this.currentParams$.getValue().deviceType;

    const isSubType = OPTIC_DEVICE_SUB_TYPES.some(
      subType => subType.id === this.currentParams$.getValue().deviceType
    );
    return {
      deviceId,
      isSubType,
    };
  }

  openDialog(type: string, item?: DeviceFile) {
    const isUpload = type === 'Upload';
    const modalRef = this.modalService.open(RegisterSoftwareComponent, {
      backdrop: isUpload ? 'static' : true,
      keyboard: !isUpload,
      windowClass: 'common-menu-popup-modal in',
      container: '#ng-modal-container',
      size: 'sm',
    });
    modalRef.componentInstance.item = item;
    modalRef.componentInstance.type = type;
    modalRef.componentInstance.deviceTypes = this.deviceTypes$.getValue();
    modalRef.result.then(update => {
      if (update) {
        this.clearCache();
        this.loadFromCache();
        this.showToast(isUpload ? 'File Registered' : 'File Updated');
        this.refetchFiles();
      }
    });
  }

  updateSoftwareDetails(item: DeviceFile) {
    this.openDialog('Update', item);
  }

  getSoftwareFileUrl(item: DeviceFile) {
    return `${getApiConstants().fileLibrary.fileDownload}/${item.id}/${
      item.softwareFile
    }`;
  }

  handleDelete(type: string, item?: DeviceFile) {
    const modalRef = this.modalService.open(DeleteFilePopupComponent, {
      centered: true,
      windowClass: 'common-menu-popup-modal in',
      container: '#ng-modal-container',
      size: 'sm',
    });
    const deleteAll = this.selectAllText !== SELECT_ALL_MESSAGE;
    const {
      resultsMetadata: { totalResults },
    } = this.files$.getValue();
    modalRef.componentInstance.type = type;
    modalRef.componentInstance.id = item?.id;
    modalRef.componentInstance.name = item?.name;
    modalRef.componentInstance.filesToDelete = this.selectedFiles;
    modalRef.componentInstance.deleteAll = deleteAll;
    modalRef.componentInstance.totalFiles = totalResults;
    modalRef.componentInstance.deviceType = this.isOptic()
      ? OPTIC_12.id
      : this.selectedTab.id;
    const deletedFiles = totalResults - this.excludedFiles.length;
    modalRef.result
      .then(fileDeleted => {
        if (fileDeleted) {
          const count = deleteAll ? deletedFiles : this.selectedFiles.length;
          const fileLabel = count > 1 ? 'Files' : 'File';
          const toastMessage =
            type === 'File'
              ? `${count} ${fileLabel} Successfully Deleted`
              : 'Software Archived';
          type === 'File'
            ? this.undoDeleteToast(toastMessage, fileDeleted)
            : this.showToast(toastMessage);
          this.clearCache();
          this.loadFromCache();
          this.refetchFiles();
        }
      })
      .catch(() => {});
  }

  undoDeleteToast(message: string, deleteRequestId: string) {
    this.toastService.show({
      message,
      type: 'success',
      classname: 'fl-delete-toast',
      custom: true,
      action: () => {
        this.fileLibraryService
          .undoDelete({
            deviceType: this.isOptic() ? OPTIC_12.id : this.selectedTab.id,
            deleteRequestId,
          })
          .subscribe({
            next: () => {
              this.refetchFiles();
            },
            error: () => {},
          });
      },
    });
  }

  refetchFiles(pageIndex?: number) {
    const { isSubType, deviceId } = this.getDeviceIdAndSubType();
    const { selectedUsers, selectedExtensions } = this.extractProperty();
    const currentParams = this.currentParams$.getValue();

    this.fileLibraryService.loadFilesData({
      ...currentParams,
      ...(pageIndex && { pageIndex }),
      deviceType: isSubType ? OPTIC_12.id : deviceId,
      subDeviceType: isSubType ? deviceId : null,
      filters: {
        ...currentParams.filters,
        ...this.selectedFilters,
        createdBy: selectedUsers,
        fileExtensions: selectedExtensions,
      },
    });
  }

  isUploadedToday(uploadedDate: string): boolean {
    const uploaded = new Date(uploadedDate);
    const now = new Date();

    const diffInMilliseconds = now.getTime() - uploaded.getTime();
    const diffInHours = diffInMilliseconds / (1000 * 60 * 60);

    return diffInHours <= 24;
  }

  isOptic() {
    return OPTIC_DEVICE_SUB_TYPES.some(
      device => device.id === this.selectedTab.id
    );
  }

  toggleDescription(id: number): void {
    this.showFullDescription[id] = !this.showFullDescription[id];
  }

  getDescription(file: DeviceFile) {
    if (file.description === UNDEFINED) {
      return '';
    }
    if (this.showFullDescription[file.id]) {
      return file.description;
    }
    return file.description.slice(0, this.descLimit.lower);
  }

  shouldShowToggle(file: DeviceFile) {
    return file.description.length > this.descLimit.lower;
  }

  showToast(message: string, delay?: number) {
    this.toastService.show({
      message,
      delay,
    });
  }

  mayEdit() {
    return AuthService.isAllowedAccess('WRITE_FILES');
  }

  mayDownload() {
    return AuthService.isAllowedAccess('DOWNLOAD_FILES');
  }

  handleSearchedText() {
    const trimmedText = this.searchedText.trim();
    if (trimmedText !== this.previousSearchedText) {
      this.searchSubject$.next(trimmedText);
      this.previousSearchedText = trimmedText;
    }
  }

  clearSearchedText() {
    this.searchedText = '';
    this.previousSearchedText = '';
    this.searchSubject$.next(this.searchedText);
  }

  filterFiles(): void {
    const modalRef = this.modalService.open(FileLibraryFilterPopupComponent, {
      centered: true,
      container: '#ng-modal-container',
      windowClass: 'common-menu-popup-modal in filter-popup',
      size: 'lg',
      animation: true,
    });

    modalRef.result
      .then((updated: boolean) => {
        if (updated) {
          this.handleFilterModalClose();
        }
      })
      .catch(() => {});
  }

  private handleFilterModalClose(): void {
    const currentFilters = this.currentParams$.getValue().filters;
    const currentParams = {
      ...this.currentParams$.getValue(),
      pageIndex: 0,
      filters: {
        ...currentFilters,
        ...this.selectedFilters,
        createdBy: this.selectedFilters.createdBy ?? [],
        fileExtensions: this.selectedFilters.fileExtensions ?? [],
      },
    };
    this.currentParams$.next(currentParams);
    this.updateRoute();
  }

  removeTag(filterType: string, id?: string | number) {
    const currentFilters = this.currentParams$.getValue().filters!;

    switch (filterType) {
      case 'isDuplicate':
        currentFilters.isDuplicate = !currentFilters?.isDuplicate;
        break;
      case 'createdBefore':
        currentFilters.createdBefore = 0;
        break;
      case 'ext':
        currentFilters.fileExtensions = currentFilters.fileExtensions?.filter(
          ext => ext.id !== id
        );
        break;
      case 'user':
        currentFilters.createdBy = this.selectedFilters.createdBy?.filter(
          user => user.id !== id
        );
        break;
      default:
        break;
    }
    this.currentParams$.next({
      ...this.currentParams$.getValue(),
      pageIndex: 0,
      filters: {
        ...currentFilters,
      },
    });
    this.store.dispatch(applyFilters({ filters: currentFilters }));
    this.updateRoute();
  }

  updateTags() {
    if (!this.tagsContainer) return;

    const availableSpace = this.tagsContainer.nativeElement.offsetWidth - 20;
    let totalWidth = 100;
    this.visibleTags = [];
    this.hiddenTags = [];

    const tags = this.generateTags();
    tags.forEach(tag => {
      const tagElement = document.createElement('div');
      tagElement.style.position = 'absolute';
      tagElement.style.visibility = 'hidden';
      tagElement.innerHTML = `<span>${tag.label}</span>`;
      document.body.appendChild(tagElement);

      const tagWidth = tagElement.offsetWidth + 25;
      document.body.removeChild(tagElement);

      if (totalWidth + tagWidth <= availableSpace) {
        totalWidth += tagWidth;
        this.visibleTags.push(tag);
      } else {
        this.hiddenTags.push(tag);
      }
    });

    this.moreTagsCount = this.hiddenTags.length;
    this.cdRef.detectChanges();
  }

  generateTags() {
    const { filters } = this.currentParams$.getValue();
    const tags: FileLibraryFilterTag[] = [];

    if (filters?.isDuplicate) {
      tags.push({ label: 'Duplicate Files', type: 'isDuplicate' });
    }
    if (filters?.createdBefore) {
      tags.push({
        label: `${filters.createdBefore} day${filters.createdBefore > 1 ? 's' : ''} ago`,
        type: 'createdBefore',
      });
    }
    filters?.createdBy?.forEach(user => {
      tags.push({ label: user.fullName, type: 'user', id: user.id });
    });
    filters?.fileExtensions?.forEach(ext => {
      tags.push({ label: ext.name, type: 'ext', id: ext.id });
    });

    return tags;
  }

  private extractProperty() {
    const selectedUsers = this.selectedFilters.createdBy?.map(user => user.id);
    const selectedExtensions = this.selectedFilters.fileExtensions?.map(
      ext => ext.name
    );
    return {
      selectedUsers,
      selectedExtensions,
    };
  }

  private updateRoute(): void {
    const { pageIndex, deviceType, filters } = this.currentParams$.getValue();
    this.router.navigate([], {
      queryParams: {
        pageIndex,
        deviceType,
        ...(!isEmpty(filters) && { filters: JSON.stringify(filters) }),
      },
    });
  }

  tagsLength(): number {
    return this.visibleTags.length + this.hiddenTags.length;
  }

  ngOnDestroy(): void {
    this.searchSubject$.complete();
    this.destroy$.next();
    this.destroy$.complete();
  }

  selectAllFiles(event: Event) {
    const { checked } = event.target as HTMLInputElement;
    this.excludedFiles = [];
    if (checked) {
      this.selectAllFilesInternal();
      return;
    }
    this.deSelectAllFiles();
  }

  private selectAllFilesInternal(): void {
    const {
      results,
      resultsMetadata: { totalResults },
    } = this.files$.getValue();

    this.selectedFiles = results.map(file => file.id);
    this.selectAllText = `All ${totalResults > 1 ? totalResults : ''} files are selected`;

    this.updateCache();
  }

  private deSelectAllFiles(): void {
    this.selectedFiles = [];
    this.selectAllText = SELECT_ALL_MESSAGE;
    this.clearCache();
  }

  selectFile(id: number) {
    const index = this.selectedFiles.indexOf(id);
    if (index > -1) {
      this.selectedFiles.splice(index, 1);
      if (
        this.isAllSelected() ||
        (this.isFewSelected() && this.excludedFiles.indexOf(id) === -1)
      ) {
        this.excludedFiles.push(id);
      }
    } else {
      this.selectedFiles.push(id);
      if (this.excludedFiles.indexOf(id) > -1) {
        this.excludedFiles.splice(this.excludedFiles.indexOf(id), 1);
      }
    }
    if (this.selectAllText !== SELECT_ALL_MESSAGE) this.updateSelectAllText();
    this.updateCache();
  }

  updateCache() {
    localStorage.setItem('selectedFiles', JSON.stringify(this.selectedFiles));
    localStorage.setItem('allSelectedText', this.selectAllText);
    localStorage.setItem('excludedFiles', JSON.stringify(this.excludedFiles));
  }

  clearCache() {
    localStorage.removeItem('selectedFiles');
    localStorage.removeItem('allSelectedText');
    localStorage.removeItem('excludedFiles');
  }

  loadFromCache(): void {
    this.selectedFiles = this.parseLocalStorageItem('selectedFiles', []);
    this.selectAllText =
      localStorage.getItem('allSelectedText') || SELECT_ALL_MESSAGE;
    this.excludedFiles = this.parseLocalStorageItem('excludedFiles', []);
  }

  private parseLocalStorageItem<T>(key: string, defaultValue: T): T {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  }

  isFileSelected(id: number) {
    return this.selectedFiles.indexOf(id) > -1;
  }

  isAllSelected() {
    return this.selectAllText.startsWith('All');
  }

  isFewSelected() {
    if (this.selectAllText === SELECT_ALL_MESSAGE) return false;
    if (isEmpty(this.selectedFiles)) return false;
    return (
      this.selectedFiles.length < this.files$.getValue().results.length ||
      this.excludedFiles.length
    );
  }

  private updateSelectAllText(): void {
    const {
      resultsMetadata: { totalResults },
    } = this.files$.getValue();
    const totalFilesSelected = totalResults - this.excludedFiles.length;

    switch (true) {
      case totalFilesSelected === totalResults:
        this.selectAllText = `All ${totalResults} files are selected`;
        break;
      case totalFilesSelected === 0:
        this.selectAllText = SELECT_ALL_MESSAGE;
        break;
      default:
        this.selectAllText = `${totalFilesSelected} of ${totalResults} files are selected`;
    }
  }

  isDeleteDisabled() {
    return (
      isEmpty(this.selectedFiles) && this.selectAllText === SELECT_ALL_MESSAGE
    );
  }

  shouldTabActionDisabled(key: string) {
    const regex = new RegExp(RESTRICTED_DEVICE_TYPES.join('|'), 'i');
    const shouldActionDisable = regex.test(key);
    return shouldActionDisable;
  }

  formatDate(date: string) {
    return dayjs(date).format('MMM D, YYYY [at] h:mm a');
  }

  formatDateUTC(date: string) {
    return dayjs(date).format('(UTCZ)');
  }

  isMaxFileDeleteLimitReached(): boolean {
    const { maxFileDeleteLimit, totalResults } =
      this.files$.getValue().resultsMetadata;
    return totalResults > maxFileDeleteLimit;
  }
}
