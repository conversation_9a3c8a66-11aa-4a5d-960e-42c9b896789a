import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { debounceTime, Observable, Subject, Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import _ from 'lodash';
import { loadFileDownloads } from '../../store/actions/file-downloads.actions';
import * as selectors from '../../store/selectors/file-downloads.selectors';
import {
  PAGE,
  PAGE_SIZE,
  SOFTWARE,
  STATUS_FILTER_ITEMS,
} from '../../constants/appConstant';
import { FileDownloadParams } from '../../models/file-downloads.model';
import { FileDownloadsService } from '../../services/file-downloads.service';
import { TableDef } from 'src/app/constants/tableColumnDef';
import { loadSiteGroups } from 'src/app/store/actions/site-groups.actions';
import { SoftwareParams } from 'src/app/models/ics-stepper-template.model';
import { FILE_DOWNLOADS } from 'src/app/constants/globalConstant';
import { MediaDownloadsService } from 'src/app/modules/module-media/services/media-downloads.service';
import { loadDeviceTypes } from 'src/app/store/actions/device-types.actions';
import {
  DeviceStatus,
  DeviceType,
  FilterGroup,
  FilterItem,
  ViewType,
} from 'src/app/models/common';
import { selectDeviceTypesData } from 'src/app/store/selectors/device-types.selector';
import { AuthService } from 'src/app/services/auth.service';
import { FeatureFlags } from 'src/app/constants/appConstants';

@Component({
  selector: 'app-file-download',
  templateUrl: './file-download.component.html',
  styleUrls: ['./file-download.component.scss'],
})
export class FileDownloadComponent implements OnInit, OnDestroy {
  fileDownloads$!: Observable<TableDef>;

  rolloutParams: FileDownloadParams;

  softwareParams: SoftwareParams;

  searchText = '';

  searchTextChanged = new Subject<string>();

  prevSearchText = '';

  pageIndex = 0;

  loading$!: Observable<boolean>;

  FILE_DOWNLOADS = FILE_DOWNLOADS;

  currentPageIndex = 0;

  error$!: Observable<string | null>;

  dropdownFilters: FilterGroup[] = [];

  showResultsMessage: boolean = false;

  resultsMessage: string = '';

  private subUpdateFileDownloadsService: Subscription | undefined;

  private subFileDownloads: Subscription | undefined;

  autoOpenId: number | null = null;

  constructor(
    private store: Store,
    private router: Router,
    private route: ActivatedRoute,
    private fileDownloadsService: FileDownloadsService,
    private mediaDownloadsService: MediaDownloadsService
  ) {
    const searchedMedia = this.mediaDownloadsService.loadSearchedMedia();
    this.rolloutParams = {
      autoPoll: false,
      pageIndex: this.loadPageIndex(),
      pageSize: PAGE_SIZE,
      type: SOFTWARE,
      ...(searchedMedia && { name: searchedMedia }),
    };
    this.softwareParams = { type: SOFTWARE };
    const idParam = this.route.snapshot.paramMap.get('id');
    this.autoOpenId = idParam ? +idParam : null;
  }

  ngOnInit(): void {
    // load device types, software data and site groups
    this.store.dispatch(loadDeviceTypes({}));
    this.store.dispatch(loadSiteGroups());

    this.store.select(selectDeviceTypesData).subscribe(deviceTypes => {
      if (deviceTypes?.length) {
        const deviceFilters: FilterItem<DeviceType>[] = deviceTypes.map(dt => ({
          group: 'Devices',
          selected: false,
          value: dt.name,
          obj: dt,
        }));

        const isApprovalFlagEnabled = AuthService.getFeatureFlags().some(
          flag =>
            flag.key === FeatureFlags.FILE_DOWNLOAD_APPROVAL_REQUIRED &&
            flag.active
        );
        const statusItems = STATUS_FILTER_ITEMS.filter(status =>
          status.value === 'releasePendingApproval' ||
          status.value === 'releaseRejected'
            ? isApprovalFlagEnabled
            : true
        );
        const statusFilters: FilterItem<DeviceStatus>[] = statusItems.map(
          s => ({
            group: 'Status',
            selected: false,
            value: s.name,
            obj: s,
          })
        );

        this.dropdownFilters = [
          { name: 'Devices', options: _.sortBy(deviceFilters, 'value') },
          { name: 'Status', options: _.sortBy(statusFilters, 'value') },
        ];
      }
    });

    // load file downloads
    this.store.dispatch(loadFileDownloads({ params: this.rolloutParams }));
    this.fileDownloads$ = this.store.select(selectors.selectFileDownloadsData);
    this.loading$ = this.store.select(selectors.selectFileDownloadsLoading);
    this.error$ = this.store.select(selectors.selectFileDownloadsError);

    this.setupSearch();

    // update file downloads
    this.subUpdateFileDownloadsService =
      this.fileDownloadsService.updateFileDownloads.subscribe(res => {
        if (res) {
          this.store.dispatch(
            loadFileDownloads({ params: this.rolloutParams })
          );
          this.fileDownloadsService.updateFileDownloads.next(false);
        }
      });

    this.subFileDownloads = this.fileDownloads$.subscribe(response => {
      const { name, deviceType, status } = this.rolloutParams;
      const { totalResults } = response.data.resultsMetadata;

      this.showResultsMessage =
        (totalResults === 0 && name) || deviceType || status;
      this.resultsMessage = 'No results found for the selected filters.';
      if (totalResults > 0) {
        if (deviceType && status) {
          const statusName = STATUS_FILTER_ITEMS.find(
            item => item.value === status
          );
          this.resultsMessage = `${totalResults} results found for ${deviceType} and ${statusName?.name}`;
        } else if (deviceType) {
          this.resultsMessage = `${totalResults} results found for ${deviceType}`;
        } else if (status) {
          const statusName = STATUS_FILTER_ITEMS.find(
            item => item.value === status
          );
          this.resultsMessage = `${totalResults} results found for ${statusName?.name}`;
        }
      }
    });
  }

  ngOnDestroy(): void {
    if (this.subUpdateFileDownloadsService) {
      this.subUpdateFileDownloadsService?.unsubscribe();
    }
    if (this.subFileDownloads) {
      this.subFileDownloads.unsubscribe();
    }
  }

  handleSearchText() {
    this.searchTextChanged.next(this.searchText);
  }

  setupSearch() {
    this.searchTextChanged.pipe(debounceTime(1000)).subscribe(model => {
      const value = model.trim();
      if (value !== this.prevSearchText) {
        this.prevSearchText = value;
        if (value !== '') {
          this.rolloutParams = {
            ...this.rolloutParams,
            name: value,
            pageIndex: 0,
          };
          this.searchQuery();
        } else {
          delete this.rolloutParams.name;
          this.rolloutParams = { ...this.rolloutParams, pageIndex: 0 };
          this.searchQuery();
        }

        // change navigation url according to 'search'
        this.error$.subscribe(error => {
          if (!error) {
            this.router.navigate([], {
              relativeTo: this.route,
              queryParams: {
                ...(model.length > 0 && { name: model }),
                page: this.rolloutParams.pageIndex,
              },
            });
          }
        });
      }
    });
  }

  searchQuery() {
    this.store.dispatch(loadFileDownloads({ params: this.rolloutParams }));
    this.fileDownloads$ = this.store.select(selectors.selectFileDownloadsData);
  }

  onPageIndexChange(currentPageIndex: number) {
    this.pageIndex = currentPageIndex - 1;
    const updatedParams: FileDownloadParams = {
      ...this.rolloutParams,
      pageIndex: this.pageIndex,
    };

    this.store.dispatch(loadFileDownloads({ params: updatedParams }));
    this.fileDownloads$ = this.store.select(selectors.selectFileDownloadsData);
    this.router.navigate([], {
      queryParams: { page: currentPageIndex - 1 },
    });
  }

  handleMediaSearch(searchedMedia: string) {
    this.searchTextChanged.next(searchedMedia);
  }

  loadPageIndex(): number {
    let page = this.route.snapshot.queryParamMap.get(PAGE);
    if (page && +page < 0) {
      page = String(0);
      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: {
          page: +page,
        },
      });
    }

    return page === null ? this.currentPageIndex : +page;
  }

  get viewType(): typeof ViewType {
    return ViewType;
  }

  handleFilters(value: {
    [key: string]: FilterItem<DeviceType | DeviceStatus>;
  }): void {
    const searchedMedia = this.mediaDownloadsService.loadSearchedMedia();
    this.rolloutParams = {
      autoPoll: false,
      pageIndex: 0,
      pageSize: PAGE_SIZE,
      type: SOFTWARE,
      ...(searchedMedia && { name: searchedMedia }),
      ...(value['Devices'] && {
        deviceType: (value['Devices'].obj as DeviceType).id,
      }),
      ...(value['Status'] && {
        status: (value['Status'].obj as DeviceStatus).value,
      }),
    };

    this.store.dispatch(loadFileDownloads({ params: this.rolloutParams }));
  }
}
