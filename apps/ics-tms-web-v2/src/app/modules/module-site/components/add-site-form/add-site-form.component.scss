.row {
  margin-right: -10px !important;
  margin-left: -10px !important;

  .form {
    margin-right: 2px;
    margin-left: 2px;
    border-radius: 3px;
    background: var(--color-white);
    box-shadow:
      0 1px 3px 0 rgba(0, 0, 0, 0.2),
      0 1px 1px 0 rgba(0, 0, 0, 0.14),
      0 2px 1px -1px rgba(0, 0, 0, 0.12);

    .form-body {
      padding: 20px 25px;

      .form-title {
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--md-red-grey-600);

        h3 {
          margin: 0;
          font-size: 18px;
          line-height: 1.1;
          font-weight: 500;
        }
      }

      hr {
        margin: 2rem 0;
      }
    }

    .form-footer {
      display: flex;
      gap: 12px;
      margin: 0 0 20px 0;
      padding: 10px 15px;
      border-top: 1px solid var(--md-red-grey-600);
      background-color: #f4f5f6;
    }

    .cursor-NA {
      cursor: not-allowed;
    }
  }

  .form-control {
    width: 100%;
    height: 3.4rem;
    padding: 0.6rem 1.2rem;
    transition:
      border-color ease-in-out 0.15s,
      box-shadow ease-in-out 0.15s;
    border-radius: 0.3rem;
    background-color: var(--color-white);
    background-image: none;
    position: relative;

    &:focus {
      box-shadow: inset 0 0 0 0.1rem rgba(68, 138, 255, 0.75);
      border-color: rgba(68, 138, 255, 0.75);
      outline: 0;
    }
  }

  .tags-input {
    .tags {
      width: 100%;

      .ng-dropdown-panel-items:has([role='option']:not([id]))
        .ng-option:last-child {
        background-color: #3f51b5;
        color: white;

        &:hover {
          background-color: #3f51b5;
          color: white;
        }
      }

      .ng-dropdown-panel-items:not(:has([role='option']:not([id])))
        .ng-option:first-child {
        background-color: #3f51b5;
        color: white;

        &:hover {
          background-color: #3f51b5;
          color: white;
        }
      }

      &input {
        height: 0 !important;
      }

      &.ng-select .ng-arrow-wrapper {
        display: none;
      }

      .ng-select-container {
        height: 34px !important;
        min-height: 34px;

        .ng-value-container {
          cursor: text;
          height: 34px;
          padding-top: 0 !important;
          padding-left: 3px !important;

          .ng-placeholder {
            top: 7px !important;
            padding-left: 7px;
          }

          .ng-value {
            align-items: center !important;

            .ng-value-icon {
              padding: 0;
              font-size: 18px;
              line-height: 85%;
            }
          }
        }
      }

      &.ng-select.ng-select-opened > .ng-select-container {
        transition:
          border-color ease-in-out 0.15s,
          box-shadow ease-in-out 0.15s;
        border: 0.1rem solid var(--md-red-grey-600);
        box-shadow: inset 0 0 0 0.1rem rgba(68, 138, 255, 0.75);
        border-color: rgba(68, 138, 255, 0.75) !important;
        outline: 0;
        border-radius: 0.3rem;
      }

      &.ng-dropdown-panel.ng-select-bottom {
        top: 109%;
      }

      &.ng-dropdown-panel {
        width: 12.5rem;
      }

      &.ng-dropdown-panel .ng-dropdown-panel-items {
        max-height: 18.75rem;
        font-size: 1.4rem;
      }

      &.ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
        padding: 5px 20px;
      }

      &.ng-option {
        &:hover {
          background-color: var(--color-bg-default) !important;
        }
      }

      &.ng-select.ng-select-multiple
        .ng-select-container
        .ng-value-container
        .ng-value {
        display: flex;
        flex-direction: row-reverse;
        margin: 0px 3px 3px 0px;
        padding: 2px 6px;
        align-items: baseline;
        background-color: var(--btn-light-gray-border);
        border-radius: 5rem;
        transition: all ease-in 0.1s;

        &:hover {
          background-color: var(--btn-cancel-hover-color);
          border-radius: 5rem;
          cursor: pointer;
        }
      }

      &.ng-select.ng-select-multiple
        .ng-select-container
        .ng-value-container
        .ng-value
        .ng-value-label {
        font-size: 1.2rem;
        font-weight: bold;
        border-radius: 5rem;
        transition: all ease-in 0.1s;

        &:hover {
          border-radius: 5rem;
          background-color: var(--btn-cancel-hover-color);
        }
      }

      &.ng-select .ng-clear-wrapper {
        display: none;
      }

      &.ng-select.ng-select-multiple
        .ng-select-container
        .ng-value-container
        .ng-value
        .ng-value-icon:hover {
        background: transparent;
      }

      &.ng-select.ng-select-multiple
        .ng-select-container
        .ng-value-container
        .ng-value
        .ng-value-icon.left {
        border-right: none;
      }

      &.ng-select .ng-select-container .ng-value-container .ng-input > input {
        cursor: text;
      }

      &.ng-select.ng-select-focused:not(.ng-select-opened)
        > .ng-select-container {
        box-shadow: inset 0 0 0 0.1rem rgba(68, 138, 255, 0.75);
        border-color: rgba(68, 138, 255, 0.75);
      }

      &.ng-select.ng-select-multiple
        .ng-select-container
        .ng-value-container
        .ng-placeholder {
        top: 0.438rem;
      }
    }
  }

  .form-field-container {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: space-between;

    .fc-powered {
      display: flex;
      flex-direction: column;
      gap: 1.2rem;
    }
  }

  .no-tag {
    color: #616161;
    font-size: 13px;
    margin-top: 3px;
  }

  tr {
    border-bottom: none;
    border-top: 0.1rem solid var(--tr-border-color);
  }

  tr:first-child {
    border-top: none;
  }

  .mb {
    margin-bottom: 20px;
  }

  .mb-15px {
    margin-bottom: 15px;
  }

  .mb-5px {
    margin-bottom: 5px;
  }

  .min-height-class {
    min-height: 5rem;
  }

  .text-grey {
    margin-top: 0.8rem;
    color: var(--color-dark-gray);
    font-weight: 500;
    text-transform: uppercase;
  }

  .w-100 {
    width: 90%;
  }

  .w-50 {
    width: 90%;
  }

  .w-60 {
    width: 70%;
  }

  .form-label {
    flex-grow: 1;
    text-align: right;
    font-size: 13px;
    font-weight: bold;
    margin-bottom: 0 !important;
  }

  .bold-header {
    font-weight: 500;
    color: var(--color-black);
    font-size: 1.4rem;
  }
}

.rki-div {
  line-height: 1.5;

  span {
    font-size: 90%;
  }
}

.divider {
  border-top-color: var(--divider-color);
  width: inherit;
}

.col-left {
  width: 20%;
}

.text-flex {
  margin-top: 3px;
  margin-bottom: 5px;
  display: flex;
  justify-content: flex-end;

  .no-tag {
    width: 82.5%;
  }
}

.display-middle {
  display: flex;
  align-items: center;
  font-size: 0.813rem;
  height: 2.4rem;
  gap: 1.6rem;

  input {
    height: 1.8rem;
    cursor: pointer;
    transition: all ease-in 0.1s;
    color: var(--label-unknown);
    font-weight: 700;
    width: 1.5rem;

    &:hover {
      color: var(--color-black);
    }
  }

  label {
    font-weight: 400;
    display: flex;
    align-items: center;
    font-size: 1.3rem;

    i {
      margin-left: 0.3rem;
      color: var(--md-red-grey-600);
    }
  }

  .checkbox-disabled {
    color: var(--checkbox-disabled);
    cursor: not-allowed;
  }
}

.custom-select-container {
  width: 100%;
  max-width: 18.75rem;
  margin-bottom: 2rem;
}

.selected-option {
  padding: 1.6rem;
}

.option-styling {
  background: var(--color-white);
  width: fit-content;
}

.select-input {
  background-color: var(--dropdown-by-default);
}

.form-select {
  font-size: 1.4rem;
  padding: 0.6rem 2.25rem 0.6rem 1.2rem;
}

.fa-trash-can {
  color: var(--label-danger);
}

.rki-dropdown {
  width: 100%;
  z-index: 1;

  &:hover {
    background-color: var(--color-bg-default);
  }

  .btn-outline-primary {
    border-color: var(--md-red-grey-600);

    &:focus {
      border-color: var(--dropdown-selected-border-hover);
      box-shadow: inset 0 0 0 0.1rem var(--dropdown-border-focus);
    }

    &:hover {
      border-color: var(--dropdown-border-hover);
    }
  }

  .dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    &:after {
      top: 50%;
      right: 0.938rem;
      transform: translateY(-50%);
    }

    background-color: var(--dropdown-by-default);
    color: var(--color-black);
    font-size: 1.4rem;

    &:focus {
      background-color: var(--dropdown-bg-selected);
      color: var(--color-black);
    }

    &:hover {
      background-color: var(--dropdown-hover);
      color: var(--color-black);
    }
  }

  .dropdown-menu {
    font-size: 1.4rem;
    max-height: 30rem;
    overflow-y: auto;
    --bs-dropdown-link-hover-bg: var(--color-bg-default);
    --bs-dropdown-link-hover-color: var(--color-black);
    --bs-dropdown-link-active-bg: var(--color-primary);
    --bs-dropdown-link-active-color: var(--color-white);
    margin: 0.2rem 0 0;
    padding: 0.8rem 0;
    box-shadow:
      0 0.1rem 0.3rem 0 rgba(0, 0, 0, 0.2),
      0 0.1rem 0.8rem 0 rgba(0, 0, 0, 0.14),
      0 0.2rem 0.1rem -0.1rem rgba(0, 0, 0, 0.12);
    min-width: 16rem;
  }

  .selected {
    background-color: var(--color-primary);
    color: var(--color-white);
  }

  .text-left {
    text-align: left;
  }
}

.siteGroups {
  width: 100%;
  background-color: var(--dropdown-bg-selected);
  border-radius: 0.3rem;

  .ng-arrow-wrapper {
    .ng-arrow {
      border-color: var(--color-black) transparent transparent !important;
    }
  }

  .ng-dropdown-panel-items:not(:has(.ng-option-selected))
    .ng-option:first-of-type {
    background-color: #3f51b5;
    color: white;

    &:hover {
      background-color: #3f51b5;
      color: white;
    }
  }

  &.ng-select-opened .ng-select-container {
    box-shadow: inset 0 0 0 0.1rem rgba(68, 138, 255, 0.75) !important;
    border-color: rgba(68, 138, 255, 0.75) !important;

    .ng-arrow {
      display: none;
    }
  }

  .ng-select-container {
    width: 100%;
    height: 34px !important;
    min-height: 34px;
    overflow: visible !important;
    border-radius: 0.3rem !important;
    color: var(--color-black-shade-one) !important;
    background-color: var(--dropdown-by-default);

    .ng-arrow-wrapper {
      margin: 0 3px 4px 0;

      .ng-arrow {
        border-width: 4px 4px 0 4px;
      }
    }

    &:hover:not(:focus-within) {
      background-color: var(--dropdown-hover);
      cursor: pointer;
    }

    &:focus {
      box-shadow: inset 0 0 0 0.2rem rgba(68, 138, 255, 0.75) !important;
      border-color: rgba(68, 138, 255, 0.75);
    }

    .ng-input {
      cursor: pointer;

      input {
        cursor: pointer !important;
        height: 100%;
        border-color: var(--dropdown-border-hover);
      }
    }

    .ng-clear-wrapper {
      display: none;
    }
  }

  .ng-dropdown-panel:has(.ng-dropdown-panel-items):has(div):has(
      .ng-option-disabled
    ) {
    display: none !important;
  }

  .ng-dropdown-panel {
    width: auto;
    min-width: 160px;
    box-shadow:
      0 1px 3px 0 rgba(0, 0, 0, 0.2),
      0 1px 8px 0 rgba(0, 0, 0, 0.14),
      0 2px 1px -1px rgba(0, 0, 0, 0.12);
    border-radius: 0.18rem;
    padding: 5px 0;
    background-clip: padding-box;

    .ng-option {
      padding: 5px 20px;

      &.ng-option-marked {
        background-color: transparent;
        color: inherit;
      }

      &:hover {
        background-color: var(--color-bg-default);
      }
    }

    .ng-option-selected {
      background-color: var(--color-primary) !important;
      color: var(--color-white) !important;
    }
  }
}

.site-name {
  margin-bottom: 0 !important;
}

.site-error-message {
  margin-top: 2px;
  margin-bottom: 5px;
  display: flex;
  justify-content: flex-end;

  .no-tag {
    width: 81.5%;
    color: #dd2c00;
  }

  .txt-invalid-address {
    margin-top: 2px;
    width: 81.5%;
    color: #f44336;
    font-size: 14px;
    font-weight: normal;
  }
}

.pac-container {
  position: absolute !important;
  z-index: 1;

  .pac-item {
    &:hover,
    &.keyboard-focus {
      cursor: pointer;
      background-color: var(--color-bg-default);
    }
  }
}

.pac-icon {
  &:hover {
    background: url('https://maps.gstatic.com/mapfiles/place_api/icons/geocode-71.png')
      no-repeat center;
    background-size: 1.4rem;
    margin: 0.8rem 0.6rem 0.375 0;
    vertical-align: middle;
  }
}

.delete-btn:active {
  border: none;
}

.disable-button {
  cursor: not-allowed !important;
  opacity: 0.65 !important;
  pointer-events: all !important;
  border-color: var(--active-btn-bg-color) !important;
  background-color: var(--active-btn-bg-color) !important;

  &:hover {
    border-color: var(--active-btn-bg-color) !important;
    background-color: var(--active-btn-bg-color) !important;
  }
}

input,
select {
  height: auto;
}

.ng-select.ng-select-multiple .ng-select-container .ng-value-container {
  cursor: text;
}

.ng-dropdown-panel {
  box-shadow:
    0 0.1rem 0.3rem 0 #00000033,
    0 0.1rem 0.8rem 0 #00000024,
    0 0.2rem 0.1rem -0.1rem #0000001f;
  overflow-x: hidden;
  margin-top: 0;
  font-size: 1.4rem;
  padding: 0.8rem 0;
  border: 0.1rem solid transparent;
  min-width: 10rem !important;
  width: auto !important;
  min-width: 160px;
  position: absolute;
  display: block;

  .ng-dropdown-panel-items .ng-option.ng-option-marked {
    &:hover {
      background-color: var(--color-bg-default);
    }
  }
}

.ng-select.ng-select-focused:not(.ng-select-opened) > .ng-select-container {
  box-shadow: inset 0 0 0 0.1rem #448affbf;
  border-color: #448affbf;
}

.validating {
  color: #616161 !important;
}

.p-0 {
  padding-right: 0;
}

.ng-dropdown-panel-items > div {
  display: flex;
  flex-direction: column;

  [role='option']:not([id]) {
    order: -1;
  }
}

.bootstrap-iso {
  .btn:first-child:active {
    color: black;
    background-color: #e2e2e2;
  }

  .btn.active {
    border: 1px solid transparent;
  }

  .btn:disabled {
    border: none;
  }
}

.control-label.control-label-sm {
  .site-info-tooltip {
    white-space: pre-line;

    .tooltip-arrow {
      display: none;
    }

    .tooltip-inner {
      margin-top: 5px;
      font-size: 12px;
      line-height: 1.43;
      font-family:
        'Roboto',
        'Helvetica Neue',
        -apple-system,
        BlinkMacSystemFont,
        Arial,
        sans-serif,
        system-ui;
      text-align: left;
    }
  }
}

.dropdown-menu.show {
  .dropdown-item {
    padding: 5px 20px;
  }

  .dropdown-item.selected:hover {
    background-color: var(--color-primary);
    color: white;
  }
}
