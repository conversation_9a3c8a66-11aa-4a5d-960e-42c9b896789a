.bootstrap-iso {
  .mfa-verification-modal {
    overflow-y: hidden;

    .modal-dialog {
      .modal-content {
        border-radius: 2px !important;

        .header {
          padding: 15px 15px 0px;

          h4 {
            margin: 0;
            line-height: 1.43;
            font-size: 18px;
          }
        }

        .body {
          padding: 10px 15px 30px;
          font-size: 14px;

          p {
            margin: 0 0 10px;
          }

          input {
            width: 100%;
            border: 1px solid var(--md-red-grey-600);
            border-radius: 3px;
            padding: 6px 12px;
            transition:
              border-color ease-in-out 0.15s,
              box-shadow ease-in-out 0.15s;

            &:focus {
              box-shadow: inset 0 0 0 1px #448affbf;
              border-color: #448affbf;
              outline: 0;
            }
          }
        }

        .footer {
          display: flex;
          justify-content: flex-end;
          gap: 5px;
          padding: 10px 15px;

          .btn-confirm {
            height: auto;
            width: auto;
            padding: 6px 17.5px;
            color: var(--btn-color-red);
            border-color: var(--dropdown-border-color);
            background-color: var(--dropdown-border-color);

            &:hover {
              color: var(--color-white);
              border-color: var(--btn-border-color-red);
              background-color: var(--btn-color-red);
            }
          }
        }
      }
    }
  }
}
