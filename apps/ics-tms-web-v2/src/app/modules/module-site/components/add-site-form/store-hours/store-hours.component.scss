.store-hours-selector {
  .selector-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    width: 100%;
    padding: 0.6rem 1.2rem;

    i {
      font-size: 1.2rem;
    }

    &:focus {
      box-shadow: inset 0 0 0 0.1rem rgba(68, 138, 255, 0.75);
      border-color: rgba(68, 138, 255, 0.75);
      outline: 0;
    }

    &:hover {
      border-color: var(--dropdown-border-hover);
      background-color: var(--color-bg-default);
    }
  }

  .box {
    border: 0.1rem solid transparent;
    background-color: var(--color-white);
    z-index: 1;
    box-shadow:
      0 0.1rem 0.3rem 0 rgba(0, 0, 0, 0.2),
      0 0.1rem 0.8rem 0 rgba(0, 0, 0, 0.14),
      0 0.2rem 0.1rem -0.1rem rgba(0, 0, 0, 0.12);
    border-radius: 0 0.3rem 0.3rem 0;
    position: absolute;

    ul {
      padding: 0;
      margin-bottom: 0;
    }
  }

  .left-box {
    min-width: 16rem;
    position: absolute;
    top: 100%;
    left: 0;
    margin: 0.2rem 0 0;
    padding: 5px 0;
    background-clip: padding-box;

    ul {
      li {
        font-weight: 400;
        line-height: 1.43;
        display: block;
        clear: both;
        padding: 5px 20px;
        white-space: nowrap;
        color: var(--color-black);
        cursor: pointer;

        &:hover {
          background-color: var(--color-bg-default);
        }
      }
    }
  }

  .right-box {
    cursor: text;
    left: calc(100% - 18.1rem);
    width: 18.1rem;
    margin-top: 0.1rem;
    margin-left: -0.1rem;

    ul {
      li {
        font-size: 1.2rem;
        display: -ms-flexbox;
        display: flex;
        padding: 5px 0;

        div {
          min-width: 2.5rem;
          padding: 0 0.8rem;
          text-align: center;
        }
      }
    }
  }
}

.form-field-container {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .fc-powered {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
  }
}

.form-control {
  width: 100%;
  height: 3.4rem;
  padding: 0 !important;
  transition:
    border-color ease-in-out 0.15s,
    box-shadow ease-in-out 0.15s;
  border-radius: 0.3rem;
  border: 0.1rem solid var(--md-red-grey-600);
  background-color: var(--dropdown-by-default);
}

.mb {
  margin-bottom: 0;
}

.w-100 {
  width: 90% !important;
}

.form-label {
  flex-grow: 1;
  text-align: right;
  font-weight: 500;
  font-size: 1.3rem;
  margin-bottom: 0 !important;
}

.col-left {
  width: 18%;
}

.selected {
  font-weight: 700 !important;
  text-decoration: none;
  outline: 0;
  background-color: var(--logo-color-one);
  color: var(--color-white) !important;

  &:hover {
    background-color: var(--logo-color-one) !important;
  }
}

.hours-ui-wrapper {
  padding-top: 1.4rem;

  tr {
    border: 0.1rem solid var(--color-white);
    display: flex;
    flex-grow: 1;
  }

  thead {
    display: flex;
    flex-grow: 1;

    th {
      flex-grow: 1;
      position: sticky;
      width: 13%;
      top: 0;
      text-align: center;
      border-bottom: 0.2rem solid var(--color-white) !important;
      background-color: var(--list-group-item-hover);
      line-height: 1.43;
      padding: 0.8rem;
    }
  }

  tbody {
    display: flex;
    flex-direction: column;
    cursor: default;

    td {
      flex-grow: 1;
      width: 13%;
      font-size: 1.2rem;
      vertical-align: middle;
      border-bottom: 0.2rem solid var(--color-white) !important;
      background-color: var(--color-white);
      text-align: center;
      background-color: var(--color-bg-fa);
      padding: 0.8rem;
    }
  }
}

.text-muted {
  color: var(--color-black-shade-two) !important;
}

.blank {
  width: 9% !important;
  text-align: start !important;
}

.time,
.am-pm {
  border: transparent;
  background: transparent;
  padding: 0 0.2rem 0 0;
}

.time-input-box {
  width: 4rem;
  height: 1.9rem;
  border-radius: 0.3rem;
  outline: none;
  padding: 0.1rem 0.3rem;
  border: 0.1rem solid rgba(68, 138, 255, 0.75);
  margin-right: 0.3rem;

  &:focus {
    border-color: rgba(68, 138, 255, 0.75);
    outline: 0;
  }
}

.table {
  td {
    border-top: none;
  }
}
