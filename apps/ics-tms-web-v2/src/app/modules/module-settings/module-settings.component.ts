import {
  AfterViewInit,
  Component,
  inject,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { filter } from 'rxjs';
import { COMPANY_NAME } from './constants/appConstants';
import { getCompanySettingsMap } from './utils/transform-data';
import { COMPANY_SETTINGS } from 'src/app/constants/appConstants';
import { ListItem } from 'src/app/constants/settingsSideItems';
import { AuthService } from 'src/app/services/auth.service';
import { setHeaderName } from 'src/app/store/actions/globalStore.actions';

@Component({
  selector: 'app-module-settings',
  templateUrl: './module-settings.component.html',
  styleUrls: ['./module-settings.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class ModuleSettingsComponent implements OnInit, AfterViewInit {
  selectedItem!: string;

  items!: ListItem[];

  router: Router = inject(Router);

  store: Store = inject(Store);

  ngOnInit(): void {
    this.items = getCompanySettingsMap();
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.updateSelectedItem();
      });
    this.updateSelectedItem();
  }

  ngAfterViewInit(): void {
    this.store.dispatch(setHeaderName({ name: COMPANY_SETTINGS }));
  }

  updateSelectedItem() {
    this.selectedItem = this.getLastSegment(this.router.url)!;
  }

  selectItem(item: ListItem) {
    this.selectedItem = item.name;
  }

  private getLastSegment(url: string) {
    return getCompanySettingsMap().find(item => url.includes(item.urlEndPoint))
      ?.name;
  }

  getRouterLink(navigateLink: string): string {
    return navigateLink.replace(
      COMPANY_NAME,
      AuthService.getCompany()?.name.toLowerCase() || ''
    );
  }
}
