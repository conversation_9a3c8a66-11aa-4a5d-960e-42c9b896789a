.alarm-site-selection-container {
  .alarm-site-selection-accordion {
    .accordion-item {
      border: none;
      .accordion-button {
        width: auto;
        &:focus,
        &:focus-visible {
          outline: 0 !important;
          box-shadow: none;
        }
        &:not(.collapsed) {
          background-color: transparent;
          box-shadow: none;
          .icon {
            transform: rotate(90deg);
          }
        }
      }
      .accordion-body {
        ul {
          overflow: auto;
          max-width: 400px;
          max-height: 250px;
          padding-left: 0;
          list-style: none;
        }
      }
    }
  }
  .sites-loading-icon {
    color: var(--md-indigo-550);
    margin: 0.5rem 1rem;
  }
  .alarm-multi-site-select-list {
    display: block;
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--md-red-grey-600);
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    background-color: #fafafa;
    .alarm-multi-site-select-header {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      margin-bottom: 1rem;
      .alarm-select-all-filter {
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
      input {
        &::placeholder {
          color: var(--md-grey-700);
          opacity: 1;
        }
      }
    }
    .alarm-multi-site-select-body {
      .alarm-multi-site-list {
        display: block;
        width: 100%;
        max-width: 100%;
        height: 370px;
        margin: 0 0 0 0;
        background-color: #f5f5f5;
        position: relative;
        border-radius: 3px;
        overflow: auto;
        padding: 0;
        box-shadow:
          0 1px 3px 0 var(--md-black-20),
          0 1px 1px 0 var(--md-black-14),
          0 2px 1px -1px var(--md-black-12);
        .alarm-multi-site-select-item {
          display: flex;
          align-items: center;
          gap: 1rem;
          justify-content: flex-start;
          word-break: break-word;
          hyphens: auto;
          overflow-wrap: break-word;
          padding: 8px 12px 8px 12px;
          background-color: #fff;
          font-weight: 400;
          margin: 0;
          cursor: pointer;
          border-bottom: 1px solid var(--md-red-grey-600);
          input {
            height: auto;
            cursor: pointer;
          }
          &:hover {
            background-color: var(--md-blue-grey-100);
          }
          &.selected {
            background-color: var(--md-light-blue-40);
          }
        }
        .no-border {
          border-bottom: none;
        }
      }
      .pagination-controls {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: 1rem;
        gap: 1rem;
        .pagination-button {
          font-weight: bold;
          margin-left: 5px;
          transition: all ease-in 0.1s;
          color: #212121;
          border: none;
          border-radius: 3px;
          background-color: transparent;
          &:disabled {
            cursor: not-allowed;
            color: #bdbdbd;
            background-color: transparent !important;
          }
        }
      }
      .alarm-multi-sites-loader {
        position: relative;
        height: 370px !important;
        border-radius: 3px;
        background-color: #eee;
        display: flex;
        padding: 20px 0;
        text-align: center;
        align-items: center;
        justify-content: center;
        .no-data-text {
          font-weight: bold;
          text-align: center;
          color: #757575;
        }
      }
    }
  }
}
