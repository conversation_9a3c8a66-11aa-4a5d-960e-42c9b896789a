.edit-site-groups-container {
  background-color: var(--white);
  box-shadow:
    0 0.1rem 0.3rem 0 var(--md-black-20),
    0 0.1rem 0.1rem 0 var(--md-black-14),
    0 0.2rem 0.1rem -0.1rem var(--md-black-12);
  margin-bottom: 2.5rem;
  border-radius: 0.3rem;

  .heading-container-site-groups {
    padding: 1.5rem;
    border-bottom: 0.1rem solid var(--md-red-grey-600);

    h1 {
      margin: 0;
      font-size: 2rem;
      font-weight: 400;
    }
  }

  .body-container-site-groups {
    padding: 1.5rem;
    font-size: 1.4rem;

    .name-area-sg {
      display: flex;
      flex-direction: column;
      border-bottom: 0.1rem solid var(--md-red-grey-600);

      input {
        margin-bottom: 0.5rem;
      }

      p {
        font-weight: 700;
        margin-bottom: 0.8rem;
      }

      span {
        margin-bottom: 1.5rem;
      }
    }

    .main-container-site-groups {
      margin-top: 1.6rem;

      p {
        font-size: 1.2rem;
        font-weight: 700;
        color: var(--label-unknown);
        margin-bottom: 0.8rem;
      }

      label {
        font-weight: 600;
        font-size: 1.4rem;
        margin-bottom: 0.8rem;
      }
    }

    .site-groups-container-blocks {
      margin-top: 1.3rem;
      display: flex;
      justify-content: space-between;
      gap: 1.6rem;

      .available-sites-container {
        width: 50%;
        border-radius: 0.3rem;
        box-shadow:
          0 0.1rem 0.3rem 0 var(--md-black-20),
          0 0.1rem 0.1rem 0 var(--md-black-14),
          0 0.2rem 0.1rem -0.1rem var(--md-black-12);
        word-break: break-word;
        background-color: var(--color-bg-fa);

        .panel-heading-available-sites {
          padding: 1rem 1.5rem;
          display: flex;
          justify-content: space-between;
          border-bottom: 0.1rem solid var(--md-red-grey-600);

          .panel-heading-sg {
            span {
              font-weight: 500;
              font-size: 1.6rem;
            }
          }

          .panel-heading-card-sg {
            transition: all ease-in 0.1s;
            font-size: 1.2rem;
            line-height: 1.5;
            padding: 0.1rem 0.8rem;
            border-radius: 0.3rem;
            border: 0.1rem solid var(--md-red-grey-800);
            background-color: var(--md-red-grey-100);
            touch-action: manipulation;
            font-weight: 500;

            &:hover {
              border-color: var(--md-red-grey-800);
              background-color: var(--md-red-grey-200);
              cursor: pointer;
            }

            &:disabled {
              pointer-events: all;
              cursor: not-allowed;
              opacity: 0.5;

              &:hover {
                border: 0.1rem solid var(--md-red-grey-800);
                background-color: var(--md-red-grey-100);
              }
            }
          }
        }

        .panel-data-site-groups {
          height: 41.5rem;
          .no-site-found {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;

            .head {
              font-weight: 500;
            }

            .text {
              color: var(--md-grey-700);
            }
          }

          .panel-contents-container {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            .div-container-list-sg {
              padding: 1rem 1.5rem;
              display: flex;
              flex-direction: column;
              cursor: pointer;
              border-bottom: 0.1rem solid var(--md-red-grey-600);
              background-color: var(--white);

              &:hover {
                background-color: var(--list-group-item-selected);
                cursor: pointer;
              }

              .bold-text-panel-sg {
                font-weight: 700;
                font-size: 1.4rem;
                cursor: pointer;
              }

              .small-text-panel-sg {
                font-size: 1.2rem;
                color: var(--md-grey-700);
                font-weight: 400;
                cursor: pointer;
              }
            }
            .virtual-scroll-viewport {
              height: 41.5rem;
              width: 100%;
            }

            .tooltip {
              .tooltip-arrow {
                display: none;
              }

              .tooltip-inner {
                max-width: max-content;
                border-radius: 0;
                transform: translate(0.5rem, -2rem);
                font-weight: 400;
                padding: 0.3rem 0.6rem;
              }
            }
          }
        }
        .sites-updating {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
        }
      }
    }

    .collapsable-card-site-groups {
      margin-top: 1.5rem;

      .myCustomBtnSG {
        border: none;
        padding: 0;
        color: var(--color-email-not-verified);
        font-weight: 500;
        font-size: 1.4rem;

        &:hover {
          color: var(--color-email-not-verified-hover);
        }

        i {
          margin-right: 0.8rem;
        }

        transition: all ease-in 0.2s;

        .rotate-90-degree {
          transition: all ease-in 0.2s;
          transform: rotate(90deg);
        }
      }

      .advanced-collapse-body-sg {
        .h4-advance-body-sg {
          margin: 1.6rem 0;
          font-size: 1.8rem;
          font-weight: 500;
        }

        .p-advance-body-sg {
          margin-bottom: 1.6rem;
          font-size: 1.4rem;
        }

        .company-select-input-sg {
          width: 60%;
        }
      }
    }

    .button-container-site-groups-footer {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-top: 2rem;
      padding-top: 2rem;
      border-top: 1px solid var(--md-red-grey-600);
      .delete-site-group {
        margin-left: auto;
      }
    }
  }
}

.confirm-delete-site-group-modal {
  .modal-dialog {
    transform: translate(0, -5rem) !important;
    .modal-content {
      box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.5) !important;
      border-radius: 0.2rem !important;
      width: 40rem;
    }
  }
}
