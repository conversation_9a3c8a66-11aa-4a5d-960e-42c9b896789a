import { inject, Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, exhaustMap, map, of } from 'rxjs';
import * as DeviceTypesActions from '../actions/device-types.actions';
import { DeviceTypesService } from 'src/app/services/device-types.service';

@Injectable()
export class DeviceTypesEffects {
  actions$ = inject(Actions);

  deviceTypesService = inject(DeviceTypesService);

  loadDeviceTypes$ = createEffect(() =>
    this.actions$.pipe(
      ofType(DeviceTypesActions.loadDeviceTypes),
      exhaustMap(action =>
        this.deviceTypesService.getDeviceTypes(action?.serviceRecipientId).pipe(
          map(deviceTypes =>
            DeviceTypesActions.loadDeviceTypesSuccess({
              deviceTypes,
            })
          ),
          catchError(error =>
            of(
              DeviceTypesActions.loadDeviceTypesFailure({
                error: error.message,
              })
            )
          )
        )
      )
    )
  );
}
