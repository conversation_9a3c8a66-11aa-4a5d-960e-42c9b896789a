import { createAction, props } from '@ngrx/store';
import { DeviceType } from 'src/app/models/common';

export const loadDeviceTypes = createAction(
  '[Device Types] Load Device Types',
  props<{ serviceRecipientId?: string }>()
);

export const loadDeviceTypesSuccess = createAction(
  '[Device Types] Load Device Types Success',
  props<{ deviceTypes: DeviceType[] }>()
);

export const loadDeviceTypesFailure = createAction(
  '[Device Types] Load Device Types Failure',
  props<{ error: string }>()
);
