import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';

import { authGuard, noAuthGuard } from './auth.guard';
import { EmptyComponent } from './components/empty-component/empty.component';
import { ForgetPasswordComponent } from './components/forget-password/forget-password.component';
import { ResetEmailSentComponent } from './components/forget-password/reset-email-sent/reset-email-sent.component';
import { LicensesComponent } from './components/licenses/licenses.component';
import { LoginComponent } from './components/login/login.component';
import { PrivacyPolicyComponent } from './components/privacy-policy/privacy-policy.component';
import { ReleaseNotesComponent } from './components/release-notes/release-notes.component';
import { ResetPasswordComponent } from './components/reset-password/reset-password.component';
import { TermsAndConditionsComponent } from './components/terms-and-conditions/terms-and-conditions.component';
import { TwoFactorComponent } from './components/two-factor/two-factor.component';

const routes: Routes = [
  // #region Login
  {
    path: '',
    component: LoginComponent,
    canActivate: [noAuthGuard],
    data: { css: 'view-is-full' },
  },
  {
    path: 'legal/licenses',
    component: LicensesComponent,
    canActivate: [authGuard],
  },
  {
    path: 'release-notes',
    component: ReleaseNotesComponent,
    canActivate: [authGuard],
  },
  {
    path: 'sessions/two-factor',
    component: TwoFactorComponent,
    canActivate: [noAuthGuard],
    data: { css: 'view-is-full' },
  },
  {
    path: 'signup',
    component: ResetPasswordComponent,
    data: { css: 'view-is-full' },
  },
  {
    path: 'resetpassword',
    component: ResetPasswordComponent,
    data: { css: 'view-is-full' },
  },
  {
    path: 'resend-password',
    component: ForgetPasswordComponent,
    data: { css: 'view-is-full' },
  },
  {
    path: 'resend-password/reset-email-sent',
    component: ResetEmailSentComponent,
    data: { css: 'view-is-full' },
  },
  {
    path: 'legal/privacy',
    component: PrivacyPolicyComponent,
    data: { css: 'view-is-full' },
  },
  {
    path: 'legal/terms',
    component: TermsAndConditionsComponent,
    data: { css: 'view-is-full' },
  },
  // #endregion

  // #region Sites
  {
    path: 'sites',
    loadChildren: () =>
      import('./modules/module-site/module-site.module').then(
        m => m.ModuleSiteModule
      ),
    canActivate: [authGuard],
  },
  // #endregion

  // #region Empty components
  {
    path: 'dashboard',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: { css: 'view-is-full' },
  },
  {
    path: 'sites/:id',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: { css: 'view-is-full' },
  },
  {
    path: 'sites/:id/:deviceId/overview',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: { css: 'view-is-full' },
  },
  {
    path: 'secure-reports',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: { css: 'view-is-full' },
  },
  {
    path: 'asset-management',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: { css: 'view-is-full' },
    children: [
      {
        path: '**',
        component: EmptyComponent,
      },
    ],
  },
  {
    path: 'fuel-price-management',
    component: EmptyComponent,
    canActivate: [authGuard],
    data: { css: 'view-is-full' },
    children: [
      {
        path: '**',
        component: EmptyComponent,
      },
    ],
  },
  // #endregion

  // #region Remote
  {
    path: 'remote',
    loadChildren: () =>
      import('./modules/modules-remote/modules-remote.module').then(
        m => m.ModulesRemoteModule
      ),
    canActivate: [authGuard],
  },
  // #endregion

  // #region Settings
  {
    path: 'settings',
    loadChildren: () =>
      import(
        './modules/module-account-settings/module-account-settings.module'
      ).then(m => m.ModuleAccountSettingsModule),
    canActivate: [authGuard],
  },

  // #region Settings
  {
    path: ':company-name/settings',
    loadChildren: () =>
      import('./modules/module-settings/module-settings.module').then(
        m => m.ModuleSettingsModule
      ),
    canActivate: [authGuard],
  },
  // #endregion

  // #region Notifications
  {
    path: 'notifications',
    loadChildren: () =>
      import('./modules/module-notifications/module-notifications.module').then(
        m => m.ModuleNotificationsModule
      ),
    canActivate: [authGuard],
  },
  // #endregion

  // #region RKI
  {
    path: 'rki',
    loadChildren: () =>
      import('./modules/module-rki/module-rki.module').then(
        m => m.ModuleRkiModule
      ),
    canActivate: [authGuard],
  },
  // #endregion

  // #region Media
  {
    path: 'media',
    loadChildren: () =>
      import('./modules/module-media/module-media.module').then(
        m => m.ModuleMediaModule
      ),
    canActivate: [authGuard],
  },
  // #endregion
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      preloadingStrategy: PreloadAllModules,
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
