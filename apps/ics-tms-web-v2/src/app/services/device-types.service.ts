import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { getApiConstants } from '../constants/api';
import { DeviceType } from '../models/common';

@Injectable({
  providedIn: 'root',
})
export class DeviceTypesService {
  httpClient = inject(HttpClient);

  getDeviceTypes(serviceRecipientId?: string): Observable<DeviceType[]> {
    const url = `${getApiConstants().app.getDeviceTypes}`;

    if (serviceRecipientId) {
      const params = new HttpParams().set(
        'serviceRecipientId',
        serviceRecipientId
      );
      return this.httpClient.get<DeviceType[]>(url, { params });
    }
    return this.httpClient.get<DeviceType[]>(url);
  }
}
