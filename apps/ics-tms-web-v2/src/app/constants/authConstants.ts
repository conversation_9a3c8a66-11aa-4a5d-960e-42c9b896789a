import { FeatureFlags } from './appConstants';

export const PERMISSIONS: { [key: string]: string[] } = {
  COMPANY_ADMIN: [
    'WRITE_ROLLOUT',
    'GET_ROLLOUT',
    'GET_FILES',
    'WRITE_FILES',
    'DELETE_ROLLOUT',
    'GET_DEVICES',
    'WRITE_DEVICES',
    'RESET_AUTHENTICATION',
    'WRITE_SITES',
    'GET_SITES',
    'WRITE_ALARMS',
    'WRITE_USERS',
    'VIEW_COMPANY_SETTINGS',
    'WRITE_MEDIA_SETTINGS',
    'WRITE_SITE_GROUPS',
    'WRITE_PEOPLE',
    'WRITE_TEAMS',
    'WRITE_BULK_REBOOT',
    'WRITE_BULK_RECOMMISSION',
    'GET_BULK_OPERATIONS',
    'VIEW_MEDIA_SETTINGS',
  ],
  ANALYST: [
    'WRITE_ROLLOUT',
    'GET_ROLLOUT',
    'GET_FILES',
    'DOWNLOAD_FILES',
    'DELETE_ROLLOUT',
    'GET_DEVICES',
    'WRITE_DEVICES',
    'RESET_AUTHENTICATION',
    'WRITE_SITES',
    'GET_SITES',
    'WRITE_ALARMS',
    'VIEW_COMPANY_SETTINGS',
    'WRITE_BULK_REBOOT',
    'WRITE_BULK_RECOMMISSION',
    'GET_BULK_OPERATIONS',
    'VIEW_MEDIA_SETTINGS',
  ],
  POWER_USER: [
    'WRITE_ROLLOUT',
    'GET_ROLLOUT',
    'GET_FILES',
    'WRITE_FILES',
    'DELETE_ROLLOUT',
    'GET_DEVICES',
    'WRITE_DEVICES',
    'RESET_AUTHENTICATION',
    'WRITE_SITES',
    'GET_SITES',
    'GET_USERS',
    'WRITE_BULK_REBOOT',
    'WRITE_BULK_RECOMMISSION',
    'GET_BULK_OPERATIONS',
    'VIEW_MEDIA_SETTINGS',
    'VIEW_COMPANY_SETTINGS',
  ],
  SPECIALIST: [
    'GET_SITES',
    'GET_DEVICES',
    'WRITE_DEVICES',
    'RESET_AUTHENTICATION',
    'GET_FILES',
    'GET_ROLLOUT',
    'WRITE_ROLLOUT',
    'DELETE_ROLLOUT',
    'GET_BULK_OPERATIONS',
    'VIEW_MEDIA_SETTINGS',
    'VIEW_COMPANY_SETTINGS',
  ],
  USER: [
    'GET_ROLLOUT',
    'GET_FILES',
    'GET_DEVICES',
    'PULL_FILES',
    'GET_USERS',
    'GET_BULK_OPERATIONS',
    'VIEW_MEDIA_SETTINGS',
    'VIEW_COMPANY_SETTINGS',
  ],
  MEDIA_DESIGNER: [
    'WRITE_MEDIA',
    'GET_MEDIA',
    'VIEW_COMPANY_SETTINGS',
    'VIEW_MEDIA_SETTINGS',
  ],
  MEDIA_DEPLOYER: [
    'GET_MEDIA',
    'WRITE_MEDIA',
    'DELETE_MEDIA',
    'VIEW_COMPANY_SETTINGS',
    'VIEW_MEDIA_SETTINGS',
  ],
  MEDIA_APPROVER: ['GET_MEDIA', 'VIEW_COMPANY_SETTINGS', 'VIEW_MEDIA_SETTINGS'],
  PLAYLIST_VIEW: ['GET_MEDIA', 'VIEW_COMPANY_SETTINGS', 'VIEW_MEDIA_SETTINGS'],
  PLAYLIST_MANAGE: [
    'GET_MEDIA',
    'WRITE_MEDIA',
    'DELETE_MEDIA',
    'VIEW_COMPANY_SETTINGS',
    'VIEW_MEDIA_SETTINGS',
  ],
  PLAYLIST_DEPLOY: [
    'GET_MEDIA',
    'WRITE_MEDIA',
    'DELETE_MEDIA',
    'DEPLOY_MEDIA',
    'VIEW_COMPANY_SETTINGS',
    'VIEW_MEDIA_SETTINGS',
  ],
  RKI: ['GET_RKI'],
  CONFIG_MGMT_PUBLISH: ['VIEW_CONFIG_MGMT_MENU'],
  CONFIG_MGMT_ASSIGN: ['VIEW_CONFIG_MGMT_MENU'],
  CONFIG_MGMT_DEPLOY: ['VIEW_CONFIG_MGMT_MENU'],
  FUEL_PRICE_MGMT_VIEW: ['VIEW_FUEL_PRICE_MGMT_MENU'],
  FUEL_PRICE_MGMT_EDIT: ['EDIT_FUEL_PRICE_MGMT_MENU'],
  CONFIG_MGMT_VIEW: ['CONFIG_MGMT_VIEW'],
  REPORTS_VIEW: ['REPORTS_VIEW'],
  RKI_VIEW: ['RKI_VIEW'],
  SETTINGS_VIEW: ['SETTINGS_VIEW'],
  MEDIA_VIEW: ['MEDIA_VIEW'],
  BULK_OPERATIONS_VIEW: ['BULK_OPERATIONS_VIEW'],
  REMOTE_MGMT_VIEW: ['REMOTE_MGMT_VIEW'],
};

export const FEATURE_FLAGS_ARRAY = [
  {
    key: 'mediaMgmt',
    active: false,
  },
  {
    key: 'reporting',
    active: false,
  },
  {
    key: 'provisionDevices',
    active: false,
  },
  {
    key: 'tamperClear',
    active: false,
  },
  {
    key: 'offlineJobs',
    active: false,
  },
  {
    key: 'offlineRKI',
    active: false,
  },
  {
    key: 'devicesSwapOut',
    active: false,
  },
  {
    key: 'playlistMgmt',
    active: false,
  },
  {
    key: 'factoryReset',
    active: false,
  },
  {
    key: 'configMgmt',
    active: false,
  },
  {
    key: 'siteDetailV2',
    active: false,
  },
  {
    key: 'siteAttributes',
    active: false,
  },
  {
    key: 'shellSupport',
    active: false,
  },
  {
    key: 'USE_KEYCLOAK',
    active: false,
  },
  {
    key: 'deviceMerchantReset',
    active: false,
  },
  {
    key: 'ENABLE_FUEL_PRICE_MULTISITES',
    active: false,
  },
  {
    key: 'STAGED_SOFTWARE_ROLLOUT',
    active: false,
  },
  {
    key: 'BLUEFIN',
    active: false,
  },
  {
    key: FeatureFlags.FILE_DOWNLOAD_APPROVAL_REQUIRED,
    active: false,
  },
];

export const WRITE_MEDIA = 'WRITE_MEDIA';
export const MEDIA_DESIGNER = 'MEDIA_DESIGNER';
