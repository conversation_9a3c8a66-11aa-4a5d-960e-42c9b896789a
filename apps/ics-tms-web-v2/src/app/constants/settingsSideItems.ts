import { ResetPasswordComponent } from '../components/reset-password/reset-password.component';
import { AccountProfileComponent } from '../modules/module-account-settings/components/account-profile/account-profile.component';

export interface ListItem {
  name: string;
  state: string;
  urlEndPoint: string;
  isAllowed?: boolean;
  component?: unknown;
}

export const ACCOUNT_SETTINGS_MAP: ListItem[] = [
  {
    name: 'Profile',
    state: '/settings/account',
    urlEndPoint: 'account',
    component: AccountProfileComponent,
  },
  {
    name: 'Password',
    state: '/settings/reset-password',
    urlEndPoint: 'reset-password',
    component: ResetPasswordComponent,
  },
];
