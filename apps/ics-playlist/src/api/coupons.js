/* eslint-disable import/no-cycle */
import request from './index';

export const getCoupons = payload =>
  request()
    .get(
      payload
        ? `/coupon-containers/?orderBy=${payload?.orderBy}&pageIndex=${payload?.pageIndex}`
        : '/coupon-containers/',
      { payload }
    )
    .then(resp => resp.data);

export const createTextCoupon = payload =>
  request()
    .post('/coupon-containers/', payload)
    .then(resp => resp.data);

export const editTextCoupon = params => {
  request()
    .put(`/coupons/${params.couponId}`, params.payload)
    .then(resp => resp.data);
};

export const createGraphicCoupon = payload =>
  request()
    .post('/coupon-containers/', payload)
    .then(resp => resp.data);

export const deleteCoupon = params =>
  request().delete(`/coupon-containers/${params.couponId}`);
export const editGraphicCoupon = params => {
  request()
    .put(`/coupons/${params.couponId}`, params.payload)
    .then(resp => resp.data);
};
