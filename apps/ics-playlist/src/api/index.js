/* eslint-disable import/no-cycle */
/* eslint-disable no-underscore-dangle */

import axios from 'axios';

import { store } from '../index';
import { actions as viewActions } from '../actions/view';
import { actions as authActions } from '../actions/auth';
import errors from './errors';

export const getToken = () => localStorage.getItem('token');
export const setToken = value => localStorage.setItem('token', value);
export const clearStorage = () => localStorage.clear();
export const clearCookies = () =>
  document.cookie.split(';').forEach(c => {
    document.cookie = c
      .replace(/^ +/, '')
      .replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);
  });

export default () => {
  const instance = axios.create({
    baseURL: `${window.__ENV__.api}/v1`, // eslint-disable-line no-underscore-dangle,
  });

  instance.interceptors.request.use(config => {
    config.headers.common.Authorization = `Bearer ${getToken()}`; // eslint-disable-line no-param-reassign
    return config;
  });

  instance.interceptors.response.use(
    response => {
      if (
        response.headers['refresh-token'] &&
        !localStorage.getItem('enableKeycloak')
      ) {
        localStorage.setItem('token', response.headers['refresh-token']);
      }
      return response;
    },
    error => {
      if (error.response.status === 401) {
        store.dispatch(authActions.authExpired());
      }
      throw error;
    }
  );

  instance.interceptors.response.use(undefined, error => {
    const {
      response: { data },
    } = error;
    const errorMessage = errors[data.message] || data.message;
    store.dispatch(
      viewActions.showToast({
        message: errorMessage || 'Something went wrong',
        show: true,
      })
    );
    throw error;
  });

  return instance;
};
