/* eslint-disable import/no-cycle */
import request from './index';

export function getPresignedImageUrl(params) {
  const { mediaType } = params;
  const mapImageType = {
    product: 'text_product_image',
    logo: 'text_logo_image',
    coupon: 'graphical_coupon_image',
    devicedisplayimage: 'device_display_image',
  };

  return request()
    .post('coupons/media', { ...params, mediaType: mapImageType[mediaType] })
    .then(resp => resp.data);
}
