import React, { FC, useCallback, useContext, useEffect, useState } from 'react';
import {
  Box,
  Card,
  CircularProgress,
  DialogTitle,
  IconButton,
  Typography,
} from '@mui/material';
import TextSnippetIcon from '@mui/icons-material/TextSnippet';
import CloseIcon from '@mui/icons-material/Close';
import { useNavigate } from 'react-router';
import { useSnackbar } from 'notistack';
import { AxiosError } from 'axios';
import isEqual from 'lodash/isEqual';
import uniqWith from 'lodash/uniqWith';
import isEmpty from 'lodash/isEmpty';
import PaymentTabCard from '../../pages/SiteDetailTabs/PaymentsTab/PaymentCard';
import { useMergeState } from '../../hooks/useMergeStates';
import { State } from '../../pages/SiteDetailTabs/PaymentsTab';
import {
  ERROR_LABELS,
  MULTIPLE_ERROR_LABELS,
  PAYMENT_DASHBOARD_PAGE_SIZE,
} from '../../constants/app';
import { PendingTransferContext } from '../../pages/PendingTransferContext';
import { useGetApplicationDetails, useGetSite } from '../../services/use-query';
import { NOT_FOUND_PAGE } from '../../constants/routes';
import {
  ApplicationError,
  ErrorType,
  MultipleApplicationError,
  PaymentDashboardResponse,
  PaymentTabCardType,
} from '../../constants/types';
import ErrorTabCard from './ErrorTabCard';

interface ApplicationDialogProps {
  siteId: string;
  device: { deviceId: number; deviceName: string };
  closeDialog: () => void;
  applicationFilter?: Array<string>;
  breakpoints: { xs: number; sm: number; md: number };
}

const ApplicationDialog: FC<ApplicationDialogProps> = ({
  siteId,
  device,
  applicationFilter = [],
  closeDialog,
  breakpoints,
}) => {
  const { enqueueSnackbar } = useSnackbar();
  const [errors, setErrors] = useState<ErrorType[]>([]);
  const navigate = useNavigate();
  const [
    {
      paymentDashboardDataSet,
      pageIndex,
      pageSize,
      totalResults,
      numberOfApplications,
    },
    setStates,
  ] = useMergeState<State>({
    paymentDashboardDataSet: [],
    totalResults: undefined,
    numberOfApplications: undefined,
    numberOfShowedApplications: undefined,
    appNameFilter: [],
    deviceNameFilter: [],
    pageIndex: 0,
    pageSize: PAYMENT_DASHBOARD_PAGE_SIZE,
    hasMore: true,
  });

  const {
    isMismatchedConfigLoading,
    hasConfigMismatchFetchUserRole,
    mismatchedConfigRefetch,
  } = useContext(PendingTransferContext);

  const { data: siteDetailData, error: siteDetailFetchError } =
    useGetSite(siteId);

  const { data: errorsData, refetch: refetchErrors } = useGetApplicationDetails(
    siteId,
    pageIndex,
    pageSize,
    true, // to fetch with errors
    device.deviceId
  );

  const {
    data: labelsData,
    isLoading,
    error: paymentDashboardFetchError,
    refetch: refetchLabels,
  } = useGetApplicationDetails(
    siteId,
    pageIndex,
    pageSize,
    false, // to fetch all labels
    device.deviceId
  );

  const removeErrorsFromPaymentDashboardData = (data: PaymentTabCardType[]) => {
    const allCollectedErrors: ErrorType[] = [];

    // separating the xpos related payment dashboard data as all the error codes are associated with xpos now
    const xposItems = data.filter(item => item.appName === 'infx-xpos');

    xposItems.forEach((item: PaymentTabCardType) => {
      const { paymentDashboard } = item;
      if (!paymentDashboard) return;

      const allErrorsByCode: Record<string, MultipleApplicationError> = {}; // creating an object to basically store all the error code based properties like status, errorCode etc
      const allErrorCodes = new Set<String>(); // storing the error codes here

      // remove error entry from paymentDashboard - so that the error doesn't appear on the applcation dashboard
      // eslint-disable-next-line no-param-reassign
      item.paymentDashboard = paymentDashboard.filter(entry => {
        const isNewSchemaErrorlabel = /^\d{4,}(-[A-Z])? /.test(entry.label);
        const isOldSchemaErrorLabel = ERROR_LABELS.includes(entry.label);
        return isOldSchemaErrorLabel && !isNewSchemaErrorlabel;
      });

      // to collect the error codes together first
      paymentDashboard.forEach(entry => {
        if (
          MULTIPLE_ERROR_LABELS.some(label => entry.label.includes(label)) &&
          entry.label.includes('ErrorCode') &&
          entry.value
        ) {
          allErrorCodes.add(entry.value.toString().trim());
        }
      });

      // brute force approach - group related entries under the correct error code
      paymentDashboard.forEach(entry => {
        const baseLabel = entry.label.split(' ')[0].trim();
        if (allErrorCodes.has(baseLabel)) {
          if (!allErrorsByCode[baseLabel]) {
            allErrorsByCode[baseLabel] = {};
          }
          allErrorsByCode[baseLabel][entry.label] = entry;
        }
      });

      // add the errors with the errorcodes,errordescription and the timestamp (needed to show the first one at the top)
      Object.entries(allErrorsByCode).forEach(([errorCode, errorDetails]) => {
        const errorDescriptionEntry = Object.values(errorDetails).find(detail =>
          detail.label.toLowerCase().includes('error description')
        );
        const statusEntry = Object.values(errorDetails).find(detail =>
          detail.label.toLowerCase().includes('status')
        );

        if (statusEntry?.value === true) {
          allCollectedErrors.push({
            errorCode,
            errorDescription: errorDescriptionEntry?.value?.trim() || '',
            timeStamp: errorDescriptionEntry?.timestamp,
          });
        }
      });

      setErrors(allCollectedErrors);
    });

    // below logic to support the old dotted string schema
    data.forEach((item: PaymentTabCardType) => {
      const { paymentDashboard } = item;

      const applicationError = {} as ApplicationError;
      // eslint-disable-next-line no-param-reassign
      item.paymentDashboard = paymentDashboard.filter(entry => {
        if (ERROR_LABELS.includes(entry.label)) {
          applicationError[entry.label] = entry;
          return false;
        }
        return true;
      });

      const {
        ErrorCode: errorCodeEntry,
        Resolved: resolvedEntry,
        'Error Description': errorDescriptionEntry,
      } = applicationError;

      if (errorCodeEntry) {
        const errorCode = errorCodeEntry.value
          .replace('Error Code:', '')
          .trim();
        const errorDescription = errorDescriptionEntry?.value
          .replace('Error Description:', '')
          .trim();
        const timeStamp = errorDescriptionEntry?.timestamp;

        const isResolved =
          resolvedEntry &&
          new Date(errorCodeEntry.timestamp) <=
            new Date(resolvedEntry.timestamp) &&
          (resolvedEntry.value.toLowerCase().includes('transacting') ||
            resolvedEntry.value.includes(errorCode));

        if (!isResolved) {
          setErrors(prevErrors => [
            ...prevErrors,
            {
              errorCode,
              errorDescription,
              timeStamp,
            },
          ]);
        }
      }
    });
  };

  useEffect(() => {
    const pdError = paymentDashboardFetchError as AxiosError;
    const sdError = siteDetailFetchError as AxiosError;
    if (pdError && pdError.response?.status) {
      enqueueSnackbar(pdError.message, { variant: 'error' });
    }

    if (sdError && sdError.response?.status) {
      if (sdError.response?.status === 404) {
        navigate(NOT_FOUND_PAGE);
        return;
      }
      enqueueSnackbar(pdError.message, { variant: 'error' });
    }
  }, [paymentDashboardFetchError, siteDetailFetchError]);

  // use this to set the errors in the dialog
  useEffect(() => {
    if (errorsData && siteDetailData) {
      const { results } = errorsData as PaymentDashboardResponse;

      const mergedData = uniqWith([...results], isEqual);
      if (!isEmpty(mergedData)) {
        // set errors only
        removeErrorsFromPaymentDashboardData(mergedData);
        return;
      }
      setErrors([]);
    }
  }, [errorsData, siteDetailData, setStates]);

  // use this to set the labels in the dialog
  useEffect(() => {
    if (labelsData && siteDetailData) {
      const { resultsMetadata, results } =
        labelsData as PaymentDashboardResponse;

      const mergedData = uniqWith([...results], isEqual);
      if (!isEmpty(mergedData)) {
        setStates({
          paymentDashboardDataSet: mergedData,
          totalResults: resultsMetadata.totalResults,
          numberOfApplications: resultsMetadata.numberOfApplications,
          numberOfShowedApplications: new Set(
            mergedData.map(dashboard => dashboard.appName)
          ).size,
          pageIndex: resultsMetadata.pageIndex,
          hasMore:
            mergedData.length < resultsMetadata.totalResults &&
            resultsMetadata.pageIndex <
              Math.ceil(resultsMetadata.totalResults / pageSize),
        });
        return;
      }
      setStates({
        paymentDashboardDataSet: [],
        totalResults,
        numberOfApplications,
        numberOfShowedApplications: 0,
        pageIndex,
        hasMore: 0,
      });
    }
  }, [labelsData, siteDetailData, setStates]);

  useEffect(() => {
    if (!isMismatchedConfigLoading && hasConfigMismatchFetchUserRole) {
      mismatchedConfigRefetch();
    }
  }, []);

  useEffect(() => {
    refetchLabels();
    refetchErrors();
  }, [device.deviceId]);

  const getFilteredPaymentDashboardDataSet = useCallback(() => {
    if (applicationFilter.length === 0) {
      return paymentDashboardDataSet;
    }
    return paymentDashboardDataSet.filter(dataset =>
      applicationFilter.includes(dataset.appName)
    );
  }, [paymentDashboardDataSet, applicationFilter]);

  /* eslint-disable no-nested-ternary */
  return (
    <>
      <DialogTitle
        id='device-dialog-title'
        sx={{
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          paddingRight: '48px',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <IconButton
            sx={{
              boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.1)',
              borderRadius: '8px',
            }}
          >
            <TextSnippetIcon />
          </IconButton>
          <Typography variant='h6' component='div' sx={{ ml: 1, mt: -1 }}>
            Applications and Device details
          </Typography>
        </Box>
        <Typography variant='body2' sx={{ ml: 6, mt: -2 }}>
          {device.deviceName}
        </Typography>
        <IconButton
          onClick={() => closeDialog()}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      {isLoading ? (
        <CircularProgress sx={{ margin: 'auto' }} />
      ) : !isEmpty(getFilteredPaymentDashboardDataSet()) ? (
        <>
          {!isEmpty(errors) && (
            <ErrorTabCard errors={errors} isLoading={isLoading} />
          )}
          <Card
            sx={{
              display: 'flex',
              flexDirection: 'column',
              p: 2,
              gap: 1,
              overflow: 'auto',
              pr: 1,
              maxHeight: errors.length !== 0 ? '60%' : '100%',
            }}
          >
            {getFilteredPaymentDashboardDataSet().map(
              ({
                deviceId,
                deviceName,
                appName,
                deviceHealth,
                paymentDashboard,
              }) => (
                <PaymentTabCard
                  key={deviceId}
                  deviceId={deviceId}
                  deviceName={deviceName}
                  deviceHealth={deviceHealth}
                  appName={appName}
                  paymentDashboard={paymentDashboard}
                  breakpoints={breakpoints}
                />
              )
            )}
          </Card>
        </>
      ) : (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            textAlign: 'center',
            padding: 2,
            borderRadius: 1,
            backgroundColor: '#f0f0f0',
            marginTop: 4,
          }}
        >
          <Typography variant='h6' gutterBottom>
            No Data Available
          </Typography>
        </Box>
      )}
    </>
  );
};

export default ApplicationDialog;
