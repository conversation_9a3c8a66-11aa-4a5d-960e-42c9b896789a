// RejectionDialog.tsx
import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Divider,
  Box,
} from '@mui/material';
import { enqueueSnackbar } from 'notistack';

import { usePutApprovalReject } from '../services/use-query';

interface RejectionDialogProps {
  rejectDialogOpen: boolean;
  // TO-DO : Remove any
  siteAttributes: any;
  handleRejectCancel: () => void;
  handleRejectConfirm: (delay: number) => void;
}

const RejectionDialog: React.FC<RejectionDialogProps> = ({
  rejectDialogOpen,
  siteAttributes,
  handleRejectCancel,
  handleRejectConfirm,
}) => {
  const [reason, setReason] = React.useState<string>('');
  const { mutate: putApprovalRejectMutate } = usePutApprovalReject();

  const handleCancel = () => {
    setReason('');
    handleRejectCancel();
  };

  const handleReject = () => {
    // TO-DO : Remove any
    const payload = siteAttributes.map((attribute: any) => ({
      id: Number(attribute.entityApprovalSettingId),
      status: 'rejected',
      comment: reason.trim(),
      additionalData: {},
    }));

    putApprovalRejectMutate(payload, {
      onSuccess: () => {
        enqueueSnackbar(
          'The request for modifying site attributes request has been rejected. Submitter will be notified of its status.',
          { variant: 'success' }
        );
        setReason('');
        handleRejectConfirm(500);
      },
      onError: (error: any) => {
        enqueueSnackbar(
          `Rejection failed: ${error.message || 'Unknown error'}`,
          { variant: 'error' }
        );
      },
    });
  };

  return (
    <Dialog
      open={rejectDialogOpen}
      onClose={handleRejectCancel}
      maxWidth='sm'
      fullWidth
    >
      <Box sx={{ backgroundColor: '#E1E1EC' }}>
        <DialogTitle sx={{ fontSize: '1.5rem', fontWeight: 'bold' }}>
          Please leave a reason for this rejection
        </DialogTitle>

        <Divider />

        <DialogContent>
          <TextField
            label='Reason for Rejection'
            type='text'
            fullWidth
            multiline
            minRows={3}
            value={reason}
            onChange={e => setReason(e.target.value)}
            variant='outlined'
            InputLabelProps={{ shrink: true }}
          />
        </DialogContent>

        <Divider />

        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>
          <Button
            onClick={handleCancel}
            sx={{
              margin: 0,
              color: '#3f51b5',
              textTransform: 'none',
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleReject}
            variant='contained'
            sx={{
              margin: 0,
              textTransform: 'none',
              borderRadius: '12px',
              boxShadow: 3,
              backgroundColor: '#3f51b5',

              '&:hover': {
                backgroundColor: '#303f9f',
              },
            }}
          >
            Submit Rejection
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
};

export default RejectionDialog;
