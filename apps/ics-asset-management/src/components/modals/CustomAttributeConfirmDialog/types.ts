import {
  IUpdateCustomAttributePayloadValue,
  IUpdateCustomAttributeDeploymentType,
} from '../../../constants/types';
import { Entities } from '../../../constants/entities';

export enum CustomAttributeConfirmDialogStep {
  confirmation,
  confirmationImmediate,
  confirmationMaintainance,
  scheduleWindow,
}

export enum CustomAttributeConfirmDialogType {
  UPDATE = 'UPDATE',
  SUBMIT_FOR_APPROVAL = 'SUBMIT_FOR_APPROVAL',
  APPROVE = 'APPROVE',
}

export type CustomAttributeConfirmDialogProps = {
  attributes: IUpdateCustomAttributePayloadValue[];
  canSubmit: boolean;
  defaultDeploymentType?: IUpdateCustomAttributeDeploymentType;
  entityType: Entities;
  isLoading: boolean;
  onSubmit: (delay: number) => void;
  siteIds: string[];
  isBulkUpdate: boolean;
  // TO-DO : Remove any
  dailogType?: CustomAttributeConfirmDialogType;
  // TO-DO : Remove any
  attributesToApprove?: any[];
  showMfaPrompt?: boolean;
};

export type CustomAttributeConfirmDialogState = {
  isDialogOpen: boolean;
  isMutating: boolean;
  hasMutationError: boolean;
  step: CustomAttributeConfirmDialogStep;
};

export type TimeAndDateFormatInScheduleWindowType = (
  from: string | Date | undefined,
  to: string | Date | undefined
) => string;
