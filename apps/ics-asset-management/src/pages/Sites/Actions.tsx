import React, { memo } from 'react';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import LoadingButton from '@mui/lab/LoadingButton';
import { useNavigate } from 'react-router-dom';
import PopupState, { bindTrigger, bindMenu } from 'material-ui-popup-state';
import useHasPermissions from '../../hooks/useHasPermissions';
import FeatureFlags from '../../constants/featureFlags';
import UserRoles from '../../constants/userRoles';
import Copy from './constants/copy';
import useFilterParams from './hooks/useFilterParams';
import useSitesPageCSV from './hooks/useSitesPageCSV';

const Actions = () => {
  const navigate = useNavigate();
  const { valuesFromParams } = useFilterParams();

  const hasSiteAttributeImportAccess = useHasPermissions({
    userRoles: [UserRoles.CONFIG_MGMT_DEPLOY],
    companyFeatureFlags: [
      FeatureFlags.SITE_ATTRIBUTES,
      FeatureFlags.SITE_DETAIL_V2,
      FeatureFlags.SHELL_SUPPORT,
    ],
  });

  const canApproveSiteAttributes = useHasPermissions({
    userRoles: [
      UserRoles.COMPANY_ADMIN,
      UserRoles.POWER_USER,
      UserRoles.SITE_CA_APPROVER,
    ],
    companyFeatureFlags: [],
    partialRoleCheck: true,
  });

  const { isFetchingSitesCSV, handleDownloadCSV } =
    useSitesPageCSV(valuesFromParams);

  return (
    <PopupState variant='popover' popupId='sites-actions'>
      {popupState => (
        <>
          <LoadingButton
            {...bindTrigger(popupState)}
            endIcon={<ArrowDropDownIcon />}
            loading={isFetchingSitesCSV}
            variant='outlined'
          >
            Actions
          </LoadingButton>
          <Menu {...bindMenu(popupState)}>
            <MenuItem
              onClick={() => {
                popupState.close();
                navigate('/sites/add');
              }}
            >
              {Copy.addSiteButtonLabel}
            </MenuItem>
            {hasSiteAttributeImportAccess && canApproveSiteAttributes && (
              <MenuItem
                onClick={() => {
                  popupState.close();
                  navigate('attributes/import-attributes');
                }}
              >
                {Copy.importAttributesButtonLabel}
              </MenuItem>
            )}
            {hasSiteAttributeImportAccess && canApproveSiteAttributes && (
              <MenuItem
                onClick={() => {
                  popupState.close();
                  navigate('attributes/bulk-edit');
                }}
              >
                {Copy.bulkEditAttributesButtonLabel}
              </MenuItem>
            )}
            <MenuItem disabled={isFetchingSitesCSV} onClick={handleDownloadCSV}>
              {Copy.downloadCSVButtonLabel}
            </MenuItem>
          </Menu>
        </>
      )}
    </PopupState>
  );
};

export default memo(Actions, () => true);
