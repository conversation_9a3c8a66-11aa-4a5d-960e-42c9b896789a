import React, { FC, useEffect, useMemo, useState } from 'react';
import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';
import Typography from '@mui/material/Typography';
import {
  FilterAlt as FilterIcon,
  SortByAlpha as SortIcon,
} from '@mui/icons-material';
import CircularProgress from '@mui/material/CircularProgress';
import InfoIcon from '@mui/icons-material/Info';
import { Badge, IconButton, Popover, Tooltip } from '@mui/material';
import { bindPopover, bindTrigger } from 'material-ui-popup-state';
import { usePopupState } from 'material-ui-popup-state/hooks';
import {
  Application,
  ApplicationDevice,
  ISelectedApplication,
  TabProps,
} from '../../../constants/types';
import { APPLICATIONS_AND_PAYMENTS_DASHBOARD_ALERT } from '../../../constants/app';

import { useGetApplicationDashboard } from '../../../services/use-query';
import NoApplicationsFound from '../../../components/NoApplicationsFound';
import { APPLICATION_SYSTEM } from '../PaymentsTab/constants';
import { stylesDoughnutChart } from './styles';
import { DeviceList } from './DeviceList';
import Actions from './FiltersAndActions/Actions';
import { ApplicationsAndPaymentsTabContext } from './context';
import { ApplicationDeviceTable } from './ApplicationDeviceTable';
import { ApplicationStatusWidget } from './ApplicationStatusWidget';
import { FilterPopover } from './FiltersAndActions/Filters';
import DevicveSortForm, {
  InstanceSortOrder,
  InstanceSortOrderBy,
  InstanceSortFormData,
} from './FiltersAndActions/Sort';

const ApplicationsAndPaymentsTab: FC<TabProps> = ({ siteId }) => {
  const classesDoughnutChart = stylesDoughnutChart();

  // context API
  const [selectedApplications, setSelectedApplications] = useState<
    Array<ISelectedApplication>
  >([]);
  const [filteredDevices, setFilteredDevices] = useState<
    ApplicationDevice[] | null
  >(null);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  const [filteredCounts, setFilteredCounts] = useState(0);
  const [sortOptions, setSortOptions] = useState<InstanceSortFormData>({
    order: InstanceSortOrder.asc,
    orderBy: InstanceSortOrderBy.deviceName,
  });
  const popupState = usePopupState({
    variant: 'popover',
    popupId: 'sortPopup',
  });

  const addRemoveSelectedApplication = (
    application: ISelectedApplication,
    added: boolean
  ) => {
    if (added) {
      setSelectedApplications(previousValue => [...previousValue, application]);
    } else {
      setSelectedApplications(previousValue =>
        previousValue.filter(
          app =>
            !(
              app.appName === application.appName &&
              app.deviceId === application.deviceId
            )
        )
      );
    }
  };

  const contextObject = useMemo(
    () => ({
      selectedApplications,
      addRemoveSelectedApplication,
    }),
    [selectedApplications, addRemoveSelectedApplication]
  );

  // api call(s)
  const { data: applicationDashboardResponse, isLoading } =
    useGetApplicationDashboard(
      { siteId },
      {
        cacheTime: 0,
        enabled: true,
        retry: 0,
        retryOnMount: false,
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        refetchOnReconnect: false,
        keepPreviousData: true,
        staleTime: Infinity,
      }
    );

  const sortDevices = (devices, sortOrder, orderBy) =>
    [...devices].sort((a, b) => {
      const getValue = device => {
        if (orderBy === 'applicationName') {
          // logic to sort the appNames within the device first and then sort it with other devices
          const apps = [...(device.applications ?? [])].filter(
            app => app?.appName
          );
          const sortedApps = apps.sort((appA, appB) => {
            const nameA = appA.appName.toLowerCase();
            const nameB = appB.appName.toLowerCase();
            if (nameA < nameB) return sortOrder === 'asc' ? -1 : 1;
            if (nameA > nameB) return sortOrder === 'asc' ? 1 : -1;
            return 0;
          });
          return sortedApps[0]?.appName.toLowerCase() ?? '';
        }
        if (orderBy === 'status') {
          const isNotRunning =
            !device.applications?.length ||
            device.applications?.some(
              app => app?.status.toLowerCase() !== 'running'
            );
          return isNotRunning ? 'offline' : 'running';
        }
        return `${device[orderBy] ?? ''}`.toLowerCase();
      };
      const aValue = getValue(a);
      const bValue = getValue(b);
      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

  // section - all applications
  const applicationsCount = useMemo(() => {
    let count = 0;
    if (applicationDashboardResponse) {
      const { results } = applicationDashboardResponse;

      results?.forEach(device => {
        device.applications.forEach(({ appName }) => {
          if (appName !== APPLICATION_SYSTEM) {
            count += 1;
          }
        });
      });
    }
    return count;
  }, [applicationDashboardResponse]);

  // status widget's/doughnut chart's meta-data and its state(s)
  const charDataset = useMemo(() => {
    const dataset = [
      { label: 'Active', value: 0 },
      { label: 'Inactive', value: 0 },
    ];

    if (applicationDashboardResponse) {
      const { results } = applicationDashboardResponse;

      results?.forEach(device => {
        const nonSystemApps = device.applications.filter(
          application => application.appName !== APPLICATION_SYSTEM
        );

        const isNotRunning =
          !nonSystemApps?.length ||
          nonSystemApps.some(application => application.status !== 'running');
        isNotRunning ? (dataset[1].value += 1) : (dataset[0].value += 1);
      });
    }

    return dataset;
  }, [applicationDashboardResponse]);

  // application-device table's meta-data and its state(s)
  const [tableHeaders, setTableHeaders] = useState<ApplicationDevice[]>([]);
  const [tableRows, setTableRows] = useState<Application[]>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleOpenFilter = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseFilter = () => {
    setAnchorEl(null);
  };

  useEffect(() => {
    if (applicationDashboardResponse) {
      const { results, applications } = applicationDashboardResponse;
      setTableHeaders(results.length ? results : []);
      setTableRows(applications.length ? applications : []);
    }
  }, [applicationDashboardResponse]);

  // section - device list meta-data and its state(s)
  const [devices, setDevices] = useState<ApplicationDevice[]>([]);

  const handleFormSubmit = (data: InstanceSortFormData) => {
    setSortOptions(data);
  };

  // to handle the device list change - normally and based on the filter as well
  useEffect(() => {
    if (applicationDashboardResponse) {
      const { results = [] } = applicationDashboardResponse;

      if (results.length) {
        // If user interacted, use filteredDevices to filter applicationDashboardResponse
        const baseFilteredResults =
          hasUserInteracted && filteredDevices?.length
            ? results.filter(res =>
                filteredDevices.some(fd => fd.deviceId === res.deviceId)
              )
            : results;

        const cleanedResults = baseFilteredResults.map(device => ({
          ...device,
          applications: device.applications.filter(
            ({ appName }) => appName !== APPLICATION_SYSTEM
          ),
        }));

        setDevices(
          sortDevices(cleanedResults, sortOptions.order, sortOptions.orderBy)
        );
      } else {
        setDevices([]);
      }
    }
  }, [
    applicationDashboardResponse,
    filteredDevices,
    sortOptions,
    hasUserInteracted,
  ]);

  return (
    <ApplicationsAndPaymentsTabContext.Provider value={contextObject}>
      <Box padding='24px' paddingBottom={0}>
        <Alert
          icon={<InfoIcon color='primary' fontSize='small' />}
          severity='info'
          sx={{
            flexShrink: 0,
          }}
        >
          {APPLICATIONS_AND_PAYMENTS_DASHBOARD_ALERT}
        </Alert>
      </Box>

      {applicationsCount <= 0 && (
        <Box
          width='100%'
          height='100%'
          display='flex'
          flexDirection='column'
          alignItems='center'
          justifyContent='center'
          gap='4px'
        >
          {isLoading ? <CircularProgress /> : <NoApplicationsFound />}
        </Box>
      )}

      {/* MAIN LAYOUT */}
      {(!isLoading && applicationsCount) > 0 && (
        <Box
          width='100%'
          height='100%'
          display='flex'
          flexDirection='column'
          gap='24px'
          padding='24px'
        >
          {/* SECTION 1 - APPLICATION STATUS WIDGET & TABLE */}
          <Box display='flex' flexDirection='column' gap='8px'>
            {/* SECTION 1 - HEADER */}
            <Typography
              align='left'
              color='black'
              variant='body1'
              sx={{
                lineHeight: 2.4,
                fontWeight: 700,
              }}
            >
              All Applications ({applicationsCount})
            </Typography>

            {/* SECTION 1 - CONTENT */}
            <Box className={classesDoughnutChart.root} height='240px'>
              {/* SECTION 1.A - STATUS WIDGET/DOUGHNUT CHART */}
              <Box
                width='540px'
                height='240px'
                display='flex'
                flexDirection='column'
                alignItems='center'
                gap='4px'
                border='1px solid #D2C5DA'
                borderRadius='16px'
              >
                <Typography
                  width={1}
                  align='left'
                  color='black'
                  variant='body1'
                  padding='0 12px'
                  sx={{
                    lineHeight: 2.4,
                    fontWeight: 500,
                  }}
                >
                  Application Status
                </Typography>

                <ApplicationStatusWidget dataset={charDataset} />
              </Box>

              {/* SECTION 1.B - TABLE */}
              <ApplicationDeviceTable headers={tableHeaders} rows={tableRows} />
            </Box>
          </Box>

          {/* SECTION 2 - APPLICATION LIST */}
          <Box display='flex' flexDirection='column' gap='8px'>
            <Box
              display='flex'
              justifyContent='space-between'
              alignItems='center'
            >
              {/* SECTION 2 - HEADER */}
              <Typography
                align='left'
                color='black'
                variant='body1'
                sx={{
                  lineHeight: 2.4,
                  fontWeight: 700,
                }}
              >
                Device List ({devices?.length})
              </Typography>

              {/* SECTION 2 - APPLICATION FILTER/ACTIONS/SORT */}
              <Box display='flex' alignItems='center' gap={1}>
                <Badge
                  badgeContent={filteredCounts}
                  color='primary'
                  sx={{
                    '& .MuiBadge-badge': {
                      top: 4,
                      right: 4,
                    },
                  }}
                >
                  <Tooltip arrow title='Filter Options'>
                    <IconButton
                      data-testid='schedule-instance-filter-btn'
                      onClick={handleOpenFilter}
                      sx={{
                        border: '1px solid lightgrey',
                        borderRadius: '50%',
                      }}
                    >
                      <FilterIcon />
                    </IconButton>
                  </Tooltip>
                  <FilterPopover
                    anchorEl={anchorEl}
                    onClose={handleCloseFilter}
                    siteId={siteId}
                    hasUserInteracted={hasUserInteracted}
                    setHasUserInteracted={setHasUserInteracted}
                    filteredCounts={filteredCounts}
                    setFilteredCounts={setFilteredCounts}
                    onFilterChange={(filteredData: ApplicationDevice[]) => {
                      setFilteredDevices(filteredData);
                    }}
                  />
                </Badge>
                <Badge
                  badgeContent={
                    sortOptions.order === InstanceSortOrder.desc ? '↓' : '↑'
                  }
                  color='primary'
                  sx={{
                    '& .MuiBadge-badge': {
                      top: 4,
                      right: 4,
                    },
                  }}
                >
                  <IconButton
                    {...bindTrigger(popupState)}
                    data-testid='config-instance-sort-btn'
                    aria-label='open sort options'
                    sx={{
                      border: '1px solid lightgrey',
                      borderRadius: '50%',
                    }}
                  >
                    <SortIcon />
                  </IconButton>
                  <Popover
                    {...bindPopover(popupState)}
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                    transformOrigin={{ vertical: 'top', horizontal: 'left' }}
                    onClose={popupState.close}
                  >
                    <DevicveSortForm
                      popupState={popupState}
                      defaultValues={sortOptions}
                      resetValues={{
                        order: InstanceSortOrder.asc,
                        orderBy: InstanceSortOrderBy.deviceName,
                      }}
                      onFormSubmit={handleFormSubmit}
                    />
                  </Popover>
                </Badge>
                <Actions siteId={siteId} />
              </Box>
            </Box>

            {/* SECTION 2 - CONTENT - LIST OF DEVICE(S) */}
            <DeviceList siteId={siteId} devices={devices} />
          </Box>
        </Box>
      )}
    </ApplicationsAndPaymentsTabContext.Provider>
  );
};

export default ApplicationsAndPaymentsTab;
