import { ReactNode } from 'react';
import {
  ICustomAttributeDefinition,
  ICustomSiteAttributeSetWithStaged,
} from '../../../constants/types';

interface Dictionary<T> {
  [index: string]: T;
}

interface CustomAttributeFormValue {
  attributeDefinitionId: number;
  value: unknown;
  isValid: boolean;
}

interface SingleSiteEditorFormState {
  isLoading?: boolean;
  isSubmitting?: boolean;
  errorMessage?: string;
  attributeDefinitions?: ICustomAttributeDefinition[];
  formInput: CustomAttributeFormValue[];
  formId: string;
  hasCMDeployPermission: boolean;
  isSiteVisible: boolean;
}

interface SingleSiteEditorFormProps {
  groupedSiteAttributes: Dictionary<ICustomSiteAttributeSetWithStaged[]>;
  siteId: string;
  updatedAt: string;
  isAttributesLoading: boolean;
  refetchSiteAttributes: (delay: number) => void;
  renderKey: string;
  children?: ReactNode;
}

export {
  CustomAttributeFormValue,
  SingleSiteEditorFormState,
  SingleSiteEditorFormProps,
};
