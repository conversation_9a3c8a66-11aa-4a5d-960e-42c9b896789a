import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  CircularProgress,
  Typography,
} from '@mui/material';
import groupBy from 'lodash/groupBy';
import orderBy from 'lodash/orderBy';
import head from 'lodash/head';
import uniqWith from 'lodash/uniqWith';
import isEqual from 'lodash/isEqual';
import React, {
  FC,
  Suspense,
  useCallback,
  useContext,
  useEffect,
  useMemo,
} from 'react';
import { AxiosError } from 'axios';
import DescriptionIcon from '@mui/icons-material/Description';
import InfoIcon from '@mui/icons-material/Info';
import CancelIcon from '@mui/icons-material/Cancel';
import {
  ICustomSiteAttributeSet,
  ICustomSiteAttributeSetWithStaged,
  IUpdateCustomAttributeDeploymentType,
  TabProps,
} from '../../constants/types';
import {
  useGetCustomAttributesApprovalSettings,
  useGetCustomSiteAttributes,
  useGetPendingForApprovalCustomAttributes,
} from '../../services/use-query';
import { Entities } from '../../constants/entities';
import FETCH_DATA from '../../constants/errors';
import HTTP_STATUS from '../../constants/httpCodes';
import { useMergeState } from '../../hooks/useMergeStates';
import {
  ATTRIBUTE_PAGE_SIZE,
  SITE_ATTRIBUTES_REQUEST_DELAY,
} from '../../constants/app';
import { mismatchAlert as mismatchAlertTheme } from '../../constants/theme';
import { PendingTransferContext } from '../PendingTransferContext';
import { generateUUIDV4, getUserRoles, hasUserRole } from '../../utils/helpers';
import UserRoles from '../../constants/userRoles';
import SiteAttributeChangeConfirmationModal from '../../components/modals/SiteAttributeChangeConfirmationModal';
import { ConfigMismatchContent } from '../../constants/messages';
import FeatureFlags from '../../constants/featureFlags';
import ScheduleDeploymentAlert from '../../components/ScheduleDeploymentAlert';
import useHasPermissions from '../../hooks/useHasPermissions';
import RejectionDialog from '../../components/RejectionDialog';
import CustomAttributeConfirmDialog from '../../components/modals/CustomAttributeConfirmDialog';
import { CustomAttributeConfirmDialogType } from '../../components/modals/CustomAttributeConfirmDialog/types';
import sleep from '../../utils/sleep';
import SingleSiteEditorForm from './AttributesTab/SingleSiteEditorForm';
import PendingSiteTransferAlert from './PendingSiteTransferAlert';

interface Dictionary<T> {
  [index: string]: T;
}

interface IState {
  groupedSiteAttributes: Dictionary<ICustomSiteAttributeSetWithStaged[]>;
  currentPage: number;
  updatedAt: string;
  errorText: string;
  isEmptyAttributes: boolean;
  mergedSiteAttributes: ICustomSiteAttributeSetWithStaged[];
  renderKey: string;
  // approval flow related properties
  siteAttributesRequiringApproval: ICustomSiteAttributeSet[];
  siteAttributesPendingApproval: ICustomSiteAttributeSet[];
  isRejectDialogOpen: boolean;
  isRefetching?: boolean;
}

const AttributesTab: FC<TabProps> = ({ siteId: entityId }) => {
  const entity = Entities.Sites;
  const [
    {
      groupedSiteAttributes,
      currentPage,
      updatedAt,
      errorText,
      isEmptyAttributes,
      mergedSiteAttributes,
      renderKey,
      siteAttributesRequiringApproval,
      siteAttributesPendingApproval,
      isRejectDialogOpen,
      isRefetching,
    },
    setStates,
  ] = useMergeState<IState>({
    groupedSiteAttributes: {},
    currentPage: 1,
    updatedAt: null,
    errorText: null,
    isEmptyAttributes: false,
    mergedSiteAttributes: [],
    renderKey: generateUUIDV4(),
    siteAttributesRequiringApproval: [],
    siteAttributesPendingApproval: [],
    isRejectDialogOpen: false,
    isRefetching: false,
  });

  const {
    data: siteAttributesResponse,
    isError,
    error,
    isFetching: isAttributesLoading,
    refetch: refetchSiteAttributesQuery,
  } = useGetCustomSiteAttributes(entity, entityId, currentPage, null, {
    cacheTime: 0,
    retry: false,
  });

  // fetch custom site attributes approval settings
  // handle error and disable update/submit for approval button
  const { data: saApprovalSettings, refetch: refetchApprovalSettings } =
    useGetCustomAttributesApprovalSettings();

  useEffect(() => {
    if (siteAttributesResponse && saApprovalSettings) {
      // get all enabled settings which has 'customAttributeDefinitionId'
      const currentUserRoles = getUserRoles();
      const settings = saApprovalSettings.filter(setting => {
        const { condition, rolesNotRequireApproval } = setting;
        // if approval setting does not have a customAttributeDefinitionId, skip it
        if (!condition?.customAttributeDefinitionId) {
          return false;
        }
        // if rolesNotRequireApproval is not defined or empty, treat as requiring approval
        if (!rolesNotRequireApproval || rolesNotRequireApproval?.length === 0) {
          return true; // if no rolesNotRequireApproval, treat as requiring approval
        }
        // if rolesNotRequireApproval is defined, check if current user has any of those roles
        return !currentUserRoles.some(role =>
          rolesNotRequireApproval.includes(role)
        );
      });

      // get matching site attributes
      const matchingSiteAttributes = siteAttributesResponse.results.reduce(
        (result, ca) => {
          const match = settings.find(
            setting => setting.condition.customAttributeDefinitionId === ca.id
          );

          if (match) {
            result.push(ca);
          }
          return result;
        },
        []
      );

      setStates({ siteAttributesRequiringApproval: matchingSiteAttributes });
    } else {
      setStates({ siteAttributesRequiringApproval: [] });
    }
  }, [siteAttributesResponse, saApprovalSettings]);

  // fetch custom site attributes approval settings
  // handle error and disable update/submit for approval button
  const {
    data: saPendingForApproval,
    refetch: refetchAttributesPendingForApproval,
  } = useGetPendingForApprovalCustomAttributes(entityId);
  useEffect(() => {
    if (siteAttributesResponse && saPendingForApproval) {
      const pendingCustomAttributes = saPendingForApproval.filter(
        item => item.condition?.customAttributeDefinitionId
      );

      const matchingSiteAttributes = siteAttributesResponse.results.reduce(
        (result, ca) => {
          const match = pendingCustomAttributes.find(
            setting => setting.condition.customAttributeDefinitionId === ca.id
          );

          if (match) {
            result.push({
              ...ca,
              entityApprovalSettingId: match.id,
              proposedValue: match.proposedValue,
            });
          }
          return result;
        },
        []
      );

      setStates({ siteAttributesPendingApproval: matchingSiteAttributes });
    } else {
      setStates({ siteAttributesPendingApproval: [] });
    }
  }, [siteAttributesResponse, saPendingForApproval]);

  const {
    isSiteTransferApprovalModalOpen,
    isSiteTransferLoading,
    isSiteTransferPending,
    isMismatchedConfigLoading,
    hasConfigMismatchFetchUserRole,
    hasUserDeployedConfig,
    mismatchedConfigRefetch,
    mismatchedConfigs,
    setSiteTransferApprovalModalOpen,
  } = useContext(PendingTransferContext);

  const orderAndGroupSiteAttributes = (
    data: ICustomSiteAttributeSetWithStaged[]
  ) => {
    setStates({
      groupedSiteAttributes: groupBy(orderBy(data, ['groupName']), 'groupName'),
    });
  };

  const getUpdatedAt = (data: ICustomSiteAttributeSetWithStaged[]) => {
    const sortedByUpdatedAt = data
      ?.sort()
      .filter(item => item.updatedAt !== null);
    setStates({
      updatedAt: head(orderBy(sortedByUpdatedAt, ['updatedAt'], ['desc']))
        ?.updatedAt,
    });
  };

  const createCustomErrorMessage = (errorParam: AxiosError) => {
    switch (errorParam.response.status) {
      // error messages can be customized
      case HTTP_STATUS.TooManyRequests:
        setStates({ errorText: errorParam.message });
        break;
      case HTTP_STATUS.Internal:
        setStates({ errorText: errorParam.message });
        break;
      case HTTP_STATUS.BadRequest:
        setStates({ errorText: errorParam.message });
        break;
      default:
        break;
    }
  };

  const hasDeployUserRole = hasUserRole(UserRoles.CONFIG_MGMT_DEPLOY);

  useEffect(() => {
    if (!isError) {
      if (siteAttributesResponse) {
        const { resultsMetadata, results } = siteAttributesResponse;

        const unstagedAttributeDefinitions = results.filter(r => !r.isStaged);
        const stagedAttributeDefinitions = results.filter(r => r.isStaged);

        const unstagedAttributeDefinitionsWithStagedFlags =
          unstagedAttributeDefinitions.map(
            (unstaged): ICustomSiteAttributeSetWithStaged => ({
              ...unstaged,
              doesStagedValueExist: stagedAttributeDefinitions.some(
                staged => staged.id === unstaged.id
              ),
            })
          );

        const mergedData = uniqWith(
          [
            ...unstagedAttributeDefinitionsWithStagedFlags,
            ...stagedAttributeDefinitions,
          ],
          isEqual
        );

        // set 'requiresApproval' and 'pendingApproval' flag for each attribute
        const updatedMergedData = mergedData.map(attribute => {
          const requiresApproval = siteAttributesRequiringApproval.find(
            attr => attr.id === attribute.id
          );
          const pendingApproval = siteAttributesPendingApproval.find(
            attr => attr.id === attribute.id
          );

          return {
            ...attribute,
            requiresApproval: !!requiresApproval,
            pendingApproval: siteAttributesPendingApproval.some(
              attr => attr.id === attribute.id
            ),
            entityApprovalSettingId: requiresApproval
              ? requiresApproval.entityApprovalSettingId
              : null,
            ...(pendingApproval?.proposedValue !== undefined &&
              pendingApproval?.proposedValue !== null && {
                value: pendingApproval.proposedValue,
              }),
          };
        });

        const pageCount = Math.ceil(
          resultsMetadata.totalResults / ATTRIBUTE_PAGE_SIZE
        );
        const hasMore =
          updatedMergedData.length < resultsMetadata.totalResults &&
          resultsMetadata.pageIndex < pageCount;

        setStates({
          mergedSiteAttributes: updatedMergedData,
          renderKey: generateUUIDV4(),
        });

        if (updatedMergedData?.length === 0) {
          setStates({ isEmptyAttributes: true });
          setStates({ errorText: FETCH_DATA.EMPTY_ATTRIBUTES });
        }

        if (hasMore) {
          setTimeout(() => {
            setStates({ currentPage: currentPage + 1 });
          }, SITE_ATTRIBUTES_REQUEST_DELAY);
        } else {
          orderAndGroupSiteAttributes(updatedMergedData);
          getUpdatedAt(updatedMergedData);
        }
      }
    } else {
      createCustomErrorMessage(error as AxiosError);
    }
  }, [
    currentPage,
    siteAttributesResponse,
    isError,
    siteAttributesRequiringApproval,
    siteAttributesPendingApproval,
  ]);

  const refetchSiteAttributes = () => {
    const FIRST_PAGE = 1;
    setStates({ currentPage: FIRST_PAGE, mergedSiteAttributes: [] });
    // required when just only one page
    if (currentPage === FIRST_PAGE) {
      refetchSiteAttributesQuery();
    }
    setStates({ renderKey: generateUUIDV4() });
  };

  useEffect(() => {
    if (!isMismatchedConfigLoading && hasConfigMismatchFetchUserRole) {
      mismatchedConfigRefetch();
    }
  }, []);

  const hasSchedulePermission = useHasPermissions({
    userRoles: [UserRoles.CONFIG_MGMT_DEPLOY],
    companyFeatureFlags: [FeatureFlags.SCHEDULING_UCA],
  });

  const hasCaApproverPermission = hasUserRole(UserRoles.SITE_CA_APPROVER);
  const isCompanyAdmin = hasUserRole(UserRoles.COMPANY_ADMIN);
  const isPowerUser = hasUserRole(UserRoles.POWER_USER);

  const showAttributesRequireApprovalAlert = useMemo(
    (): boolean =>
      !isCompanyAdmin &&
      !isPowerUser &&
      !hasCaApproverPermission &&
      siteAttributesRequiringApproval.length > 0,
    [siteAttributesRequiringApproval]
  );

  const showAttributesPendingApprovalAlert = useMemo(
    (): boolean =>
      !isCompanyAdmin &&
      !isPowerUser &&
      !hasCaApproverPermission &&
      siteAttributesPendingApproval.length > 0,
    [siteAttributesPendingApproval]
  );

  const showSiteAttributesApprovalRequestAlert = useMemo(
    (): boolean =>
      (isCompanyAdmin || isPowerUser) &&
      hasCaApproverPermission &&
      siteAttributesPendingApproval.length > 0,
    [siteAttributesPendingApproval]
  );

  const setIsRejectDialogOpen = useCallback(
    (value: boolean): void => setStates({ isRejectDialogOpen: value }),
    []
  );

  const handleOnConfirmDialogSubmit = useCallback(async (delay: number) => {
    setIsRejectDialogOpen(false);

    setStates({ isRefetching: true });
    await sleep(delay);
    setStates({ isRefetching: false });

    refetchApprovalSettings();
    refetchAttributesPendingForApproval();
    refetchSiteAttributes();
  }, []);

  return (
    <Box sx={{ height: '100%', width: '100%' }}>
      {!isError && !isEmptyAttributes && (
        <SingleSiteEditorForm
          groupedSiteAttributes={groupedSiteAttributes}
          isAttributesLoading={isAttributesLoading}
          refetchSiteAttributes={handleOnConfirmDialogSubmit}
          renderKey={renderKey}
          siteId={entityId}
          updatedAt={updatedAt}
        >
          {showAttributesRequireApprovalAlert && (
            <Alert
              icon={<InfoIcon color='primary' fontSize='small' />}
              severity='info'
              sx={{
                flexShrink: 0,
              }}
            >
              Some of the attributes require approval.
            </Alert>
          )}

          {showAttributesPendingApprovalAlert && (
            <Alert
              icon={<InfoIcon color='primary' fontSize='small' />}
              severity='info'
              sx={{
                flexShrink: 0,
              }}
            >
              Some of the attributes are pending for approval.
            </Alert>
          )}

          {showSiteAttributesApprovalRequestAlert && (
            <>
              <Alert
                icon={<InfoIcon color='primary' fontSize='small' />}
                severity='info'
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  flexShrink: 0,
                  color: 'common.headerIcon',
                  bgcolor: 'common.primaryContent',
                }}
                action={
                  <Box
                    display='flex'
                    flexDirection='row'
                    alignItems='center'
                    justifyContent='flex-end'
                    gap={1.5}
                    margin={0}
                  >
                    <Button
                      variant='text'
                      sx={{
                        m: 0,
                        fontSize: '12px',
                        border: '1px solid #00394D',
                      }}
                      endIcon={<CancelIcon fontSize='small' />}
                      onClick={() => setIsRejectDialogOpen(true)}
                      disabled={isRefetching || isAttributesLoading}
                    >
                      Reject
                    </Button>

                    <Suspense fallback={<CircularProgress />}>
                      <CustomAttributeConfirmDialog
                        attributes={[]}
                        canSubmit
                        defaultDeploymentType={
                          IUpdateCustomAttributeDeploymentType.maintenanceWindow
                        }
                        entityType={Entities.Sites}
                        isLoading={isRefetching || isAttributesLoading}
                        onSubmit={handleOnConfirmDialogSubmit}
                        siteIds={[entityId]}
                        isBulkUpdate={false}
                        dailogType={CustomAttributeConfirmDialogType.APPROVE}
                        attributesToApprove={siteAttributesPendingApproval}
                        showMfaPrompt={false}
                      />
                    </Suspense>
                  </Box>
                }
              >
                Custom Site Attributes approval request
              </Alert>

              <RejectionDialog
                rejectDialogOpen={isRejectDialogOpen}
                siteAttributes={siteAttributesPendingApproval}
                handleRejectCancel={() => setIsRejectDialogOpen(false)}
                handleRejectConfirm={handleOnConfirmDialogSubmit}
              />
            </>
          )}

          {!isSiteTransferLoading && isSiteTransferPending && (
            <>
              <PendingSiteTransferAlert
                text='Site Transfer Pending'
                actionEnabled={hasDeployUserRole}
                callback={() => setSiteTransferApprovalModalOpen(true)}
              />
              <SiteAttributeChangeConfirmationModal
                isOpen={isSiteTransferApprovalModalOpen}
                onClose={() => {
                  setSiteTransferApprovalModalOpen(false);
                }}
                siteId={entityId}
                siteAttributes={mergedSiteAttributes}
                refetchSiteAttributes={refetchSiteAttributes}
              />
            </>
          )}
          {hasSchedulePermission && (
            <Box>
              <ScheduleDeploymentAlert
                siteId={entityId}
                sourceType='UpdateSiteCustomAttribute'
              />
            </Box>
          )}
          {hasConfigMismatchFetchUserRole &&
            !!mismatchedConfigs?.resultsMetadata?.totalResults &&
            !hasUserDeployedConfig && (
              <Alert
                severity='info'
                sx={mismatchAlertTheme}
                icon={<DescriptionIcon color='primary' fontSize='small' />}
              >
                {ConfigMismatchContent.mismatchAlertContent}
              </Alert>
            )}
        </SingleSiteEditorForm>
      )}
      {(isError || isEmptyAttributes) && (
        <Box
          sx={{
            p: 3,
            textAlign: 'center',
          }}
        >
          <Typography variant='bodyMedium'>
            {isEmptyAttributes ? errorText : `${errorText}, please retry`}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default AttributesTab;
