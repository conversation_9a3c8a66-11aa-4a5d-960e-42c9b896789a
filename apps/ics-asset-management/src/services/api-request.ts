/* eslint-disable default-param-last */
import axios from 'axios';
import qs from 'qs';
import {
  IPaymentSchemaResponse,
  Devices,
  IPaymentActionJob,
  IPaymentActionsResponse,
  PaymentDashboardResponse,
  Site,
  DevicesSyncKey,
  SiteSummary,
  SiteDeviceIssue,
  SiteIssue,
  KeyGroups,
  ICustomJobPayload,
  Tags,
  NewDevice,
  Device,
  DeviceType,
  AlarmRules,
  ICustomSiteAttributeResponse,
  IAttributeDefinitionResponse,
  IEntityCompanies,
  IEntitySiteTags,
  IEntitySites,
  IMismatchedConfigDevicesResponse,
  FilesForDeploymentResponse,
  FilesForDeploymentDetailsResponse,
  PastImportByCsvHistoryResponse,
  PastImportByCsvHistoryRequest,
  GetJobsResponse,
  GetJobsParameters,
  DottedStringResponse,
  CreateJobPayload,
  UpdateCustomAttributesByEntityTypeRequest,
  PostSiteDetailsRequest,
  EntitySettingsResponse,
  ApplicationDashboardResponseData,
  ValidateSiteTagsRequest,
  ValidateSiteTagsResponse,
  GetMaintenanceWindow,
  MonitorPayload,
  IDownloadConfigFileResponse,
  ConfigFileParams,
} from '../constants/types';
import {
  ATTRIBUTE_DEFINITION_PAGE_SIZE,
  ATTRIBUTE_PAGE_SIZE,
  ENTITIES_RESPONSE_PAGE_SIZE,
} from '../constants/app';
import { Entities } from '../constants/entities';
import {
  ScheduleAlertReq,
  ScheduleAlertResponse,
} from '../components/ScheduleDeploymentAlert/types';
import { GetLastScheduledPayload } from '../components/ScheduleDeploymentModal/types';
import HTTP_STATUS from '../constants/httpCodes';
import { APPLICATION_SYSTEM } from '../pages/SiteDetailTabs/PaymentsTab/constants';
import request from './request';

/**
 * Get a Site
 * @param siteId
 * @returns {Promise<Site>}
 */
export const getSite = (siteId: string, signal): Promise<Site> =>
  request()
    .get(`/sites/${siteId}`, { signal })
    .then(resp => resp.data);

export const getKeyGroups = (serviceRecipientId: string): Promise<KeyGroups> =>
  request()
    .get(`/rki/keygroups?serviceRecipientId=${serviceRecipientId}`)
    .then(resp => resp.data)
    .catch(() => []);

export const postSiteDetails = (
  payload: PostSiteDetailsRequest
): Promise<Site> =>
  request()
    .put(`/sites/${payload.site.id}`, payload)
    .then(resp => resp.data);

export const deleteSite = (payload: string): Promise<Site> =>
  request()
    .delete(`/sites/${payload}`)
    .then(resp => resp.data);

export const postSyncDevices = (payload: DevicesSyncKey): Promise<Devices> =>
  request()
    .post('/devices/sync-keygroup-to-site', payload)
    .then(resp => resp.data);

export const getAllTags = (): Promise<Tags> =>
  request()
    .get('/tags')
    .then(resp => resp.data);

export const getAlarmRules = (siteId: string): Promise<AlarmRules> =>
  request()
    .get(`/sites/${siteId}/devices/alarmrules`)
    .then(resp => resp.data)
    .catch(() => []);

export const postAlarmRules = (data: AlarmRules): Promise<AlarmRules> =>
  request()
    .put('/devices/alarmrules', data)
    .then(resp => resp.data);

/**
 * Get devices in a site
 * @param siteId
 * @returns {Promise<Devices>}
 */
export const getDevices = (
  siteId: string,
  pageIndex: number,
  pageSize: number
): Promise<Devices> =>
  request()
    .get(`/sites/${siteId}/devices?pageIndex=${pageIndex}&pageSize=${pageSize}`)
    .then(resp => resp.data);

/**
 * Get any schedules are present
 * @param data
 * @returns {Promise<ScheduleAlertResponse>}
 */
export const getDeviceScheduled = (
  data: ScheduleAlertReq
): Promise<ScheduleAlertResponse> =>
  request()
    .post(`/schedule-ms/schedules/source-type-scheduled`, data)
    .then(resp => resp.data);

/**
 * Get devices in a site
 * @param siteId
 * @returns {Promise<Devices>}
 */
export const getSiteSummary = (siteId: string): Promise<SiteSummary> =>
  request()
    .get(`/sites/${siteId}`)
    .then(resp => resp.data);

/**
 * Get payment dashboard fora site
 * @param siteId
 * @param pageIndex
 * @param pageSize
 * @param signal
 * @returns {Promise<PaymentDashboardResponse>}
 */
export const getPaymentDashboard = (
  siteId: string,
  pageIndex: number,
  pageSize: number,
  appNameFilter?: string,
  deviceNameFilter?: string,
  signal?: AbortSignal
): Promise<PaymentDashboardResponse> => {
  const params = { pageIndex, pageSize, appNameFilter, deviceNameFilter };
  return request()
    .get(`/payments/dashboard/${siteId}`, {
      params,
      paramsSerializer: parameters =>
        qs.stringify(parameters, {
          arrayFormat: 'brackets',
          encode: false,
        }),
      signal,
    })
    .then(resp => resp.data);
};

export const getApplicationDetails = (
  siteId: string,
  pageIndex: number,
  pageSize: number,
  applicationDashboard: boolean,
  deviceId: string,
  signal?: AbortSignal
): Promise<PaymentDashboardResponse> => {
  const params = { pageIndex, pageSize, applicationDashboard, deviceId };
  // remove once api is fixed, right api returns same data for both cases
  if (!applicationDashboard) {
    delete params.applicationDashboard;
  }

  return request()
    .get(`/payments/dashboard/${siteId}`, {
      params,
      paramsSerializer: parameters =>
        qs.stringify(parameters, {
          arrayFormat: 'brackets',
          encode: false,
        }),
      signal,
    })
    .then(resp => resp.data);
};

export const postDeviceMonitoring = (
  payload: MonitorPayload
): Promise<MonitorPayload> => {
  const { deviceId, monitoringModeOn } = payload;
  return request()
    .post(`/monitoring/${deviceId}/create`, { monitoringModeOn })
    .then(resp => resp.data);
};

export const postNewDevice = (newDevice: NewDevice): Promise<Device> =>
  request()
    .post('/devices', newDevice)
    .then(resp => resp.data);

export const getDeviceTypes = (): Promise<DeviceType[]> =>
  request()
    .get('/devicetypes')
    .then(resp => resp.data);

export const getSiteIssues = (
  siteId: string,
  siteStatus: string,
  signal: AbortSignal
): Promise<SiteIssue[]> =>
  request()
    .get(`/sites/${siteId}/alarms`, { signal })
    .then(result => result.data);

export const getSiteDevicesIssues = (
  siteId: string,
  signal: AbortSignal
): Promise<SiteDeviceIssue[]> =>
  request()
    .get(`/sites/${siteId}/devices/alarms`, { signal })
    .then(result => result.data);

export const getDevicesInSite = (siteId: string): Promise<Devices> =>
  request()
    .get(`/sites/${siteId}/devices`)
    .then(result => result.data);

/**
 * Get payment actions
 * @param deviceId
 * @param appName
 * @param signal
 * @returns {Promise<Array<IPaymentActionsResponse>>}
 */
export const getPaymentActions = (
  deviceId: number,
  appName: string,
  signal?: AbortSignal
): Promise<Array<IPaymentActionsResponse>> =>
  request()
    .get(`/payments/actions/${deviceId}/${appName}`, { signal })
    .then(resp => resp.data);

/**
 * Get payment actions schema
 * @param schemaId
 * @param signal
 * @returns {Promise<IPaymentSchemaResponse>}
 */
export const getPaymentSchema = (
  schemaId: number,
  signal?: AbortSignal
): Promise<IPaymentSchemaResponse> =>
  request()
    .get(`/payments/schemas/${schemaId}`, { signal })
    .then(resp => resp.data);

/**
 * Post payment action job
 * @param deviceFileId
 * @param jobData
 * @param signal
 * @returns {Promise<IPaymentActionJob>}
 */
export const postPaymentActionJob = (
  deviceFileId: number,
  jobData: object,
  signal?: AbortSignal
): Promise<IPaymentActionJob> =>
  request()
    .post(`/payments/actions/${deviceFileId}/job`, jobData, { signal })
    .then(resp => resp.data);

/**
 * Post payment action custom job
 * @param deviceFileId
 * @param payload
 * @param signal
 * @returns {Promise<any>}
 */
export const postPaymentActionCustomJob = (
  deviceFileId: number,
  payload: ICustomJobPayload,
  signal?: AbortSignal
): Promise<IPaymentActionJob> =>
  request()
    .post(`/payments/actions/${deviceFileId}/job/custom`, payload, { signal })
    .then(resp => resp.data);

/**
 * Fetch file
 * @param fileURL
 * @returns {Promise<string | object>} fileData
 */
export const getFileData = (
  fileURL: string,
  signal?: AbortSignal
): Promise<string | object> =>
  axios.get(fileURL, { signal }).then(res => res.data);

/**
 * Get custom site attributes
 * Workaround: pageIndex
 * @param entity
 * @param entityId
 * @param page
 * @param isStaged
 * @param size
 * @returns {Promise<ICustomSiteAttributeResponse>}
 */
export const getCustomSiteAttributes = (
  entity: string,
  entityId: string,
  page: number,
  isStaged: boolean | null = null,
  signal?: AbortSignal
): Promise<ICustomSiteAttributeResponse> =>
  request()
    .get(`/custom-attributes/${entity}/${entityId}`, {
      params: { pageSize: ATTRIBUTE_PAGE_SIZE, pageIndex: page - 1, isStaged },
      signal,
    })
    .then(resp => resp.data);

export const getCheckRKI = ({
  devicesId,
  keyGroupRef,
}: {
  devicesId: number[];
  keyGroupRef: string | null;
}) =>
  request()
    .get('/devices/check-rki', { params: { keyGroupRef, devices: devicesId } })
    .then(res => res.data);
/**
 * Get site attributes definitions
 * @param entity
 * @param page
 * @param size
 * @returns {Promise<IAttributeDefinitionResponse>}
 */
export const getSiteAttributesDefinitions = (
  entity: string,
  page: number,
  signal?: AbortSignal
): Promise<IAttributeDefinitionResponse> =>
  request()
    .get(`/custom-attributes/definitions/${entity}`, {
      params: { pageSize: ATTRIBUTE_DEFINITION_PAGE_SIZE, pageIndex: page - 1 },
      signal,
    })
    .then(resp => resp.data);

/**
 * GET entity tenants or companies
 * @param page
 * @param fields
 * @param sort
 * @param signal
 * @returns {Promise<IEntityCompanies>}
 */
export const getEntityCompanies = (
  page: number,
  fields: string[],
  sort: string[],
  signal?: AbortSignal
): Promise<IEntityCompanies> =>
  request()
    .get('/entities/companies', {
      params: {
        pageSize: ENTITIES_RESPONSE_PAGE_SIZE,
        pageIndex: page - 1,
        fields,
        sort,
      },
      signal,
    })
    .then(resp => resp.data);

/**
 * GET entity site tags
 * @param page
 * @param fields
 * @param sort
 * @param signal
 * @returns {Promise<IEntitySiteTags>}
 */
export const getEntitySiteTags = (
  page: number,
  fields: string[],
  sort: string[],
  filters: object = {},
  pageSize: number = ENTITIES_RESPONSE_PAGE_SIZE,
  signal?: AbortSignal
): Promise<IEntitySiteTags> =>
  request()
    .get('/entities/site-tags', {
      params: {
        pageSize,
        pageIndex: page - 1,
        fields,
        sort,
        filters,
      },
      signal,
    })
    .then(resp => resp.data);

/**
 * GET entity sites
 * @param page
 * @param fields
 * @param sort
 * @param allSites
 * @param signal
 * @returns {Promise<IEntitySites>}
 */
export const getEntitySites = (
  page: number,
  fields: string[] = [],
  sort: string[] = [],
  filters: object = {},
  pageSize: number = ENTITIES_RESPONSE_PAGE_SIZE,
  allSites: boolean = true,
  signal?: AbortSignal
): Promise<IEntitySites> =>
  request()
    .get('/entities/sites', {
      params: {
        pageSize,
        pageIndex: page - 1,
        fields,
        sort,
        filters,
        allSites,
      },
      signal,
    })
    .then(resp => resp.data);

export const updateCustomAttributesByEntityType = (
  { entityType, payload }: UpdateCustomAttributesByEntityTypeRequest,
  signal?: AbortSignal
) =>
  request()
    .put(`/custom-attributes/${entityType}/bulk`, { ...payload, signal })
    .then(resp => resp.data);

export const getSiteAttributeIsStaged = (
  entity: string,
  entityId: string,
  signal?: AbortSignal
): Promise<boolean> =>
  request()
    .get<boolean>(`/custom-attributes/${entity}/${entityId}/is-staged`, {
      signal,
    })
    .then(resp => resp.data);

export const approveIsStagedCustomSiteAttributes = (
  entity: Entities,
  entityId: string,
  signal?: AbortSignal
) =>
  request()
    .put(`/custom-attributes/${entity}/${entityId}/is-staged/apply`, { signal })
    .then(resp => resp.data);

export const getSiteAttributeIsDuplicateSitename = (
  entity: string,
  entityId: string,
  signal?: AbortSignal
): Promise<any> =>
  request()
    .get(
      `/custom-attributes/${entity}/${entityId}/is-staged/is-duplicate-sitename`,
      {
        signal,
      }
    )
    .then(resp => resp.data);

export const importSiteAttributes = (formData, signal?: AbortSignal) =>
  request()
    .put('/custom-attributes/import', formData, { signal })
    .then(resp => resp.data);

/**
 * GET asset report
 * @param siteId
 * @param signal
 * @returns {Observable<HttpResponse<Blob>>}
 */
export const getAssetReport = (siteId: string, signal?: AbortSignal) =>
  request()
    .get(`/reports/assets-report/${siteId}`, {
      responseType: 'arraybuffer',
      signal,
    })
    .then(response => response);

/** Fetch config mismatched by siteId
 * @param {
 *   siteId,
 *   onlyMeta
 * }
 * @param {AbortSignal} signal
 * @returns {Promise<IMismatchedConfigDevicesResponse>}
 */
export const getMismatchedConfigDevices = (
  {
    siteId,
    onlyMeta = true,
    deploymentType,
  }: {
    siteId: string;
    onlyMeta?: boolean;
    deploymentType?: string;
  },
  signal: AbortSignal
): Promise<IMismatchedConfigDevicesResponse> => {
  const params = {
    filters: {
      isDeploying: {
        $eq: false,
      },
      isNewVersionAvailable: {
        $eq: true,
      },
      siteId: {
        $in: siteId,
      },
      deploymentType: {
        $eq: deploymentType,
      },
    },
    onlyMeta,
  };

  if (!deploymentType) {
    delete params.filters.deploymentType;
  }

  return request()
    .get('/renditions/config-files', {
      params,
      paramsSerializer: parameters =>
        qs.stringify(parameters, {
          arrayFormat: 'brackets',
          encode: false,
        }),
      signal,
    })
    .then(resp => resp.data);
};

/** Fetch custom attributes by siteId
 * @param {
 *   siteId
 * }
 * @param {AbortSignal} signal
 * @returns {Promise<IMismatchedConfigDevicesResponse>}
 */
export const getCustomAttributesbySiteId = async (
  {
    siteId,
    onlyMeta = false,
  }: {
    siteId: string;
    onlyMeta?: boolean;
  },
  signal: AbortSignal
): Promise<IMismatchedConfigDevicesResponse> => {
  try {
    const response = await request().get<IMismatchedConfigDevicesResponse>(
      '/renditions/devices/config-files',
      {
        params: {
          filters: {
            siteId: { $eq: siteId },
          },
          onlyMeta,
        },
        paramsSerializer: params =>
          qs.stringify(params, {
            arrayFormat: 'brackets',
            encode: false,
          }),
        signal,
      }
    );
    return response.data;
  } catch (error) {
    // Handle error
    console.error(error);
    throw error; // Re-throw the error to handle it in the calling code
  }
};

/**
 * Post deployment by siteId
 * @param {
 *   siteId
 * }
 * @returns {Promise<null>} Currently returns only a 204 status code
 */
export const postMismatchedConfigDeployment = ({
  siteId,
}: {
  siteId: string;
}) =>
  request()
    .post('/config-management/user/instances/deployments', {
      SiteIds: [siteId],
    })
    .then(resp => resp.data);

/**
 * Validate if site tags have conflicting config instances
 * https://jira.gilbarco.com/browse/ICS-12988
 */
export const postValidateSiteTags = ({
  siteId,
  siteTagIds,
}: ValidateSiteTagsRequest): Promise<ValidateSiteTagsResponse> =>
  request()
    .post(`/config-management/user/instances/site-tags/site?validate=true`, {
      siteId,
      siteTagIds,
    })
    .then(resp => resp.data)
    .catch(error => {
      if (
        [HTTP_STATUS.BadRequest, HTTP_STATUS.NotFound].includes(
          error?.response?.status
        )
      ) {
        return error?.response?.data;
      }
      return {
        errors: [],
        message: 'An error occurred while validating site tags.',
      };
    });

/**
 * Get file ids for deployment
 * @param sites,
 * @param {AbortSignal} signal
 * @returns {Promise<FilesForDeploymentResponse>}
 */
export const getFilesForDeployment = (
  sites: string[],
  signal: AbortSignal
): Promise<FilesForDeploymentResponse> => {
  const params = {
    filters: {
      isDeploying: {
        $eq: false,
      },
      isNewVersionAvailable: {
        $eq: true,
      },
      siteId: {
        $in: sites,
      },
    },
    pageSize: 100,
  };

  return request()
    .get('/renditions/config-files', {
      params,
      paramsSerializer: parameters =>
        qs.stringify(parameters, {
          arrayFormat: 'brackets',
          encode: false,
        }),
      signal,
    })
    .then(resp => resp.data);
};

/**
 * Get file names for deployment
 * @param configFileIds,
 * @param {AbortSignal} signal
 * @returns {Promise<FilesForDeploymentDetailsResponse>}
 */
export const getFilesForDeploymentDetails = (
  configFileIds: string[],
  signal: AbortSignal
): Promise<FilesForDeploymentDetailsResponse> => {
  const params = {
    configFileIds,
    pageNumber: 1,
    pageSize: 100,
  };
  return request()
    .get('/config-management/user/config/config-files', {
      params,
      paramsSerializer: parameters =>
        qs.stringify(parameters, {
          arrayFormat: 'repeat',
          encode: false,
        }),
      signal,
    })
    .then(resp => resp.data);
};

export const immediateConfigFilesDeployment = (siteId: string) =>
  request()
    .put(`/renditions/devices/config-files/${siteId}`, {
      deploymentType: 'immediate',
    })
    .then(resp => resp.data);

/**
 * Create ICS job
 * @param payload
 * @returns {Promise<void>}
 */
export const createJob = async (payload: CreateJobPayload) => {
  await request().post(`/jobs`, payload);
};

export const saveSetting = async params => {
  await request().put(
    `/entities/entity/settings/sites/${params.siteId}`,
    params.payload
  );
};

export const getSetting = async params => {
  await request().get(`/entities/entity/settings/sites/${params.siteId}`);
};

/**
 * Get Pending FPS devices
 * @param siteId
 * @param dottedString
 * @param {AbortSignal} signal
 * @returns {Promise<DottedStringResponse>}
 */
export const getDottedStringData = ({
  signal,
  siteId,
  dottedString,
}: {
  signal: AbortSignal;
  siteId?: string;
  dottedString?: string;
}): Promise<DottedStringResponse> =>
  request()
    .get(
      `/entities/sites/${siteId}/dotted-strings?filters[dottedString][$eq]=${dottedString}`,
      { signal }
    )
    .then(resp => resp.data);

export const getApplicationDashboard = ({
  signal,
  siteId,
}: {
  signal: AbortSignal;
  siteId?: string;
}): Promise<ApplicationDashboardResponseData> =>
  request()
    .get(`/entities/applications/dashboard/${siteId}`, { signal })
    .then(resp => {
      const filteredSystemData = resp.data.applications.filter(
        ({ appName }) => appName !== APPLICATION_SYSTEM
      );
      return { ...resp.data, applications: filteredSystemData };
    });

export const getSettingsDefinitions = ({
  signal,
}: {
  signal: AbortSignal;
}): Promise<EntitySettingsResponse> =>
  request()
    .get(`/entities/entity/definitions/sites/feature/fps`, { signal })
    .then(resp => resp.data);

export const getFpsSettings = ({
  signal,
  siteId,
}: {
  signal: AbortSignal;
  siteId: string;
}): Promise<EntitySettingsResponse> =>
  request()
    .get(
      `/entities/entity/settings/sites/${siteId}?filters[featureName][$eq]=fps&filters[settingName][$eq]=FuelPriceThreshold`,
      { signal }
    )
    .then(resp => resp.data);

/**
 * Get Jobs
 * @param status
 * @param destination
 * @param siteId
 * @param jobTypes
 * @param {AbortSignal} signal
 * @returns {Promise<GetJobsResponse[]>}
 */
export const getJobs = ({
  signal,
  status,
  destination,
  siteId,
  jobTypes,
}: GetJobsParameters): Promise<GetJobsResponse[]> => {
  const params = {
    status: status.join(','),
    destination,
    siteId,
    types: jobTypes.join(','),
  };
  return request()
    .get(`/jobs`, {
      params,
      paramsSerializer: parameters =>
        qs.stringify(parameters, {
          encode: false,
        }),
      signal,
    })
    .then(resp => resp.data);
};

/**
 * Get Jobs
 * @param status
 * @param destination
 * @param siteId
 * @param {AbortSignal} signal
 * @returns {Promise<void>}
 */
export const cancelJob = (signal: AbortSignal, jobId: string): Promise<void> =>
  request()
    .post(`/jobs/${jobId}/cancel`, {
      signal,
    })
    .then(resp => resp.data);

/**
 * Upload a CSV file to import or transfer devices
 *
 * @param {FormData} csvFile
 * @returns {Promise<number>}
 *
 * @todo Get correct endpoint URL, and correct response type from the backend team
 */
export const postImportByCsv = (csvFile: FormData): Promise<number> =>
  request()
    .post('/file-upload/import', csvFile)
    .then(resp => resp.status);

/**
 * Get list of past device imports by CSV
 *
 * @param {{ pageIndex: number }} { pageIndex }
 * @param {AbortSignal} signal
 * @returns {Promise<PastImportByCsvHistoryResponse>}
 *
 * @todo Get correct endpoint URL, and correct response type from the backend team
 */
export const getImportsByCsvHistory = (
  { pageIndex = 0, category, pageSize = 10 }: PastImportByCsvHistoryRequest,
  signal: AbortSignal
): Promise<PastImportByCsvHistoryResponse> =>
  request()
    .get('/file-upload/histories', {
      params: {
        pageIndex,
        filters: {
          category: {
            $eq: category,
          },
        },
        pageSize,
        sort: 'createdDate:desc',
      },
      paramsSerializer: params =>
        qs.stringify(params, {
          arrayFormat: 'brackets',
          encode: false,
        }),
      signal,
    })
    .then(resp => resp.data);

/**
 * Get url to a temporary AWS URL
 * @param {{ fileUploadId: string }} { fileUploadId }
 * @param {AbortSignal} signal
 * @returns <Promise<{url: string}>>
 */
export const getErrorsCsvUrl = (
  { fileUploadId }: { fileUploadId: string },
  signal?: AbortSignal
): Promise<{ url: string }> =>
  request()
    .get(`/file-upload/${fileUploadId}/errors/urls`, {
      signal,
    })
    .then(resp => resp.data);

/**
 * Get a CSV file from a temporary AWS URL
 * @param {{ url: string }} { url }
 * @param {AbortSignal} signal
 * @returns <Promise<Blob>>
 */
export const getErrorsCsv = (
  { url }: { url: string },
  signal?: AbortSignal
): Promise<Blob> =>
  axios
    .get(url, {
      responseType: 'blob',
      signal,
    })
    .then(resp => resp.data);

/**
 * Get last scheduled data
 * @param {params: GetLastScheduledPayload}
 * @returns {Promise<any>}
 */
export const getlastScheduled = (
  params: GetLastScheduledPayload
): Promise<any> =>
  request()
    .get(`/schedules`, { params })
    .then(resp => resp.data);

export const getMaintenanceWindow = (): Promise<GetMaintenanceWindow> =>
  request()
    .get('/renditions/maintenance-window')
    .then(resp => resp.data);

export const downloadConfigFile = async (
  { configFileId, configFileInstanceId }: ConfigFileParams,
  signal: AbortSignal
): Promise<IDownloadConfigFileResponse> => {
  try {
    const response = await request().get<IDownloadConfigFileResponse>(
      '/renditions/devices/config-files',
      {
        params: {
          'filters[configFileContentId][$eq]': configFileId,
          'filters[configFileInstanceId][$eq]': configFileInstanceId,
          isDownload: true,
        },
        signal,
      }
    );
    return response.data;
  } catch (error) {
    const errorMessage =
      error.response?.data?.message || 'An unknown error occurred.';
    throw new Error(errorMessage);
  }
};

// TO-DO: Remove any
export const getCustomAttributesApprovalSettings = (): Promise<any> =>
  request()
    .get('/approval/settings', {
      params: {
        pageSize: 80, // same as the page size in the custom attributes list
        filters: {
          entityType: {
            $eq: 'custom-attribute',
          },
          isEnabled: {
            $eq: true,
          },
        },
      },
    })
    .then(response => {
      const {
        data: { results },
      } = response;
      return results;
    })
    .catch(error => {
      throw error;
    });

// TO-DO: Remove any
export const getPendingForApprovalCustomAttributes = (
  siteId: string
): Promise<any> =>
  request()
    .get('/approval/data', {
      params: {
        pageSize: 80, // same as the page size in the custom attributes list
        filters: {
          'entityApprovalSettings.entityType': {
            $eq: 'custom-attribute',
          },
          'condition.siteId': {
            $in: siteId,
          },
          status: {
            $in: 'pending',
          },
        },
      },
    })
    .then(response => {
      const {
        data: { results },
      } = response;
      return results;
    })
    .catch(error => {
      throw error;
    });

// TO-DO : Remove any
export const putApprovalReject = (payload: any[]): Promise<any> =>
  request()
    .put(`/approval/data`, payload)
    .then(resp => resp.data)
    .catch(error => {
      throw error;
    });
