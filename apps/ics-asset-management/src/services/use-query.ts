import { AxiosResponse } from 'axios';
import {
  useQueries,
  useQuery,
  UseQueryOptions,
  UseQueryResult,
  useMutation,
  UseMutationResult,
  UseMutationOptions,
} from '@tanstack/react-query';
import {
  AlarmRules,
  IConfig,
  ICustomSiteAttributeResponse,
  IMismatchedConfigDevicesResponse,
  Site,
  FilesForDeploymentResponse,
  FilesForDeploymentDetailsResponse,
  PastImportByCsvHistoryResponse,
  PastImportByCsvHistoryRequest,
  GetJobsResponse,
  DottedStringResponse,
  CreateJobPayload,
  NewDevice,
  Device,
  PostSiteDetailsRequest,
  EntitySettingsResponse,
  ApplicationDashboardResponseData,
  IPaymentActionsResponse,
  NewPaymentActionJobParams,
  IPaymentActionJob,
  KeyGroups,
  ValidateSiteTagsRequest,
  ValidateSiteTagsResponse,
  GetMaintenanceWindow,
  MonitorPayload,
  PaymentDashboardResponse,
  Tags,
} from '../constants/types';
import { filterDeviceTypes } from '../utils/helpers';
import {
  ScheduleAlertReq,
  ScheduleAlertResponse,
} from '../components/ScheduleDeploymentAlert/types';
import { GetLastScheduledPayload } from '../components/ScheduleDeploymentModal/types';
import {
  getSite,
  getPaymentDashboard,
  getPaymentActions,
  getPaymentSchema,
  getDevices,
  getAllTags,
  getKeyGroups,
  getAlarmRules,
  getSiteSummary,
  getSiteIssues,
  getSiteDevicesIssues,
  getDeviceTypes,
  getCustomSiteAttributes,
  getSiteAttributeIsStaged,
  getAssetReport,
  getMismatchedConfigDevices,
  getCustomAttributesbySiteId,
  postMismatchedConfigDeployment,
  getFilesForDeployment,
  getFilesForDeploymentDetails,
  immediateConfigFilesDeployment,
  postImportByCsv,
  getImportsByCsvHistory,
  getErrorsCsvUrl,
  getErrorsCsv,
  getJobs,
  cancelJob,
  getDottedStringData,
  createJob,
  postNewDevice,
  postSiteDetails,
  getSettingsDefinitions,
  getFpsSettings,
  getApplicationDashboard,
  postPaymentActionCustomJob,
  getDeviceScheduled,
  getlastScheduled,
  getSiteAttributeIsDuplicateSitename,
  postValidateSiteTags,
  getApplicationDetails,
  getMaintenanceWindow,
  postDeviceMonitoring,
  downloadConfigFile,
  getCustomAttributesApprovalSettings,
  getPendingForApprovalCustomAttributes,
  putApprovalReject,
} from './api-request';
import configService from './configService';
import { getKeyGroupsByCompany } from './rkiService';

export const useGetConfigService = (queryOpts?: UseQueryOptions<IConfig>) =>
  useQuery(['getConfigService'], () => configService(), queryOpts);

export const useGetDevices = (
  {
    siteId,
    pageIndex = 0,
    pageSize = 0,
  }: {
    siteId: string;
    pageIndex?: number;
    pageSize?: number;
  },
  queryOpts?: UseQueryOptions<Device[]>
): UseQueryResult<Device[]> =>
  useQuery<Device[]>(
    ['getDevices', siteId, pageIndex, pageSize],
    () => getDevices(siteId, pageIndex, pageSize),
    queryOpts
  );

export const useGetDeviceScheduled = (
  data: ScheduleAlertReq,
  queryOpts?: UseQueryOptions<ScheduleAlertResponse>
) =>
  useQuery(['getDeviceScheduled', data], () => getDeviceScheduled(data), {
    ...queryOpts,
    retry: 0,
    onError: error => {
      console.error('Error fetching data:', error);
    },
  });

export const useGetDeviceTypes = () =>
  useQuery(['getDeviceTypes'], async () =>
    filterDeviceTypes(await getDeviceTypes())
  );

export const useGetSite = (siteId: string, queryOpts?: UseQueryOptions<Site>) =>
  useQuery<Site>(
    ['getSite', siteId],
    ({ signal }) => getSite(siteId, signal),
    queryOpts
  );

export const useGetKeyGroups = (
  companyId: string,
  queryOpts?: UseQueryOptions<KeyGroups>
): UseQueryResult<KeyGroups> =>
  useQuery<KeyGroups>(
    ['getKeyGroups', companyId],
    () => getKeyGroups(companyId),
    queryOpts
  );

export const useGetSiteSummary = (siteId: string) =>
  useQuery(['getSiteSummary', siteId], () => getSiteSummary(siteId));

export const useGetAllTags = (queryOpts?: UseQueryOptions<Tags>) =>
  useQuery<Tags>(['getAllTags'], () => getAllTags(), queryOpts);

export const useGetAlarmRules = (
  siteId: string,
  queryOpts?: UseQueryOptions<AlarmRules>
): UseQueryResult<AlarmRules> =>
  useQuery<AlarmRules>(
    ['getAlarmRules', siteId],
    () => getAlarmRules(siteId),
    queryOpts
  );

export const useGetKeyGroupsByCompany = (companyId: string) =>
  useQuery(['getKeyGroupsByCompany', companyId], ({ signal }) =>
    getKeyGroupsByCompany(companyId, signal)
  );

/* eslint-disable default-param-last */
export const useGetPaymentDashboard = (
  siteId: string,
  pageIndex = 0,
  pageSize = 10,
  appNameFilter,
  deviceNameFilter,
  queryOpts?: Omit<
    UseQueryOptions<
      PaymentDashboardResponse,
      unknown,
      PaymentDashboardResponse
    >,
    'queryFn' | 'queryKey'
  >
): UseQueryResult<PaymentDashboardResponse> =>
  useQuery<PaymentDashboardResponse>(
    [
      'getPaymentDashboard',
      siteId,
      pageIndex,
      pageSize,
      appNameFilter,
      deviceNameFilter,
    ],
    ({ signal }) =>
      getPaymentDashboard(
        siteId,
        pageIndex,
        pageSize,
        appNameFilter,
        deviceNameFilter,
        signal
      ),
    queryOpts
  );
/* eslint-disable default-param-last */
export const useGetApplicationDetails = (
  siteId: string,
  pageIndex = 0,
  pageSize = 10,
  applicationDashboard,
  deviceId
) =>
  useQuery(
    [
      'getApplicationDetails',
      siteId,
      pageIndex,
      pageSize,
      applicationDashboard,
      deviceId,
    ],
    ({ signal }) =>
      getApplicationDetails(
        siteId,
        pageIndex,
        pageSize,
        applicationDashboard,
        deviceId,
        signal
      ),
    { enabled: !!deviceId }
  );
/* eslint-enable */

export const useGetSiteIssues = (siteId: string, siteStatus: string) =>
  useQuery(['getSiteIssues', siteId, siteStatus], ({ signal }) =>
    getSiteIssues(siteId, siteStatus, signal)
  );

export const useGetSiteDevicesIssues = (siteId: string) =>
  useQuery(['getSiteDevicesIssues', siteId], ({ signal }) =>
    getSiteDevicesIssues(siteId, signal)
  );

export const useGetAllSiteAndDevicesIssues = (
  siteId: string,
  siteStatus: string
) =>
  useQueries({
    queries: [
      {
        queryKey: ['getSiteIssues2', siteId, siteStatus],
        queryFn: () => getSiteIssues(siteId, siteStatus, null),
      },
      {
        queryKey: ['getSiteDevicesIssues2', siteId],
        queryFn: () => getSiteDevicesIssues(siteId, null),
      },
    ],
  });

export const useGetPaymentActions = (
  deviceId: number,
  appName: string,
  queryOpts?: UseQueryOptions<Array<IPaymentActionsResponse>>
) =>
  useQuery<Array<IPaymentActionsResponse>>(
    ['getPaymentActions', deviceId, appName],
    ({ signal }) => getPaymentActions(deviceId, appName, signal),
    queryOpts
  );

export const useGetPaymentSchema = (
  schemaId: number,
  queryOpts?: UseQueryOptions
) =>
  useQuery(
    ['getPaymentSchema', schemaId],
    ({ signal }) => getPaymentSchema(schemaId, signal),
    queryOpts
  );

/* eslint-disable default-param-last */
export const useGetCustomSiteAttributes = (
  entity: string,
  entityId: string,
  page: number,
  isStaged: boolean = null,
  queryOpts?: UseQueryOptions<ICustomSiteAttributeResponse>
) =>
  useQuery<ICustomSiteAttributeResponse>(
    ['getCustomSiteAttributes', entity, entityId, page, isStaged],
    ({ signal }) =>
      getCustomSiteAttributes(entity, entityId, page, isStaged, signal),
    queryOpts
  );
/* eslint-enable */

export const useGetStagedStatus = (
  entity: string,
  entityId: string,
  queryOpts?: UseQueryOptions<boolean>
) =>
  useQuery<boolean>(
    ['getSiteAttributeIsStaged', entity, entityId],
    ({ signal }) => getSiteAttributeIsStaged(entity, entityId, signal),
    queryOpts
  );

export const useGetDuplicateSitename = (
  entity: string,
  entityId: string,
  queryOpts?: UseQueryOptions<boolean>
) =>
  useQuery<any>(
    ['getSiteAttributeIsDuplicateSitename', entity, entityId],
    ({ signal }) =>
      getSiteAttributeIsDuplicateSitename(entity, entityId, signal),
    queryOpts
  );

export const useGetAssetReport = (
  siteId: string,
  queryOpts?: UseQueryOptions<AxiosResponse>
) =>
  useQuery<AxiosResponse>(
    ['getAssetReport', siteId],
    ({ signal }) => getAssetReport(siteId, signal),
    queryOpts
  );

export const useMismatchedConfigBySiteId = (
  {
    siteId,
    onlyMeta = true,
    deploymentType,
  }: {
    siteId: string;
    onlyMeta?: boolean;
    deploymentType?: string;
  },
  queryOpts?: UseQueryOptions<IMismatchedConfigDevicesResponse>
): UseQueryResult<IMismatchedConfigDevicesResponse> =>
  useQuery<IMismatchedConfigDevicesResponse>(
    ['getMismatchedConfigBySiteId', siteId],
    ({ signal }) =>
      getMismatchedConfigDevices({ siteId, onlyMeta, deploymentType }, signal),
    queryOpts
  );

export const useCustomAttributesbySiteId = (
  { siteId }: { siteId: string },
  queryOpts?: UseQueryOptions<IMismatchedConfigDevicesResponse>
): UseQueryResult<IMismatchedConfigDevicesResponse> =>
  useQuery<IMismatchedConfigDevicesResponse>(
    ['getCustomAttributesbySiteId', siteId],
    ({ signal }) => getCustomAttributesbySiteId({ siteId }, signal),
    queryOpts
  );

export const usePostMismatchedConfigDeployment = ({
  siteId,
}): UseMutationResult =>
  useMutation(() => postMismatchedConfigDeployment({ siteId }));

export const useGetFilesForDeployment = (
  {
    sites,
  }: {
    sites: string[];
  },
  queryOpts?: UseQueryOptions<FilesForDeploymentResponse>
): UseQueryResult<FilesForDeploymentResponse> =>
  useQuery<FilesForDeploymentResponse>(
    ['getFilesForDeployment', sites],
    ({ signal }) => getFilesForDeployment(sites, signal),
    queryOpts
  );

export const useGetFilesForDeploymentDetails = (
  {
    configFileIds,
  }: {
    configFileIds: string[];
  },
  queryOpts?: UseQueryOptions<FilesForDeploymentDetailsResponse>
): UseQueryResult<FilesForDeploymentDetailsResponse> =>
  useQuery<FilesForDeploymentDetailsResponse>(
    ['getFilesForDeploymentDetails', configFileIds],
    ({ signal }) => getFilesForDeploymentDetails(configFileIds, signal),
    queryOpts
  );

export const usePutImmediateConfigFilesDeployment = (): UseMutationResult =>
  useMutation({
    mutationFn: (siteId: string) => immediateConfigFilesDeployment(siteId),
  });

export const usePostImportByCsv = (
  mutationOpts?: UseMutationOptions<number>
): UseMutationResult<number> =>
  useMutation<number>({
    mutationFn: (csvFile: FormData) => postImportByCsv(csvFile),
    ...(mutationOpts ? (mutationOpts as any) : {}),
  });

export const useGetPastImportsByCsvHistory = (
  { pageIndex, category, pageSize }: PastImportByCsvHistoryRequest,
  queryOpts?: UseQueryOptions<PastImportByCsvHistoryResponse>
) =>
  useQuery<PastImportByCsvHistoryResponse>(
    ['getImportsByCsvHistory', pageIndex, category, pageSize],
    ({ signal }) =>
      getImportsByCsvHistory({ pageIndex, category, pageSize }, signal),
    queryOpts ?? {}
  );

export const useGetErrorsCsvUrl = (
  { fileUploadId }: { fileUploadId: string },
  queryOpts?: UseQueryOptions<{ url: string }>
) =>
  useQuery<{ url: string }>(
    ['getErrorsCsvUrl', fileUploadId],
    ({ signal }) => getErrorsCsvUrl({ fileUploadId }, signal),
    queryOpts ?? {}
  );

export const useGetErrorsCsv = (
  { url }: { url: string },
  queryOpts?: UseQueryOptions<Blob>
) =>
  useQuery<Blob>(
    ['getErrorsCsv', url],
    ({ signal }) => getErrorsCsv({ url }, signal),
    queryOpts ?? {}
  );

export const useGetJobs = (
  {
    status,
    destination,
    siteId,
    jobTypes,
  }: {
    status?: number[];
    destination?: string;
    siteId?: string;
    jobTypes?: string[];
  },
  queryOpts?: UseQueryOptions<GetJobsResponse[]>
): UseQueryResult<GetJobsResponse[]> =>
  useQuery<GetJobsResponse[]>(
    ['getJobs', status, destination, siteId],
    ({ signal }) => getJobs({ signal, status, destination, siteId, jobTypes }),
    queryOpts
  );

export const useCancelJob = (
  {
    jobId,
  }: {
    jobId: string;
  },
  queryOpts?: UseQueryOptions<void>
): UseQueryResult<void> =>
  useQuery<void>(
    ['cancelJob', jobId],
    ({ signal }) => cancelJob(signal, jobId),
    queryOpts
  );

export const useGetDottedString = (
  {
    siteId,
    dottedString,
  }: {
    siteId: string;
    dottedString: string;
  },
  queryOpts?: UseQueryOptions<DottedStringResponse>
): UseQueryResult<DottedStringResponse> =>
  useQuery<DottedStringResponse>(
    ['getDottedStringData', siteId, dottedString],
    ({ signal }) =>
      getDottedStringData({
        signal,
        siteId,
        dottedString,
      }),
    queryOpts
  );

export const useGetApplicationDashboard = (
  {
    siteId,
  }: {
    siteId: string;
  },
  queryOpts?: UseQueryOptions<ApplicationDashboardResponseData>
): UseQueryResult<ApplicationDashboardResponseData> =>
  useQuery<ApplicationDashboardResponseData>(
    ['getApplicationDashboard', siteId],
    ({ signal }) =>
      getApplicationDashboard({
        signal,
        siteId,
      }),
    queryOpts
  );

export const useGetSettingsDefinitions = (
  queryOpts?: UseQueryOptions<EntitySettingsResponse>
): UseQueryResult<EntitySettingsResponse> =>
  useQuery<EntitySettingsResponse>(
    ['getSettingsDefiniteions'],
    ({ signal }) =>
      getSettingsDefinitions({
        signal,
      }),
    queryOpts
  );

export const useGetFpsSettings = (
  { siteId }: { siteId: string },
  queryOpts?: UseQueryOptions<EntitySettingsResponse>
): UseQueryResult<EntitySettingsResponse> =>
  useQuery<EntitySettingsResponse>(
    ['getFpsSettings'],
    ({ signal }) =>
      getFpsSettings({
        signal,
        siteId,
      }),
    queryOpts
  );

export const useCreateJob = (queryOpts?) =>
  useMutation((params: CreateJobPayload) => createJob(params), {
    ...queryOpts,
  });

export const usePostPaymentActionCustomJob = (
  mutationOpts?: UseMutationOptions<
    IPaymentActionJob,
    unknown,
    NewPaymentActionJobParams
  >
): UseMutationResult<IPaymentActionJob, unknown, NewPaymentActionJobParams> =>
  useMutation(
    ({ deviceFileId, payload, signal }: NewPaymentActionJobParams) =>
      postPaymentActionCustomJob(deviceFileId, payload, signal),
    {
      ...mutationOpts,
    }
  );

export const usePostDeviceMonitoring = (
  mutationOpts?: UseMutationOptions<MonitorPayload, Error, MonitorPayload>
): UseMutationResult<MonitorPayload, Error, MonitorPayload> =>
  useMutation(postDeviceMonitoring, mutationOpts);

/**
 * Create a device on site
 *
 * @param {?UseMutationOptions<Device, unknown, NewDevice>} [mutationOpts]
 * @returns {UseMutationResult<Device, unknown, NewDevice>}
 */
export const usePostNewDevice = (
  mutationOpts?: UseMutationOptions<Device, unknown, NewDevice>
): UseMutationResult<Device, unknown, NewDevice> =>
  useMutation((params: NewDevice) => postNewDevice(params), {
    ...mutationOpts,
  });

/**
 * Create a device on site
 *
 * @param {?UseMutationOptions<Site, unknown, PostSiteDetailsRequest>} [mutationOpts]
 * @returns {UseMutationResult<Site, unknown, PostSiteDetailsRequest>}
 */
export const usePostSiteDetails = (
  mutationOpts?: UseMutationOptions<Site, unknown, PostSiteDetailsRequest>
): UseMutationResult<Site, unknown, PostSiteDetailsRequest> =>
  useMutation((params: PostSiteDetailsRequest) => postSiteDetails(params), {
    ...mutationOpts,
  });

export const useGetLastScheduled = (
  payload: GetLastScheduledPayload,
  queryOpts?: UseQueryOptions
) =>
  useQuery(
    ['useGetLastScheduled', payload],
    () => getlastScheduled(payload),
    queryOpts
  );

export const usePostValidateSiteTags = (
  { siteId, siteTagIds }: ValidateSiteTagsRequest,
  mutationOpts?: UseMutationOptions<
    ValidateSiteTagsResponse,
    unknown,
    ValidateSiteTagsRequest
  >
): UseMutationResult<
  ValidateSiteTagsResponse,
  unknown,
  ValidateSiteTagsRequest
> =>
  useMutation(() => postValidateSiteTags({ siteId, siteTagIds }), {
    ...mutationOpts,
  });

export const useGetMaintenanceWindow = (
  enabledCondition: boolean
): UseQueryResult<GetMaintenanceWindow> =>
  useQuery(['getMaintenanceWindow'], () => getMaintenanceWindow(), {
    enabled: enabledCondition,
    staleTime: 15 * 1000,
    cacheTime: 15 * 1000,
  });

export interface IDownloadConfigFileResponse {
  url: string; // The URL for downloading the config file
}

// The hook to fetch the config file
export const useDownloadConfigFile = (
  {
    configFileId,
    configFileInstanceId,
  }: {
    configFileId: string | number;
    configFileInstanceId: string | number;
  },
  queryOpts?: UseQueryOptions<IDownloadConfigFileResponse>
): UseQueryResult<IDownloadConfigFileResponse> =>
  useQuery<IDownloadConfigFileResponse>(
    ['downloadConfigFile', configFileId, configFileInstanceId],
    ({ signal }) =>
      downloadConfigFile({ configFileId, configFileInstanceId }, signal),
    queryOpts
  );

// TO-DO: Remove any
export const useGetCustomAttributesApprovalSettings = (): UseQueryResult<any> =>
  // TO-DO: Remove any
  useQuery<any>(
    ['getCustomAttributesApprovalSettings'],
    () => getCustomAttributesApprovalSettings(),
    { cacheTime: 0, staleTime: 0 }
  );

// TO-DO: Remove any
export const useGetPendingForApprovalCustomAttributes = (
  siteId: string
): UseQueryResult<any> =>
  // TO-DO: Remove any
  useQuery<any>(
    ['getPendingForApprovalCustomAttributes'],
    () => getPendingForApprovalCustomAttributes(siteId),
    { cacheTime: 0, staleTime: 0 }
  );

export const usePutApprovalReject =
  // TO-DO: Remove any
  (
    mutationOpts?: UseMutationOptions<any, unknown, any[], unknown>
  ): UseMutationResult<any, unknown, any[], unknown> =>
    // TO-DO: Remove any
    useMutation({
      mutationFn: (payload: any[]) => putApprovalReject(payload),
      ...mutationOpts,
    });
