import { GridColDef } from '@mui/x-data-grid';
import type { RJSFSchema } from '@rjsf/utils';
import { UseMutationResult, UseMutationOptions } from '@tanstack/react-query';
import { Entities, DeviceStatusText, TabsNames } from './entities';
import UserRoles from './userRoles';
import FeatureFlags from './featureFlags';

export interface TabProps {
  siteId: string;
  siteTimeZone?: string;
}

export interface IcsUser {
  $id: string;
  fullName?: string | undefined;
  email?: string | undefined;
}

export interface DevicesSyncKey {
  siteId: string;
  devices: {
    id: number;
    keyGroupId: string;
  }[];
}

export interface KeyGroup {
  id: string;
  name: string;
  ref?: string;
  owner?: {
    id?: string;
    keyGroupId?: string;
  };
}

export type KeyGroups = Array<KeyGroup>;

export interface Site {
  id: string;
  name: string;
  address: string;
  contactPhone: string;
  contactEmail: string;
  latitude: string;
  longitude: string;
  formattedAddress: string;
  referenceId: string;
  status: number;
  timezoneId: string;
  visible: boolean;
  hours: Hour[];
  owner: Owner;
  keyGroup: KeyGroup;
  isDefault: boolean;
  suppressOffhoursAlarm: boolean;
  tags: Tag[];
  externalReferences?: ExternalRef[];
  allowedExternalReferenceTypes?: string[];
  disableCmAutomaticDeployments?: boolean;
  disableFileDownloads?: boolean;
}

export interface ExternalRef {
  referenceId: string;
  referenceType: string;
}
[];

export interface Hour {
  openAt: number;
  closeAt: number;
}

export interface Owner {
  id: string;
  name: string;
}

export interface Tag {
  id: number;
  name: string;
  siteCount?: number;
}

export type Tags = Array<Tag>;

export const schedule = [
  '24/7',
  '5AM - 11PM',
  '6AM - 10PM',
  '8AM - 9PM',
  '9AM - 9PM',
  'Custom',
] as const;
export type Schedule = (typeof schedule)[number];

export const week = [
  'Sunday',
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
] as const;
export type Week = (typeof week)[number];
export interface AlarmRule {
  devices: number[];
  siteId: string;
  suspendedFrom: number;
  suspendedUntil: number;
}

export type AlarmRules = Array<AlarmRule>;

export type Devices = Device[];

export enum DeviceStatus {
  INACTIVE = 0,
  OPERATIONAL = 1,
  OUT_OF_SERVICE = 2,
  UNKNOWN = 3,
}

export interface AppVersion {
  [app: string]: string;
}

export interface Device {
  id: number;
  siteId: string;
  siteName: string;
  siteKeygroup: string;
  lastRegistered?: string;
  lastContact?: string;
  name: string;
  ipAddress?: string;
  gatewayAddress: string;
  description?: string;
  serialNumber: string;
  keyGroupId: string;
  keyGroupRef: string;
  presence: string;
  status: DeviceStatus;
  releaseVersion?: string;
  appVersions?: AppVersion[];
  deviceType: DeviceType;
  hasRki: boolean;
  statusAlarmTs?: string;
  oosConditions: string;
  statusStr: string;
  isMaster: boolean;
  terminalId: string;
  isAuxiliary: boolean;
  auxStatus: string;
  inFlight: boolean;
  keyGroup?: string;
  letRKI?: boolean;
}

export interface DeviceType {
  id: string;
  name: string;
  scope?: string;
  screenSize?: string;
}

export type TabType = keyof typeof TabsNames;

/** Payment Dashboard */
export interface PaymentDashboardResponse {
  results: Array<PaymentTabCardType>;
  resultsMetadata: {
    pageIndex: number;
    pageSize: number;
    totalResults: number;
    numberOfApplications: number;
  };
}
export interface PaymentTabCardMetricTableItemType {
  label: string;
  value: string | number;
  timestamp: string;
  style: string;
  type: string;
  active?: boolean;
  deviceHealth: DeviceStatusText;
}

export interface PaymentTabCardMetricTableType {
  deviceHealth: DeviceStatusText;
  paymentDashboard: Array<PaymentTabCardMetricTableItemType>;
  breakpoints: { xs: number; sm: number; md: number };
}

export interface PaymentTabCardType {
  deviceId: number;
  deviceName: string;
  appName: string;
  deviceHealth: DeviceStatusText;
  paymentDashboard: Array<PaymentTabCardMetricTableItemType>;
  breakpoints: { xs: number; sm: number; md: number };
  toggleLabel?: string;
  isToggleOn?: boolean;
  hideToggle?: boolean;
}

export interface MonitorPayload {
  deviceId: number;
  monitoringModeOn: boolean;
}

export interface NewDevice {
  deploymentType: IUpdateCustomAttributeDeploymentType;
  deviceType: string;
  name: string;
  serialNumber: string;
  siteId: string;
}

export interface SiteIssue {
  code: string;
  modified: string;
  siteId: string;
  status: boolean;
}

export interface SiteDeviceIssue {
  code: string;
  component: string;
  deviceId: string;
  modified: string;
  siteId: string;
  status: boolean;
}

/** Payment Actions  */
export interface IPaymentActionsResponse {
  label: string;
  deviceFileId: number;
  schemaId?: number;
  contentType?: string;
  existingContentId?: string;
  existingContentUrl?: string;
  existingContentTimestamp?: string;
  existingContentRetrievalUser?: string;
  existingContentRetrievalUserName?: string;
}

/** Payment Schemas */

export type IPaymentSchemaResponse = RJSFSchema;
export interface IPaymentActionJob {
  deviceFileId: string;
  data: object;
}

/** Data Viewer */
export interface IDataViewer {
  content: unknown | string | object;
}

export interface ITableData {
  _table: Array<unknown>;
  _job_templates: Array<IJobTemplates>;
}

export interface IJobTemplates {
  column: string;
  button_label: string;
  handling: string;
  'job.type': string;
  'job.destination': string;
  'job.data.ss': string;
}

export interface SiteSummary {
  address: string;
  contactEmail: string;
  contactPhone: string;
  formattedAddress: string;
  hours: [
    {
      openAt: number;
      closeAt: number;
    },
  ];
  id: string;
  isDefault: boolean;
  keyGroup: KeyGroup;
  latitude: number;
  longitude: number;
  name: string;
  owner: {
    id: string;
    name: string;
  };
  referenceId: string;
  status: number;
  suppressOffhoursAlarm: boolean;
  tags: [
    {
      id: number;
      name: string;
    },
  ];
  timezoneId: string;
  visible: boolean;
  created: string;
}

export interface IConfig {
  api: string;
  featureFlags: FeatureFlags;
  gMapsKey: string;
}

export interface GoogleMapProps {
  center: string;
  size: string;
  markers: string;
  key: string;
}

/** Custom Job */
export interface ICustomJobPayload {
  fileUploadReqId: string;
  jobData: ICustomJobPayloadJobData;
  jobDestination?: string;
  jobType?: string;
}
export interface ICustomJobPayloadJobData {
  ids: string[];
  ss?: string;
}

export interface ICustomSiteAttributeSet {
  id: number;
  isStaged: boolean;
  groupId: number;
  groupName: string;
  name: string;
  value: string;
  createdAt: string;
  updatedAt: string;
  isSecret: boolean;
  // approval flow related fields
  requiresApproval: boolean;
  pendingApproval: boolean;
  entityApprovalSettingId: number | null;
  proposedValue?: any;
}

export interface ICustomSiteAttributeSetWithStaged
  extends ICustomSiteAttributeSet {
  doesStagedValueExist: boolean;
}

export interface ResultMetadata {
  totalResults: number;
  pageIndex: number;
  pageSize: number;
}

export interface ICustomSiteAttributeResponse {
  resultsMetadata: ResultMetadata;
  results: ICustomSiteAttributeSet[];
}

export interface ICustomAttributeDefinition {
  id: number;
  isThirdParty: boolean;
  isSearchable: boolean;
  groupId: number;
  groupName: string;
  name: string;
  schema: string;
  defaultValue: string;
  schemaDefaultValue?: string;
  createdAt: string;
  updatedAt: string;
  isSecret: boolean;
  isUserEditable: boolean;
  // approval flow related fields
  requiresApproval: boolean;
  pendingApproval: boolean;
  entityApprovalSettingId: number | null;
}

export interface ICustomAttributeDefinitionWithStaged
  extends ICustomAttributeDefinition {
  doesStagedValueExist: boolean;
}

export interface IAttributeDefinitionResponse {
  results: ICustomAttributeDefinition[];
  resultsMetadata: ResultMetadata;
}

export interface EntityCompany {
  companyId: string;
  companyName: string;
}

export interface IEntityCompanies {
  resultsMetadata: ResultMetadata;
  results: EntityCompany[];
}

export interface EntitySiteTags {
  siteCount: number;
  tagId: number;
  tagName: string;
}

export interface IEntitySiteTags {
  resultsMetadata: ResultMetadata;
  results: EntitySiteTags[];
}

export interface CustomAttributes {
  attributeDefinitionId: number;
  attributeOverrideValue: string;
}

export interface EntitySites {
  companyId: string;
  customAttributes: CustomAttributes[];
  siteId: string;
  siteName: string;
  siteTags: string[];
  visible: boolean;
}

export interface IEntitySites {
  resultsMetadata: ResultMetadata;
  results: EntitySites[];
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface EntitySelectionBase {}

export interface ITenantsSelectionRow extends EntitySelectionBase {
  companyId?: string;
  companyName?: string;
}

export interface ISiteTagsSelectionRow extends EntitySelectionBase {
  tagId?: number;
  tagName?: string;
  siteCount?: number;
}

export interface CustomAttribute {
  attributeDefinitionId: number;
  attributeOverrideValue: string;
}

export interface ISitesSelectionRow extends EntitySelectionBase {
  companyId?: string;
  siteId?: string;
  siteName?: string;
  visible?: boolean;
  siteTags?: string[];
  customAttributes?: CustomAttribute[];
}

export interface EntitySelectionGridColumns {
  tenantColumns: GridColDef[];
  siteTagColumns: GridColDef[];
  siteColumns: GridColDef[];
}

export enum IUpdateCustomAttributeDeploymentType {
  immediate = 'immediate',
  maintenanceWindow = 'maintenance-window',
  scheduled = 'schedule',
}

export enum SourceType {
  addDevice = 'AddDevice',
  moveDevice = 'MoveDevice',
  updateSiteCustomAttribute = 'UpdateSiteCustomAttribute',
  updateSiteTags = 'UpdateSiteTags',
  publishConfigInstance = 'PublishConfigInstance',
  updateAssignments = 'UpdateAssignments',
}

export interface IUpdateCustomAttributesByEntityTypePayload {
  attributes: IUpdateCustomAttributePayloadValue[];
  entityIds: string[];
  deploymentType: IUpdateCustomAttributeDeploymentType;
}

export type UpdateCustomAttributesByEntityTypeRequest = {
  entityType: Entities;
  payload: IUpdateCustomAttributesByEntityTypePayload;
};

export interface IUpdateCustomAttributePayloadValue {
  attributeDefinitionId: number;
  value: unknown;
}
export interface NavigateState {
  merchantIds: string[];
  tenants: string[];
  siteTags: string[];
  sites: string[];
  allSiteIds: string[];
}

interface VariablesUsed {
  [key: string]: string[];
}

export interface MismatchedConfigResults {
  configFileDeviceStatusId: string;
  deviceId: string;
  configFileId: string;
  tenantId: string;
  siteId: string;
  siteTagId: string[];
  variablesUsed: VariablesUsed;
  tenantConfigFileInstanceId?: string | null;
  siteTagConfigFileInstanceId: string;
  siteConfigFileInstanceId: string;
  deviceConfigFileInstanceId?: string | null;
  desiredConfigFileInstanceId: string;
  desiredRenditionHash: string;
  deployedConfigFileInstanceId?: string | null;
  deployedConfigRevisionId?: string | null;
  deployedRenditionHash?: string | null;
  isDeploying: boolean;
  lastDeployAt?: string | null;
  isNewVersionAvailable: boolean;
  isMatching?: boolean | null;
  deviceCfactHash?: string | null;
  deviceCflocHash?: string | null;
  deviceCfremHash?: string | null;
  lastDeviceReportAt?: string | null;
  createdDate: string;
  updatedBy?: string | null;
  updatedDate: string;
  deletedDate?: string | null;
  deployedJobId?: string | null;
  deployedJobStatus?: string | null;
  createdBy?: string | null;
  configFileName?: string | null;
}

export interface IMismatchedConfigDevicesResponse {
  resultsMetadata: ResultMetadata;
  results?: MismatchedConfigResults[];
}

export interface IDownloadConfigFileResponse {
  url: string; // The URL for downloading the config file
}

interface FilesForDeploymentResult {
  configFileId: string;
}

interface FilesForDeploymentResultsMetadata {
  totalResults: number;
  pageIndex: number;
  pageSize: number;
}

export interface FilesForDeploymentResponse {
  resultsMetadata: FilesForDeploymentResultsMetadata;
  results: FilesForDeploymentResult[];
}

interface FilesForDeploymentDetailsResult {
  configFileId: number;
  configFileName: string;
  configFileDottedString: string;
  configFileType: string;
}

export interface FilesForDeploymentDetailsResponse {
  totalCount: number;
  results: FilesForDeploymentDetailsResult[];
}

export enum ImportByCsvCategory {
  assignSiteTags = 'assign_site_tags',
  assignDevices = 'assign_devices',
  deleteSiteTags = 'delete_site_tags',
}

export type ImportByCsvStatus = 'in_progress' | 'completed';

export type PastImportByCsvHistoryRequest = {
  category?: ImportByCsvCategory;
  pageIndex?: number;
  pageSize?: number;
};

export type PastImportByCsvHistoryResults = {
  category: ImportByCsvCategory;
  companyId: string;
  createdBy: string;
  createdDate: string;
  deletedDate: string | null;
  errorCount: string | null;
  fileName: string;
  fileUploadId?: string | null;
  processedCount: string | null;
  status: ImportByCsvStatus;
  totalCount: string;
  updatedDate: string | null;
};

export type PastImportByCsvHistoryResponse = {
  results: PastImportByCsvHistoryResults[];
  resultsMetadata: ResultMetadata;
};
export interface GetJobsParameters {
  signal: AbortSignal;
  status?: number[];
  destination?: string;
  siteId?: string;
  jobTypes?: string[];
}

export interface CreateJobPayload {
  data: string;
  destination: string;
  deviceId: string;
  type: string;
  row: Record<string, unknown>;
}

export interface NewPaymentActionJobParams {
  deviceFileId: number;
  payload: ICustomJobPayload;
  signal?: AbortSignal;
}

export interface GetJobsResponse {
  id: string;
  deviceId: number;
  destination: string;
  type: string;
  status: number;
  expiration: string;
  embargo: string;
  data: string;
  createdOn: string;
  createdBy: string;
}

interface UseSaveSetting {
  <TError = unknown>(
    queryOpts?: UseMutationOptions<void, TError, unknown>
  ): UseMutationResult<void, TError>;
}

export interface ExtendedRJSFSchema extends RJSFSchema {
  properties?: RJSFSchema;
}

export interface UseFormSchemaReturn {
  rootResult: EntitySetting;
  formSchema: ExtendedRJSFSchema | null;
  isLoading: boolean;
  useSaveSetting: UseSaveSetting;
}

export interface EntitySetting {
  entitySettingsDefinitionsId: number;
  entityType: number;
  featureName: string;
  settingName: string;
  schema: string;
  defaultValue: number | null;
  entitySettingsId?: number;
  entityValue?: string;
}

export interface EntitySettingsResponse {
  resultsMetadata: {
    totalResults: number;
    pageIndex: number;
    pageSize: number;
  };
  results: EntitySetting[];
}

interface ResultsMetadata {
  totalResults: number;
  pageIndex: number;
  pageSize: number;
}

export interface Action {
  label: string;
  subActions?: SubAction[];
}

export interface SubAction {
  label: string;
}

export interface Application {
  appName: string;
  appDisplayName: string;
}

export interface ApplicationDevice {
  deviceId: number;
  deviceName: string;
  applications: ApplicationStatus[];
}

export interface ApplicationStatus {
  appName: string;
  status: string;
  timestamp: string;
  style: string;
}

export interface ISelectedApplication extends ApplicationStatus {
  deviceId: number;
  deviceName: string;
}

export interface ApplicationDashboardResponseData {
  resultsMetadata: ResultsMetadata;
  applications: Application[];
  results: ApplicationDevice[];
}

export interface DottedStringResponseData {
  siteId: string;
  deviceId: string;
  deviceType: string;
  dottedString: string;
  data: string;
  timestamp: string;
  schema?: string;
}

export interface DottedStringResponse {
  resultsMetadata: {
    totalResults: number;
    pageIndex: number;
    pageSize: number;
  };
  results: DottedStringResponseData[];
}

export interface DottedStringJobTemplate {
  column: string;
  button_label: string;
  'job.destination': string;
  'job.type': string;
  'job.form_schema'?: RJSFSchema;
}
export interface DottedStringData {
  title: string;
  subtitle: string;
  generated_at: string;
  _job_templates: DottedStringJobTemplate[];
  _table: { [columnName: string]: string | number }[];
}

interface FuelPriceUpdate {
  ID: string;
  ProductID: number;
  ProductName: string;
  FuelModeID: number;
  FuelModeName: string;
  PriceID: number;
  UnitPrice: string;
  PreviousUnitPrice: string;
  ReceivedDateTime: string;
  AppliedDateTime: string;
  ScheduledDateTime: string;
}

interface HistoryDeviceStatus {
  DeviceID: number;
  Result: {
    OverallResult: string;
    ErrorCode: string;
    ErrorText: string;
  };
}

export interface FuelHistoriesData {
  FuelPriceUpdate: FuelPriceUpdate;
  DeviceStatusList: HistoryDeviceStatus[];
}

export type PostSiteDetailsRequest = {
  site: Site;
  deploymentType: IUpdateCustomAttributeDeploymentType;
};

type CompanyInToken = {
  featureFlags: FeatureFlags[];
  id: string;
  name: string;
};

export type Token = IcsUser & {
  aud: string;
  company: CompanyInToken;
  created: number;
  exp: Date;
  iat: Date;
  iss: string;
  jti: string;
  nbf: Date;
  roles: UserRoles[];
  sub: string;
  type: string;
};

export type ValidateSiteTagsRequest = {
  siteId: string;
  siteTagIds: number[];
};

export type ValidateSiteTagsResponseSuccess = Boolean;

export type ValidateSiteTagsResponseBadRequestError = {
  message: string;
  property: {
    siteTagId: string;
    configFileId: number;
    configFileInstanceId: number;
  };
};

export type ValidateSiteTagsResponseNotFound = {
  status: 404;
  message: string;
  timestamp: string;
  errors: ValidateSiteTagsResponseBadRequestError[];
};

export type ValidateSiteTagsResponseBadRequest = {
  status: 400;
  message: string;
  timestamp: string;
  errors: ValidateSiteTagsResponseBadRequestError[];
};

export type ValidateSiteTagsResponse =
  | ValidateSiteTagsResponseSuccess
  | ValidateSiteTagsResponseNotFound
  | ValidateSiteTagsResponseBadRequest;

export type InstructionProps = {
  title: string;
  content: Array<{
    subTitle: string;
    body: Array<string>;
  }>;
};

export type InstructionsModalProps = {
  instructions: InstructionProps;
  isOpen: boolean;
  onClose: () => void;
};
/* Application Tab */
export interface ApplicationStatusDetailType {
  label: string;
  type: string;
  timestamp: string;
  value: string | number;
  style?: any;
  active?: boolean;
  deviceHealth: DeviceStatusText;
}

export interface ApplicationType {
  deviceHealth: DeviceStatusText;
  paymentDashboard: Array<PaymentTabCardMetricTableItemType>;
}

export interface ResultType {
  deviceId: number;
  deviceName: string;
  appName: string;
  deviceHealth: DeviceStatusText;
  paymentDashboard: Array<PaymentTabCardMetricTableItemType>;
}

export interface ResultsMetadataType {
  totalResults: number;
  pageIndex: number;
  pageSize: number;
}

export interface ApplicationDashboardResponse {
  resultsMetadata: ResultsMetadataType;
  results: Array<ResultType>;
}

export interface GetMaintenanceWindow {
  from: string;
  to: string;
  isNext: boolean;
}
export interface ConfigFileParams {
  configFileId: string | number;
  configFileInstanceId: string | number;
}

export interface ErrorType {
  errorCode: string;
  errorDescription: string;
  timeStamp: string;
}

export type ApplicationErrorItemType = Omit<
  PaymentTabCardMetricTableItemType,
  'value'
> & {
  value: string;
};

export interface ApplicationError {
  ErrorCode?: ApplicationErrorItemType;
  Resolved?: ApplicationErrorItemType;
  'Error Description'?: ApplicationErrorItemType;
}
export interface PaymentDashboardType {
  label: string;
  type: string;
  active: boolean;
  value?: any;
  timestamp: string;
  appName?: string;
}

export interface MultipleApplicationError {
  timeStamp?: PaymentDashboardType;
  status?: PaymentDashboardType;
  errorCode?: PaymentDashboardType;
  errorDescription?: PaymentDashboardType;
}
