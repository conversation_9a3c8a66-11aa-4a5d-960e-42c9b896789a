export enum TabsNames {
  Devices = 'Devices',
  Attributes = 'Attributes',
  Payments = 'Payments',
  Configuration = 'App Config',
  ApplicationsAndPayments = 'Applications & Payments',
}

export enum PaymentDashboardStyleNames {
  ORANGE = 'orange',
  GREEN = 'green',
  RED = 'red',
}

export enum PaymentDashboardTypes {
  string = 'string',
  number = 'number',
}

export enum AssetStatusKeys {
  DEVICE_OFFLINE = 'device_offline',
  PAPER_LOW = 'paper_low',
  PAPER_OUT = 'paper_out',
  PRINTER_JAM = 'printer_jam',
  SDC_DESTRUCTIVE_TAMPER = 'sdc_destructive_tamper',
  UPC_DESTRUCTIVE_TAMPER = 'upc_destructive_tamper',
  SDC_REMOVAL_TAMPER = 'sdc_removal_tamper',
  UPC_REMOVAL_TAMPER = 'upc_removal_tamper',
  DESTRUCTIVE_TAMPER = 'destructive_tamper',
  LOW_MEMORY = 'low_memory',
  EXCESSIVE_BAD_CARD_READS = 'excessive_bad_card_reads',
  EXCESSIVE_REBOOTS = 'excessive_reboots',
  SITE_DEVICES_OFFLINE = 'site_devices_offline',
  VERSION_DOWNGRADE = 'version_downgrade',
  DEVICE_UNREACHABLE = 'device_unreachable',
  DEVICE_OUT_OF_SERVICE = 'device_out_of_service',
  SITE_CRITICAL = 'site_critical',
}

export enum PaymentDashboardViewableTypes {
  'text/plain' = 'text/plain',
  'text/csv' = 'text/csv',
  'application/json' = 'application/json',
}

export enum DeviceStatusText {
  INACTIVE = 'INACTIVE',
  UNKNOWN = 'UNKNOWN',
  OUT_OF_SERVICE = 'OUT_OF_SERVICE',
  OPERATIONAL = 'OPERATIONAL',
}

export enum SiteStatus {
  INACTIVE = 0,
  NORMAL = 1,
  WARNING = 2,
  UNKNOWN = 3,
  CRITICAL = 4,
}

export enum SiteStatusText {
  INACTIVE = 'Inactive',
  NORMAL = 'Normal',
  WARNING = 'Warning',
  UNKNOWN = 'Unknown',
  CRITICAL = 'Critical',
}

export enum DevicePresence {
  OutOfInstance = 'OUT_OF_INSTANCE',
}

export enum Entities {
  Tenants = 'tenants',
  SiteTags = 'site tags',
  Sites = 'sites',
}

export enum HierarchyLevelEnum {
  Tenant = 'Tenant',
  SiteTag = 'SiteTag',
  Site = 'Site',
  Device = 'Device',
}
