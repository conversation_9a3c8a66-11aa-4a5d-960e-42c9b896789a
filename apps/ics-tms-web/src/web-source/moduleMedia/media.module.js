import angular from 'angular'; // eslint-disable-line import/no-extraneous-dependencies
import uiRouter from '@uirouter/angularjs';
import CommonModule from '../common/common-module';

export default angular
    .module( 'mediaModule', [uiRouter, CommonModule] )
    .config( ( $stateProvider ) => {
        'ngInject';

        const parent = 'media';
        // TODO: States should be separated into sub-modules
        const states = [
            {
                name: 'media',
                url: '/media',
                parent: 'main',
                template: '<div ui-view></div>',
                abstract: true,
                resolve: {
                    deviceTypes: deviceTypes,
                    mayEditMedia: mayEditMedia,
                    self: self
                }
            },
            
            {
                name: 'mediaPromptSets',
                parent: parent,
                url: '/promptsets?page&order&profile&status&query&expanded',
                template: '<prompt-sets></prompt-sets>',
                controller: 'PromptSetsCtrl',
                controllerAs: 'vm',
                bindToController: true,
                params: {
                    expanded: { dynamic: true },
                    device: { dynamic: true },
                    status: { dynamic: true },
                    order: { dynamic: true },
                    query: { dynamic: true },
                    profile: { dynamic: true }
                },
                data: {
                    titleTag: 'Prompt Sets',
                    navTitle: 'Prompt Sets'
                },
                resolve: {
                    canSeeMedia: canSeeMedia
                }
            },
            {
                name: 'editPromptSet',
                parent: 'mediaPromptSets',
                url: '/:id?readOnly?approver?showMeta',
                params: {
                    showMeta: { dynamic: true }
                },
                views: {
                    '@main': {
                        template: '<prompt-builder-2 prompt-set="vm.promptSet" device-types="vm.deviceTypes" fonts="vm.fonts"></prompt-builder-2>',
                        controller: 'EditPromptSetCtrl',
                        controllerAs: 'vm',
                        bindToController: true
                    }
                },
                resolve: {
                    dayparts: dayparts,
                    promptGlobals: promptGlobals,
                    promptTemplates: promptTemplates,
                    promptSet: promptSet,
                    touchMasks: touchMasks,
                    fonts: getAllFonts
                },
                data: {
                    titleTag: 'Prompt Set',
                    navTitle: 'Prompt Set',
                    viewClass: 'view-is-full view-site-detail',
                    appView: 'view-is-full'
                }
            },
            {
                name: 'mediaDownloads',
                parent: parent,
                url: '/downloads?search&name&page&size',
                templateUrl: 'moduleMedia/media-downloads.html',
                controller: 'MediaDownloadsCtrl',
                controllerAs: 'vm',
                bindToController: true,
                data: {
                    titleTag: 'Media Downloads',
                    navTitle: 'Media Downloads',
                    viewClass: 'view-sticky-toolbar'
                },
                resolve: {
                    canSeeMedia: canSeeMedia,
                    pagedMediaDownloadList: pagedMediaDownloadList
                }
            },
            {
                name: 'createMediaDownload',
                parent: parent,
                url: '/create?softwareID',
                templateUrl: 'moduleMedia/create-media-download.html',
                controller: 'CreateMediaDownloadCtrl',
                controllerAs: 'vm',
                data: {
                    titleTag: 'Create Media Download',
                    navTitle: 'Create Download',
                    navMobileTitle: 'Create Download',
                    viewClass: 'view-is-centered'
                },
                resolve: {
                    canSeeMedia: canSeeMedia,
                    files: files,
                    siteGroups: siteGroups
                }
            },
            {
                name: 'copyMediaDownload',
                parent: parent,
                url: '/:id/copy?failed',
                templateUrl: 'moduleMedia/copy-media-download.html',
                controller: 'CopyMediaDownloadCtrl',
                controllerAs: 'vm',
                data: {
                    titleTag: 'Copy Media Download',
                    navTitle: 'Copy Download',
                    navMobileTitle: 'Copy Download',
                    viewClass: 'view-is-centered'
                },
                resolve: {
                    canSeeMedia: canSeeMedia,
                    files: files,
                    download: download,
                    siteGroups: siteGroups
                }
            }
        ];

        return _.forEach( states, ( state ) => {
            $stateProvider.state( state );
        } );
    } )
    .name;

// ***
// Resolves
// ***

/* @ngInject */
function self( authService ) {
    return authService.self();
}

/* @ngInject */
function mayEditMedia( authService ) {
    return authService.isAllowedAccess( 'WRITE_MEDIA' );
}

/* @ngInject */
function canSeeMedia( authService, $q ) {
    return authService.self()
        .then( ( res ) => {
            if ( !( _.filter( res.roles, ( role ) => role.indexOf( 'MEDIA' ) > -1 ).length ) ) {
                return $q.reject( { status: 404 } );
            }
            return true;
        } );
}

/* @ngInject */
function dayparts( mediaService ) {
    return mediaService.getDayParts();
}

/* @ngInject */
function touchMasks( mediaService ) {
    return mediaService.getTouchMasks();
}

/* @ngInject */
function promptGlobals( mediaService ) {
    return mediaService.getPromptStateGlobals();
}

/* @ngInject */
function deviceTypes( devicesService ) {
    return devicesService.getDeviceTypes('PROMPT');
}

/* @ngInject */
function promptTemplates( mediaService ) {
    return mediaService.getPromptTemplates();
}
/* @ngInject */
function promptSet( mediaService, $stateParams ) {
    return mediaService.getPromptSet( $stateParams.id );
}

/* @ngInject */
function pagedMediaDownloadList( rolloutsService, $stateParams ) {
    const name = $stateParams.name || null,
        page = $stateParams.page || 0,
        size = $stateParams.size || 20,
        type = 'media';
    return rolloutsService.getRollouts( name, size, page, null, type );
}

/* @ngInject */
function files( softwareFileService ) {
    return softwareFileService.getAvailableSoftware( { type: 'media' } );
}

/* @ngInject */
function siteGroups( siteGroupService ) {
    return siteGroupService.getAllSiteGroups();
}

/* @ngInject */
function download( rolloutsService, $stateParams ) {
    // get sites list of this rollout
    /*eslint-disable no-param-reassign*/
    return rolloutsService.getRollout( $stateParams.id ).then( ( rollout ) =>
        rolloutsService.getDeployments( $stateParams.id )
            .then( ( sites ) => {
                if ( $stateParams.failed ) {
                    rollout.sites = _.map( sites, ( site ) => {
                        // check for failed status
                        site.devices = _.filter( site.devices, { status: 4 } );
                        return site;
                    } );
                } else {
                    rollout.sites = sites;
                }
                return rollout;
            } ) );
    /*eslint-enable no-param-reassign*/
}

/* @ngInject */
function getAllFonts( mediaService ) {
    const defaultFonts = [
        {
            fontId: 'Liberation Mono',
            name: 'Liberation Mono',
            family: 'Liberation Mono',
            type: 'DEFAULT',
            supportedDevices: [ 'G7-100', 'G6-300', 'G6-400', 'G6-500' ],
            active: true
        },
        {
            fontId: 'Liberation Sans',
            name: 'Liberation Sans',
            family: 'Liberation Sans',
            type: 'DEFAULT',
            supportedDevices: [ 'G7-100', 'G6-300', 'G6-400', 'G6-500' ],
            active: true
        },
        {
            fontId: 'Liberation Serif',
            name: 'Liberation Serif',
            family: 'Liberation Serif',
            type: 'DEFAULT',
            supportedDevices: [ 'G7-100', 'G6-300', 'G6-400', 'G6-500' ],
            active: true
        },
        {
            fontId: 'FreeMono',
            name: 'FreeMono',
            family: 'FreeMono',
            type: 'DEFAULT',
            supportedDevices: [ 'G6-200' ],
            active: true
        },
        {
            fontId: 'FreeSans',
            name: 'FreeSans',
            family: 'FreeSans',
            type: 'DEFAULT',
            supportedDevices: [ 'G6-200' ],
            active: true
        },
        {
            fontId: 'FreeSerif',
            name: 'FreeSerif',
            family: 'FreeSerif',
            type: 'DEFAULT',
            supportedDevices: [ 'G6-200' ],
            active: true
        },
        {
            fontId: 'Museo Sans',
            name: 'Museo Sans',
            family: 'Museo Sans',
            type: 'DEFAULT',
            supportedDevices: [ 'G7-100', 'G6-300', 'G6-400', 'G6-500' ],
            active: true
        },
        {
            fontId: 'Harabara',
            name: 'Harabara',
            family: 'Harabara',
            type: 'DEFAULT',
            supportedDevices: [ 'G6-200' ],
            active: true
        },
        {
            fontId: 'Source Han Sans SC',
            name: 'Source Han Sans SC',
            family: 'Source Han Sans SC',
            type: 'DEFAULT',
            supportedDevices: [ 'G7-100', 'G6-300', 'G6-400', 'G6-500' ],
            active: true
        }
    ];
    return mediaService.getAssets( { type: 'FONT', pageSize: 10000, active: 'BOTH' } )
        .then( ( fonts ) => {
            const allFonts = defaultFonts;
            fonts.results.forEach( ( font ) => {
                allFonts.push( {
                    // Fallback support IE11, IE 11 does not support UUID format font family. This is only for displaying purposes.
                    fontId: angular.element( document.querySelector( 'html' ) ).hasClass( 'ie' ) ? font.id.slice( 0, 6 ) : font.id,
                    name: font.properties.face,
                    assetName: font.name,
                    family: font.id,
                    type: 'CUSTOM',
                    supportedDevices: [ 'G7-100', 'G6-300', , 'G6-400' , 'G6-200', 'G6-500' ],
                    sourceUrl: font.sourceUrl,
                    active: font.active,
                    fileSize: font.size,
                    mimeType: font.properties.mimeType
                } );
            } );
            return allFonts;
        } );
}
