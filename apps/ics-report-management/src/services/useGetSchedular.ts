import { useQuery } from '@tanstack/react-query';

import request from './request';

export interface ApiScheduler {
  id: string;
  runs:
    | {
        createdAt: string;
        status: 'Good' | 'Failed' | 'processing' | 'queued';
      }[]
    | null;
  createdBy: string;
  recurrenttype: string;
  scheduleStartAt: string | null;
  scheduleEndAt: string | null;
  scheduledValue: string;
  recipients: string[];
  condition?: {
    reportName?: string;
  };
}

interface ApiResponse {
  resultsMetadata: {
    totalResults: number;
    pageIndex: number;
    pageSize: number;
  };
  results: ApiScheduler[];
}

const getSchedulers = async (
  pageIndex = 0,
  pageSize = 20
): Promise<ApiResponse> => {
  const query = [
    'fields[]=id',
    'fields[]=runs',
    'fields[]=createdBy',
    'fields[]=recurrentType',
    'fields[]=scheduleStartAt',
    'fields[]=scheduleEndAt',
    'fields[]=scheduledValue',
    'fields[]=recipients',
    'fields[]=condition',
    'filters[isEnabled][%24eq]=true',
    `pageIndex=${pageIndex}`,
    `pageSize=${pageSize}`,
  ].join('&');

  const { data } = await request().get<ApiResponse>(`/scheduler?${query}`);
  return data;
};

export const useGetSchedular = (pageIndex = 0, pageSize = 20) =>
  useQuery({
    queryKey: ['schedulers', pageIndex, pageSize],
    queryFn: () => getSchedulers(pageIndex, pageSize),
  });

export const mapSchedulersToRows = (data: ApiScheduler[]) =>
  data.map(item => {
    let scheduleName = '-';
    try {
      scheduleName = JSON.parse(item.scheduledValue).scheduledName ?? '-';
    } catch {
      scheduleName = item.scheduledValue;
    }

    const firstRecipient = item.recipients[0] ?? '-';
    const extraRecipients = Math.max(0, item.recipients.length - 1);

    const lastRun =
      item.runs && item.runs.length ? item.runs[item.runs.length - 1] : null;

    const fmt = (d?: string | null) =>
      d ? new Date(d).toLocaleString(undefined, { hour12: false }) : '-';

    return {
      id: item.id,
      schedule: scheduleName,
      reportType: item.condition?.reportName ?? '-',
      createdBy: item.createdBy,
      startDateTime: fmt(item.scheduleStartAt),
      endDateTime: fmt(item.scheduleEndAt),
      frequency: item.recurrenttype,
      lastRun: fmt(lastRun?.createdAt),
      status: lastRun?.status ?? '',
      invitedPeople: `${firstRecipient}${extraRecipients ? ` +${extraRecipients} more` : ''}`,
      invitedPeopleFirst: firstRecipient,
      invitedPeopleExtra: extraRecipients,
      invitedPeopleRest: item.recipients.slice(1),
    };
  });
