import {
  useMutation,
  UseMutationOptions,
  UseMutationResult,
} from '@tanstack/react-query';

import type { UserPermissionsReportRequest } from '../schemas/report/userPermissionsReport';

import request from './request';

const postUserPermissionsReportCSV = ({
  pageIndex,
  pageSize = 500,
  statuses,
  permissions,
  roles,
  user,
}: UserPermissionsReportRequest): Promise<Blob> =>
  request()
    .post('/reports/user-history-report/csv', {
      pageIndex,
      pageSize,
      statuses,
      permissions,
      roles,
      user,
    })
    .then(resp => resp?.data ?? '')
    .catch(_ => null);

const usePostUserPermissionsReportCSV = (
  _?: UserPermissionsReportRequest,
  options?: UseMutationOptions<Blob, Error, UserPermissionsReportRequest>
): UseMutationResult<Blob, Error, UserPermissionsReportRequest> =>
  useMutation({
    mutationFn: data => postUserPermissionsReportCSV(data),
    ...options,
  });

export default usePostUserPermissionsReportCSV;
