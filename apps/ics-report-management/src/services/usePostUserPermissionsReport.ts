import {
  useMutation,
  UseMutationOptions,
  UseMutationResult,
} from '@tanstack/react-query';

import {
  type UserPermissionsReportRequest,
  type UserHistoryReportResponse,
  userHistoryReportResponseSchema,
} from '../schemas/report/userPermissionsReport';
import request from './request';

const EMPTY_RESPONSE: UserHistoryReportResponse = {
  resultsMetadata: {
    pageIndex: 0,
    pageSize: 10,
    totalResults: 0,
  },
  results: [],
};

const postUserPermissionsReport = ({
  pageIndex = 0,
  pageSize = 10,
  statuses,
  permissions,
  roles,
  user,
}: UserPermissionsReportRequest): Promise<UserHistoryReportResponse> =>
  request()
    .post('/reports/user-history-report', {
      pageIndex,
      pageSize,
      statuses,
      permissions,
      roles,
      user,
    })
    .then(resp => resp?.data ?? EMPTY_RESPONSE)
    .then(data => {
      const { success } = userHistoryReportResponseSchema.safeParse(data);
      if (!success) {
        return EMPTY_RESPONSE;
      }
      return data;
    })
    .catch(_ => EMPTY_RESPONSE);

const usePostUserPermissionsReport = (
  options?: UseMutationOptions<
    UserHistoryReportResponse,
    Error,
    UserPermissionsReportRequest
  >
): UseMutationResult<
  UserHistoryReportResponse,
  Error,
  UserPermissionsReportRequest
> =>
  useMutation({
    mutationFn: (data: UserPermissionsReportRequest) =>
      postUserPermissionsReport(data),
    ...options,
  });

export default usePostUserPermissionsReport;
