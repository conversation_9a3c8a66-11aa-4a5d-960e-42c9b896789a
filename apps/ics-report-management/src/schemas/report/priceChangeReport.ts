import { ZodTuple, z } from 'zod';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

import FeatureFlags from '../../constants/featureFlags';
import ReportModules from '../../constants/reportModules';
import UserRoles from '../../constants/userRoles';
import GradeName from '../../constants/productPriceChange';
import Outcome from '../../constants/OutcomePriceChange';
import removeEmptyFromObject from '../../utils/removeEmptyFromObject';
import { userSchema } from '../system/users';
import { productNameSchema } from '../system/productName';
import { roleSchema } from '../system/roles';

dayjs.extend(isBetween);
dayjs.extend(isSameOrBefore);

const MAX_SITES = 500;
const MAX_SITE_TAGS = 500;

const RequireFeatureFlags: ZodTuple<
  [
    z.ZodLiteral<FeatureFlags.REPORTING>,
    z.ZodLiteral<FeatureFlags.ENABLE_FUEL_PRICE_MULTISITES>,
  ]
> = z.tuple([
  z.literal(FeatureFlags.REPORTING),
  z.literal(FeatureFlags.ENABLE_FUEL_PRICE_MULTISITES),
]);

const RequireRoles = z.union([
  z.literal(UserRoles.ANALYST),
  z.literal(UserRoles.COMPANY_ADMIN),
  z.literal(UserRoles.POWER_USER),
  z.literal(UserRoles.SPECIALIST),
  z.literal(UserRoles.SUPER_ADMIN),
  z.literal(UserRoles.USER),
]);

const siteTagSchema = z.object({
  tagId: z.number(),
  tagName: z.string(),
  siteCount: z.number(),
});

const siteResultSchema = z.object({
  formattedAddress: z.string(),
  siteId: z.union([z.string(), z.number()]),
  siteName: z.string(),
  siteTags: z.array(z.string().optional().nullable()),
});

const priceChangeReportSchema = z.object({
  active: z.boolean(),
  baseUrl: z.literal('reports/price-change-report'),
  description: z.string(),
  id: z.number(),
  module: z.literal(ReportModules.ForeCourtManagement),
  name: z.literal('Price Change Report'),
  requireFeatureFlags: RequireFeatureFlags,
  requireRoles: z.array(RequireRoles),
});

const DateTimeRangeSchema = z.object({
  startDate: z.string(),
  endDate: z.string(),
});

const priceChangeForm = z.object({
  gradeName: z.array(productNameSchema).optional().nullable(),
  siteIds: z.array(siteResultSchema).min(1, 'At least one site is required'),
  tagIds: z.array(siteTagSchema).optional().nullable(),
  userName: z.array(userSchema).optional().nullable(),
  dateTimeRange: DateTimeRangeSchema,
  status: z.array(roleSchema).optional().nullable(),
  amount: z
    .string()
    .trim()
    .optional()
    .nullable()
    .refine(
      amount => {
        if (amount === null || amount === undefined) {
          return true;
        }
        const parts = amount.split('.');
        return (
          parts.length === 1 || (parts.length === 2 && parts[1].length <= 3)
        );
      },
      {
        message: 'Invalid number of decimal places, must be up to 3 decimals',
      }
    ),
  pageIndex: z.number().optional().nullable(),
});

const priceChangeFormSchema = priceChangeForm.transform(
  ({
    userName,
    gradeName,
    siteIds,
    tagIds,
    amount,
    status,
    pageIndex,
    dateTimeRange,
  }) =>
    removeEmptyFromObject({
      pageIndex: pageIndex ?? 0,
      gradeName,
      userName: userName?.map(({ fullName }) => fullName) ?? [],
      siteIds: siteIds?.map(({ siteId }) => siteId) ?? [],
      tagIds: tagIds?.map(({ tagId }) => tagId) ?? [],
      amount: amount ?? null,
      status: status?.map(({ name }) => name) ?? [],
      dateTimeRange,
    })
);

type PriceChangeFormSchema = z.infer<typeof priceChangeForm>; // need to change the form and schema according to the api - will be doing in another ticket

const priceChangeReportRequestSchema = z.object({
  pageIndex: z.number().optional(),
  pageSize: z.number().optional(),
  siteIds: z.array(z.string()).optional(),
  tagIds: z.array(z.string()).optional(),
  userName: z.array(z.string()).optional(),
  gradeNames: z.array(z.nativeEnum(GradeName)).optional(),
  status: z.array(z.nativeEnum(Outcome)).optional(),
  amount: z.string().optional(),
  startDate: z.string(),
  endDate: z.string(),
});

type PriceChangeReportRequestSchema = z.infer<
  typeof priceChangeReportRequestSchema
>;

const priceHistoryReportResult = z.object({
  siteIds: z.array(z.string()).optional(),
  userIds: z.array(z.string()).optional(),
  gradeNames: z.array(z.nativeEnum(GradeName)).optional(),
  outcome: z.array(z.nativeEnum(Outcome)).optional(),
  amount: z.string().optional(),
  startDate: z.string(),
  endDate: z.string(),
});

const resultsMetadata = z.object({
  totalResults: z.number(),
  pageIndex: z.number(),
  pageSize: z.number(),
});

const priceHistoryReportResponseSchema = z.object({
  resultsMetadata,
  results: z.array(priceHistoryReportResult),
});

type PriceHistoryReportResponse = z.infer<
  typeof priceHistoryReportResponseSchema
>;

type PriceChangeReportResult = z.infer<typeof priceHistoryReportResult>;

export {
  type PriceHistoryReportResponse,
  type PriceChangeReportRequestSchema,
  type PriceChangeFormSchema,
  type PriceChangeReportResult,
  priceChangeReportSchema,
  priceChangeFormSchema,
  priceChangeReportRequestSchema,
  priceHistoryReportResponseSchema,
  MAX_SITES,
  MAX_SITE_TAGS,
};
