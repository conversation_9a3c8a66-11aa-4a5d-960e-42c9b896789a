import React, { ReactNode } from 'react';
import Tooltip from '@mui/material/Tooltip';

interface TooltipContainerProps {
  children: ReactNode;
  content: string;
  showTooltip: boolean;
}

const TooltipContainer: React.FC<TooltipContainerProps> = ({
  children,
  content,
  showTooltip,
}) => (
  <Tooltip
    title={showTooltip ? content : ''}
    disableHoverListener={!showTooltip}
  >
    <span>{children}</span>
  </Tooltip>
);

export default TooltipContainer;
