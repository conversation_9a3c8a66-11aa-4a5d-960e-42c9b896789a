import { memo, useState } from 'react';
import AssessmentIcon from '@mui/icons-material/Assessment';
import AssessmentOutlinedIcon from '@mui/icons-material/AssessmentOutlined';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import PopupState, { bindTrigger, bindMenu } from 'material-ui-popup-state';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useIsFetching } from '@tanstack/react-query';

import type { ReportSchema } from '../../schemas/system/reports';

import BaseMenu from './BaseMenu';
import BaseMenuItem from '../display/BaseMenuItem';
import ReportModules from '../../constants/reportModules';
import useGroupedReports from '../../hooks/useGroupedReports';
import useReportNavigation from '../../hooks/useReportNavigation';
import ScheduleReportDialog from './ScheduleReportDialog';

const ReportsMenu = ({
  title,
  isFormValid,
  onSchedule,
}: {
  title: string;
  isFormValid: boolean;
  onSchedule: (s: {
    scheduleName: string;
    startDateTime: Date;
    endDateTime: Date;
    recipientsList: string[];
  }) => void;
}) => {
  const { handleReportNavigation } = useReportNavigation();
  const { groupedReportsByModule, isSuccessReports } = useGroupedReports();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [, setDrawerOpen] = useState(false);
  const toggleDrawer = () => setDrawerOpen(p => !p);
  const handleToggleDrawer = () => {
    setIsDrawerOpen(prev => !prev);
  };
  const SCHEDULABLE_REPORTS = ['Payment Transaction Report'];

  const isScheduleAllowed = (reportTitle = '') =>
    SCHEDULABLE_REPORTS.some(r =>
      reportTitle?.toLowerCase().includes(r.toLowerCase())
    );

  const isFiltersLoading =
    useIsFetching({
      predicate: q => q.queryKey?.[0] === 'filterOptions',
    }) > 0;

  return (
    <>
      <PopupState variant='popover' popupId='actions-popup-menu'>
        {popupState => (
          <>
            <Box
              display='flex'
              justifyContent='flex-end'
              gap={2}
              sx={{ mb: 2 }}
            >
              {isScheduleAllowed(title) && (
                <Button
                  variant='contained'
                  color='primary'
                  onClick={handleToggleDrawer}
                  sx={{
                    marginRight: 0,
                  }}
                  disabled={isFiltersLoading || !isFormValid}
                >
                  <InfoOutlinedIcon sx={{ mr: 1 }} />
                  Schedule report
                </Button>
              )}
              {Boolean(groupedReportsByModule.length) && (
                <Button
                  endIcon={<KeyboardArrowDownIcon />}
                  variant='outlined'
                  {...bindTrigger(popupState)}
                >
                  Reports
                </Button>
              )}
            </Box>
            <BaseMenu {...bindMenu(popupState)}>
              {isSuccessReports &&
                groupedReportsByModule.length &&
                groupedReportsByModule.map(
                  ([module, reports]: [
                    module: ReportModules,
                    reports: ReportSchema[],
                  ]) => (
                    <Box key={module}>
                      <Box p={1} fontWeight='bold'>
                        {module}
                      </Box>
                      {reports
                        .filter(({ active }) => active)
                        .sort((a, b) => a.name.localeCompare(b.name))
                        .map(({ name }: ReportSchema) => {
                          const selected = name.startsWith(title);
                          return (
                            <BaseMenuItem
                              key={name}
                              onClick={() => {
                                popupState.close();
                                handleReportNavigation({ name });
                              }}
                              selected={selected}
                            >
                              <ListItemIcon>
                                {selected ? (
                                  <AssessmentIcon fontSize='small' />
                                ) : (
                                  <AssessmentOutlinedIcon fontSize='small' />
                                )}
                              </ListItemIcon>
                              <ListItemText>{name}</ListItemText>
                            </BaseMenuItem>
                          );
                        })}
                    </Box>
                  )
                )}
            </BaseMenu>
          </>
        )}
      </PopupState>
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <ScheduleReportDialog
          open={isDrawerOpen}
          onClose={handleToggleDrawer}
          onScheduleSubmit={data => {
            toggleDrawer();
            onSchedule(data);
          }}
        />
      </LocalizationProvider>
    </>
  );
};

export default memo(ReportsMenu);
