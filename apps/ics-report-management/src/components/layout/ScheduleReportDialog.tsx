import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Typo<PERSON>,
  <PERSON>con<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Chip,
  OutlinedInput,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { DateTimePicker, PickersActionBar } from '@mui/x-date-pickers';
import { PickersActionBarProps } from '@mui/x-date-pickers/PickersActionBar';

interface FormData {
  scheduleName: string;
  scheduleOptions: string;
  addRecipients: string;
  recipientsList: string[];
  startDateTime: Date | null;
  endDateTime: Date | null;
}

type ScheduleReportDialogProps = {
  open: boolean;
  onClose: () => void;
  onScheduleSubmit: (data: {
    scheduleName: string;
    startDateTime: Date;
    endDateTime: Date;
    recipientsList: string[];
  }) => void;
};

const CustomActionBar = (props: PickersActionBarProps) => {
  const { onClear, onAccept, onCancel, onSetToday } = props;

  return (
    <PickersActionBar
      {...props}
      actions={['clear', 'accept']}
      onClear={() => {
        if (onClear) onClear(); // Clear action internally resets the picker
      }}
      onAccept={onAccept}
      onCancel={onCancel}
      onSetToday={onSetToday}
    />
  );
};

const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

const addEmail = (
  raw: string,
  current: string[],
  setForm: React.Dispatch<React.SetStateAction<FormData>>,
  setErrors: React.Dispatch<
    React.SetStateAction<{ scheduleName: string; addRecipients: string }>
  >
) => {
  const email = raw.trim();
  if (!email) return;

  if (!emailRegex.test(email)) {
    setErrors(prevErrors => ({
      ...prevErrors,
      addRecipients: 'Invalid email address',
    }));
    return;
  }

  if (current.includes(email)) {
    setErrors(prevErrors => ({
      ...prevErrors,
      addRecipients: 'This email is already in the list',
    }));
    return;
  }

  setForm(prev => ({
    ...prev,
    addRecipients: '',
    recipientsList: [...prev.recipientsList, email],
  }));
  setErrors(prevErrors => ({ ...prevErrors, addRecipients: '' }));
};

const steps = ['Schedule Name', 'Schedule Options', 'Add Recipients'];
const PREVIEW_COUNT = 3;

const INITIAL_FORM: FormData = {
  scheduleName: '',
  scheduleOptions: '',
  addRecipients: '',
  recipientsList: [],
  startDateTime: null,
  endDateTime: null,
};

const ScheduleReportDialog: React.FC<ScheduleReportDialogProps> = ({
  open,
  onClose,
  onScheduleSubmit,
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [showAllRecipients, setShowAllRecipients] = useState(false);

  const [dateError, setDateError] = useState<string>('');

  const [formData, setFormData] = useState<FormData>(INITIAL_FORM);

  const [errors, setErrors] = useState<{
    scheduleName: string;
    addRecipients: string;
  }>({
    scheduleName: '',
    addRecipients: '',
  });

  const resetLocalState = () => {
    setActiveStep(0);
    setShowAllRecipients(false);
    setDateError('');
    setFormData(INITIAL_FORM);
    setErrors({ scheduleName: '', addRecipients: '' });
  };

  /** Close handler you can use everywhere inside this component */
  const handleClose = () => {
    resetLocalState(); // ⬅️ 1. clear form + stepper + errors
    onClose(); // ⬅️ 2. tell parent to hide the drawer
  };

  useEffect(() => {
    if (formData.startDateTime && formData.endDateTime) {
      if (formData.endDateTime.getTime() <= formData.startDateTime.getTime()) {
        setDateError('End Time should be more than Start Time');
      } else {
        setDateError('');
      }
    } else {
      setDateError('');
    }
  }, [formData.startDateTime, formData.endDateTime]);

  const handleNext = () => setActiveStep(s => s + 1);
  const handleBack = () => setActiveStep(s => s - 1);

  const handleChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = evt.target;

    if (name === 'scheduleName') {
      const isAlphaNum = /^[a-zA-Z0-9\s]*$/.test(value);
      setErrors(prevErrors => ({
        ...prevErrors,
        scheduleName: isAlphaNum ? '' : 'Schedule Name must be alphanumeric.',
      }));
    }
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = () => {
    if (!formData.startDateTime) return;
    onScheduleSubmit({
      scheduleName: formData.scheduleName,
      startDateTime: formData.startDateTime,
      endDateTime: formData.endDateTime,
      recipientsList: formData.recipientsList,
    });
    onClose();
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box display='flex' flexDirection='column' gap={2}>
            <TextField
              label='Schedule Name'
              name='scheduleName'
              value={formData.scheduleName}
              onChange={handleChange}
              fullWidth
              required
              error={!!errors.scheduleName}
              helperText={errors.scheduleName}
              size='small'
              autoComplete='off'
            />
            <Box display='flex' justifyContent='flex-end'>
              <Button
                variant='contained'
                size='small'
                onClick={handleNext}
                disabled={!formData.scheduleName || !!errors.scheduleName}
              >
                Next
              </Button>
            </Box>
          </Box>
        );

      case 1: {
        const now = new Date();
        const isStartDateTimeValid =
          formData.startDateTime && formData.startDateTime > now;
        return (
          <Box display='flex' flexDirection='column' gap={2}>
            <Typography variant='body1'>
              Choose the start and end date for your daily schedule.
            </Typography>
            <DateTimePicker
              label='Start Date & Time'
              value={formData.startDateTime}
              closeOnSelect={false}
              onChange={d => setFormData(p => ({ ...p, startDateTime: d }))}
              minDateTime={new Date()}
              maxDateTime={new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)}
              timeSteps={{ minutes: 30 }}
              ampm={false}
              thresholdToRenderTimeInASingleColumn={0}
              slotProps={{
                textField: { size: 'small', inputProps: { readOnly: true } },
              }}
            />

            <DateTimePicker
              label='End Date & Time'
              value={formData.endDateTime}
              closeOnSelect={false}
              onChange={d => setFormData(p => ({ ...p, endDateTime: d }))}
              thresholdToRenderTimeInASingleColumn={0}
              ampm={false}
              minDateTime={
                formData.startDateTime
                  ? new Date(formData.startDateTime.getTime() + 30 * 60 * 1000)
                  : new Date()
              }
              timeSteps={{ minutes: 30 }}
              slotProps={{
                textField: {
                  size: 'small',
                  helperText: dateError,
                  inputProps: { readOnly: true },
                },
              }}
              slots={{
                actionBar: CustomActionBar,
              }}
            />
            <Box display='flex' justifyContent='flex-end' gap={2}>
              <Button variant='outlined' onClick={handleBack} size='small'>
                Back
              </Button>
              <Button
                variant='contained'
                size='small'
                onClick={handleNext}
                disabled={
                  !formData.startDateTime ||
                  Boolean(dateError) ||
                  !isStartDateTimeValid
                }
              >
                Next
              </Button>
            </Box>
          </Box>
        );
      }
      case 2: {
        const displayedRecipients = showAllRecipients
          ? formData.recipientsList
          : formData.recipientsList.slice(0, PREVIEW_COUNT);

        return (
          <Box display='flex' flexDirection='column' gap={2}>
            <OutlinedInput
              value={formData.addRecipients}
              onChange={e =>
                setFormData(p => ({ ...p, addRecipients: e.target.value }))
              }
              onKeyDown={e => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addEmail(
                    formData.addRecipients,
                    formData.recipientsList,
                    setFormData,
                    setErrors
                  );
                }
              }}
              placeholder='Enter email and press Enter'
              fullWidth
              error={!!errors.addRecipients}
              startAdornment={
                <Box
                  sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: 0.5,
                    maxHeight: 120,
                    overflowY: 'auto',
                    overflowX: 'hidden',
                  }}
                >
                  {displayedRecipients.map(email => (
                    <Chip
                      key={email}
                      label={email}
                      onDelete={() =>
                        setFormData(p => ({
                          ...p,
                          recipientsList: p.recipientsList.filter(
                            r => r !== email
                          ),
                        }))
                      }
                      sx={{
                        maxWidth: '100%',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    />
                  ))}
                  {formData.recipientsList.length > PREVIEW_COUNT && (
                    <Chip
                      label={
                        showAllRecipients
                          ? 'show less'
                          : `+${formData.recipientsList.length - PREVIEW_COUNT} more`
                      }
                      onClick={() => setShowAllRecipients(p => !p)}
                      sx={{
                        cursor: 'pointer',
                        bgcolor: '#f0f0f0',
                        fontWeight: 500,
                      }}
                    />
                  )}
                </Box>
              }
              sx={{
                display: 'flex',
                flexWrap: 'wrap',
                alignItems: 'flex-start',
                p: 1,
                overflowY: 'auto',
              }}
            />
            <Typography variant='caption' color='error'>
              {errors.addRecipients}
            </Typography>
            <Box display='flex' justifyContent='flex-end' gap={2}>
              <Button variant='outlined' onClick={handleBack} size='small'>
                Back
              </Button>
              <Button
                variant='contained'
                size='small'
                onClick={handleSubmit}
                disabled={formData.recipientsList.length === 0}
              >
                Schedule Report
              </Button>
            </Box>
          </Box>
        );
      }

      default:
        return null;
    }
  };

  return (
    <Drawer
      anchor='right'
      hideBackdrop
      onClose={handleClose}
      open={open}
      sx={{
        '& .MuiDrawer-paper': {
          width: '30%',
          top: 52,
          bottom: 0,
          height: '100%',
        },
      }}
    >
      <Box display='flex' flex='1' flexDirection='column' overflow='hidden'>
        <Box
          alignItems='center'
          bgcolor='common.modalBackground'
          display='flex'
          justifyContent='space-between'
          px={2}
          py={1}
        >
          <Typography variant='h6'>Schedule Report</Typography>
          <IconButton onClick={handleClose} size='small'>
            <CloseIcon />
          </IconButton>
        </Box>

        <Box p={2} flex='1' display='flex' flexDirection='column'>
          <Stepper
            activeStep={activeStep}
            orientation='vertical'
            sx={{ mb: 2 }}
          >
            {steps.map((label, i) => (
              <Step key={label}>
                <StepLabel sx={{ alignItems: 'flex-start' }}>
                  {label}
                  {activeStep === i && <Box mt={2}>{renderStepContent(i)}</Box>}
                </StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>
      </Box>
    </Drawer>
  );
};

export default ScheduleReportDialog;
