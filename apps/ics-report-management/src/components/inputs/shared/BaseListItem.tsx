import Box from '@mui/material/Box';
import Checkbox from '@mui/material/Checkbox';
import Grid from '@mui/material/Grid';
import ListItem, { ListItemProps } from '@mui/material/ListItem';
import Typography from '@mui/material/Typography';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { styled } from '@mui/material/styles';

import BaseChip from '../../display/BaseChip';
import SiteStatus from '../../../constants/siteStatus';
import TooltipContainer from '../../layout/TooltipContainer';

const checkedIcon = <CheckBoxIcon fontSize='small' />;
const icon = <CheckBoxOutlineBlankIcon fontSize='small' />;

type BaseListItemProps = ListItemProps & {
  checked: boolean;
  label: string;
  labelBadge?: string;
  subLabel?: string;
  useCheckboxes?: boolean;
  key?: any;
  deviceCount?: number;
  statuses?: number;
};

const StatusBadge = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: '5px',
  padding: '2px 10px',
  borderRadius: '5px',
  fontSize: '12px',
  border: `1px solid #BDBDBD`,
});

const DeviceBadge = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  padding: '2px 10px',
  borderRadius: '5px',
  fontSize: '12px',
  border: '1px solid #BDBDBD',
  color: '#424242',
});
const renderStatusIcon = (status: SiteStatus) => {
  const iconColor = {
    [SiteStatus.Inactive]: '#607d8b',
    [SiteStatus.Normal]: '#4caf50',
    [SiteStatus.Warning]: '#ff9800',
    [SiteStatus.Unknown]: '#00000061',
    [SiteStatus.Critical]: '#f44336',
  }[status];

  return (
    <FiberManualRecordIcon
      sx={{
        fontSize: '10px',
        color: iconColor,
      }}
    />
  );
};

const BaseListItem = (props: BaseListItemProps) => {
  const {
    checked,
    children,
    label,
    labelBadge,
    onClick,
    subLabel,
    useCheckboxes,
    key,
    deviceCount,
    statuses,
    ...restProps
  } = props;
  return (
    <ListItem
      key={key}
      dense
      sx={{
        '&.MuiAutocomplete-option': {
          px: 1,
        },
      }}
      {...restProps}
      onClick={onClick} // Prevent onClick event from being overridden
    >
      <Grid alignItems='center' container flexWrap='nowrap' gap={1}>
        {useCheckboxes && (
          <Grid item flexShrink={0}>
            <Checkbox checked={checked} checkedIcon={checkedIcon} icon={icon} />
          </Grid>
        )}
        {/**
         * Min width is set to 0 to allow
         * the text to show ellipsis on overflow
         */}

        <TooltipContainer content={label} showTooltip={label.length > 1}>
          <Grid flex={1} item minWidth='0'>
            {children ?? (
              <Box>
                <Box alignItems='center' display='flex'>
                  <Typography flex={1} variant='subtitle2' noWrap>
                    {label}
                  </Typography>
                  {Boolean(labelBadge) && (
                    <BaseChip label={labelBadge} variant='outlined' />
                  )}
                </Box>

                {Boolean(subLabel) && (
                  <TooltipContainer
                    content={subLabel}
                    showTooltip={subLabel.length > 1}
                  >
                    <Typography variant='body2' color='text.secondary' noWrap>
                      {subLabel}
                    </Typography>
                  </TooltipContainer>
                )}

                <Box display='flex' alignItems='center' gap={1} mt={0.5}>
                  {!!statuses && (
                    <StatusBadge>
                      {renderStatusIcon(statuses)} {SiteStatus[statuses]}
                    </StatusBadge>
                  )}
                  {!!deviceCount && (
                    <DeviceBadge>{deviceCount} Devices</DeviceBadge>
                  )}
                </Box>
              </Box>
            )}
          </Grid>
        </TooltipContainer>
      </Grid>
    </ListItem>
  );
};

export default BaseListItem;
