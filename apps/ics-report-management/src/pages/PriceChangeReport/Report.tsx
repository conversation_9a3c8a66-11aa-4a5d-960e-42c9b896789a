import { memo, useCallback, Suspense } from 'react';
import Pagination from '@mui/material/Pagination';
import { Box } from '@mui/material';

import ReportContainer from '../../components/layout/ReportContainer';
import AGgridContainer from '../../components/layout/AGgridContainer';
import PaginationContainer from '../../components/layout/PaginationContainer';
import Form from './Form';
import lazyWithPreload from '../../utils/lazyWithPreload';
import useFormSubmit from './hooks/useFormSubmit';
import useMutateDataForRender from './hooks/useMutateDataForRender';
import encodeQueryParams from '../../utils/encodeQueryParams';
import useGetValuesFromParams from './hooks/useGetValuesFromParams';
import { PER_PAGE_SIZE } from './constants/pageContent';

const Grid = lazyWithPreload(() => import('./Grid'));

Grid.preload();

const Report = () => {
  const { valuesFromParams, setParams } = useGetValuesFromParams();
  const {
    isPendingPriceChangeReport,
    isSuccessPriceChangeReport,
    dataPriceChangeReport,
  } = useFormSubmit();

  const { page, totalResults, rowData } = useMutateDataForRender({
    data: dataPriceChangeReport,
    isDataSuccess: isSuccessPriceChangeReport,
  });

  const cPage = page + 1;
  const startWith = (cPage - 1) * PER_PAGE_SIZE + 1;
  const endWith = Math.min(
    (cPage - 1) * PER_PAGE_SIZE + PER_PAGE_SIZE,
    totalResults
  );

  const setPage = useCallback(
    (_, value: number) => {
      const newPage = value - 1;
      if (newPage === page) {
        return;
      }
      const { pageIndex, ...currentParams } = {
        ...valuesFromParams,
        page: newPage,
      };
      const newParams = encodeQueryParams(currentParams);
      setParams(newParams);
    },
    [page, setParams, valuesFromParams]
  );

  return (
    <ReportContainer>
      <Form isLoading={isPendingPriceChangeReport} />
      {isSuccessPriceChangeReport && (
        <>
          <AGgridContainer>
            <Suspense fallback={<div />}>
              <Grid results={rowData} />
            </Suspense>
          </AGgridContainer>
          {Boolean(rowData?.length) && (
            <PaginationContainer>
              <Box
                sx={{ p: '5px 30px' }}
              >{`Displaying ${startWith} - ${endWith} of ${totalResults} results`}</Box>
              <Pagination
                count={Math.ceil(totalResults / PER_PAGE_SIZE)}
                disabled={isPendingPriceChangeReport}
                onChange={setPage}
                page={page + 1}
                showFirstButton
                showLastButton
                variant='outlined'
              />
            </PaginationContainer>
          )}
        </>
      )}
    </ReportContainer>
  );
};

export default memo(Report);
