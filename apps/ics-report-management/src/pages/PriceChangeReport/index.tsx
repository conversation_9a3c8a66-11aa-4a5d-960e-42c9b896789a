import PageContent from './constants/pageContent';
import Report from './Report';
import ReportPermissionWrapper from '../../components/layout/ReportPermissionWrapper';
import usePageTitle from '../../hooks/usePageTitle';

const PAGE_TITLE = `${PageContent.REPORT_NAME}`;

const PriceChangeReport = () => {
  usePageTitle(PAGE_TITLE);

  return (
    <ReportPermissionWrapper
      pageName={PAGE_TITLE}
      reportShortName={PageContent.REPORT_SHORT_NAME}
    >
      <Report />
    </ReportPermissionWrapper>
  );
};

export default PriceChangeReport;
