import { useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';

import type { PriceChangeReportRequestSchema } from '../../../schemas/report/priceChangeReport';

import safeJsonParse from '../../../utils/safeJsonParse';
import useDebouncedUserSearch from '../../../components/inputs/UsersAutoComplete/hooks/useDebouncedUserSearch';

const useGetValuesFromParams = () => {
  const [params, setParams] = useSearchParams({
    siteIds: '',
    tageIds: '',
    amount: '',
    users: '',
    gradeNames: '',
    outcome: '',
    startDate: '',
    endDate: '',
  });

  const { results } = useDebouncedUserSearch('');

  const valuesFromParams = useMemo<PriceChangeReportRequestSchema>(() => {
    const page = params.get('page');
    const siteIds = params.get('siteIds') || params.get('siteId');
    const tagIds = params.get('tagIds') || params.get('tagId');
    const users = params.get('users') || params.get('userName');
    const gradeNames = params.get('gradeName') || params.get('productName');
    const outcome = params.get('outcome') || params.get('status');
    const dateTimeRangeString = params.get('dateTimeRange');

    const amount = params.get('amount') || params.get('price');
    const pageIndex = page ? parseInt(page, 10) : 0;
    let userNames = '';

    const decodedSiteIds = siteIds ? decodeURIComponent(siteIds) : '';
    const parsedSiteIds = safeJsonParse({
      string: decodedSiteIds,
      defaultValue: '',
    });

    const decodedTagIds = tagIds ? decodeURIComponent(tagIds) : '';
    const parsedTagIds = safeJsonParse({
      string: decodedTagIds,
      defaultValue: '',
    });

    const decodedUserIds = users ? decodeURIComponent(users) : '';
    const parsedUserIds = safeJsonParse({
      string: decodedUserIds,
      defaultValue: '',
    });

    const decodedGradeNames = gradeNames
      ? decodeURIComponent(gradeNames)
      : '[]';
    const parsedGradeNames = safeJsonParse({
      string: decodedGradeNames,
      defaultValue: [],
    });

    const decodedOutcome = outcome ? decodeURIComponent(outcome) : '';
    const parsedOutcome = safeJsonParse({
      string: decodedOutcome,
      defaultValue: [],
    });

    const decodedAmount = amount ? decodeURIComponent(amount) : '';
    const parsedAmount = safeJsonParse({
      string: decodedAmount,
      defaultValue: '',
    });

    const dateTimeRange = dateTimeRangeString
      ? JSON.parse(decodeURIComponent(dateTimeRangeString))
      : '';

    const getProductNames = parsedGradeNames[0]?.id
      ? parsedGradeNames?.map(grade => grade?.productName)
      : parsedGradeNames;

    if (parsedUserIds && parsedUserIds[0]?.indexOf('-') !== -1) {
      userNames = parsedUserIds?.map(userId => {
        const user = results.find(result => result.id === userId);
        return user ? user.fullName : null;
      });
    } else {
      userNames = parsedUserIds;
    }

    const currentValues = {
      pageIndex,
      tagId: parsedTagIds,
      siteId: parsedSiteIds,
      userName: userNames,
      status: parsedOutcome,
      productName: getProductNames,
      price: parsedAmount,
      dateTimeRange,
    } as unknown as PriceChangeReportRequestSchema;
    return currentValues;
  }, [params, results]);

  return { valuesFromParams, setParams };
};

export default useGetValuesFromParams;
