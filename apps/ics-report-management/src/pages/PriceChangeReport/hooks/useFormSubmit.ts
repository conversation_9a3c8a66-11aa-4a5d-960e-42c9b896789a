import { useEffect, useMemo, useRef } from 'react';

import usePostPriceChangeReport from '../../../services/usePostPriceChangeReport';
import useGetValuesFromParams from './useGetValuesFromParams';
import extractSubmitValues from '../utils/extractSubmitValues';
import removeEmptyFromObject from '../../../utils/removeEmptyFromObject';

const useFormSubmit = () => {
  const previousParamsRef = useRef<any>(null);
  const { valuesFromParams } = useGetValuesFromParams();

  const currentParams: any = useMemo(() => {
    const values = extractSubmitValues(valuesFromParams);
    return values;
  }, [valuesFromParams]);

  const {
    data: dataPriceChangeReport,
    mutateAsync: postPriceChangeReport,
    isPending: isPendingPriceChangeReport,
    isSuccess: isSuccessPriceChangeReport,
  } = usePostPriceChangeReport(currentParams, {
    mutationKey: ['priceChangeReport'],
  });

  const filters = {
    siteId: { $in: currentParams?.siteId },
    tagId: { $in: currentParams?.tagId },
    userName: { $in: currentParams?.userName },
    status: { $in: currentParams?.status },
    productName: { $in: currentParams?.productName },
    price: { $eq: currentParams?.price?.toString() ?? '' },
    appliedDate: {
      $gte: currentParams?.dateTimeRange?.startDate,
      $lte: currentParams?.dateTimeRange?.endDate,
    },
  };

  const currentData = {
    pageIndex: currentParams?.pageIndex,
    filters: removeEmptyFromObject(filters),
  };

  useEffect(() => {
    const hasParamsChanged =
      JSON.stringify(previousParamsRef.current) !==
      JSON.stringify(currentParams);

    if (Object.keys(currentParams).length && hasParamsChanged) {
      postPriceChangeReport(currentData);
      previousParamsRef.current = currentParams;
    }
  }, [currentParams, postPriceChangeReport]);

  return {
    isPendingPriceChangeReport,
    isSuccessPriceChangeReport,
    dataPriceChangeReport,
  };
};

export default useFormSubmit;
