import { memo, useCallback, useEffect, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import Grid from '@mui/material/Grid';
import PopupState from 'material-ui-popup-state';
import { useSnackbar } from 'notistack';
import Box from '@mui/material/Box';
import { Button } from '@mui/material';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import AssessmentOutlinedIcon from '@mui/icons-material/AssessmentOutlined';

import saveCSVlink from '../../utils/saveCSVlink';
import {
  priceChangeFormSchema,
  MAX_SITES,
  MAX_SITE_TAGS,
} from '../../schemas/report/priceChangeReport';
import BaseForm from '../../components/layout/BaseForm';
import useGetFormValues from './hooks/useGetFormValues';
import encodeQueryParams from '../../utils/encodeQueryParams';
import CustomAutocomplete from '../../components/inputs/CustomAutocomplete';
import usePostPriceChangeReportCSV from '../../services/usePostPriceChangeReportCSV';
import Outcome from '../../constants/OutcomePriceChange';
import useGetValuesFromParams from './hooks/useGetValuesFromParams';
import UsersAutocomplete from '../../components/inputs/UsersAutoComplete';
import BaseTextInput from '../../components/inputs/BaseTextInput';
import SiteTagsAutoComplete from '../../components/inputs/SiteTagsAutoComplete';
import SitesAutoCompletePagination from '../../components/inputs/SitesAutoCompletePagination';
import ProductNameAutoComplete from '../../components/inputs/ProductNameAutoComplete';
import CircularProgressWithGradient from '../../components/inputs/CircularProgressWithGradient';
import DateRangePickerWithPresets from '../../components/inputs/shared/BaseDateTimeRange';

const outcomeOptions = Object.values(Outcome);

type PriceChangeReportType = {
  isLoading?: boolean;
};

const PriceChangeReport = ({ isLoading = false }: PriceChangeReportType) => {
  const [selectedTagIds, setSelectedTagIds] = useState([]);

  const {
    control,
    getValues,
    handleSubmit,
    trigger,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(priceChangeFormSchema),
  });

  const { enqueueSnackbar } = useSnackbar();
  const { setParams } = useGetValuesFromParams();
  const getFormValues = useGetFormValues({
    getValues,
    trigger,
  });

  const {
    data: dataPriceChangeReportCSV,
    mutateAsync: postPriceChangeReportCSV,
    isPending: isPendingPriceChangeReportCSV,
    isSuccess: isSuccessPriceChangeReportCSV,
    reset: resetPostPriceChangeReportCSV,
  } = usePostPriceChangeReportCSV(
    {},
    {
      mutationKey: ['priceChangeReportCSV'],
    }
  );

  const handleSubmitAsReport = useCallback(
    data => {
      const newParams = encodeQueryParams(data);
      setParams(newParams);
    },
    [setParams]
  );

  useEffect(() => {
    if (typeof window !== 'undefined' && window.location.search) {
      window.history.replaceState({}, '', window.location.pathname);
    }
  }, []);

  const handleSubmitAsCSV = useCallback(async () => {
    const values = await getFormValues();
    if (values) {
      await postPriceChangeReportCSV(values);
    }
  }, [getFormValues, postPriceChangeReportCSV]);

  const handleDataCallback = useCallback(
    data => {
      switch (data?.status) {
        case 204:
        case 404:
          enqueueSnackbar('No data found for this report', {
            variant: 'warning',
          });
          break;
        case 301:
        case 500:
        case 502:
          enqueueSnackbar('Error generating report', {
            variant: 'error',
          });
          break;
        default:
          break;
      }
    },
    [enqueueSnackbar]
  );

  useEffect(() => {
    if (typeof window !== 'undefined' && window.location.search) {
      window.history.replaceState({}, '', window.location.pathname);
    }
    if (dataPriceChangeReportCSV && isSuccessPriceChangeReportCSV) {
      const resData: any = dataPriceChangeReportCSV;
      const resStatusCode = [204, 404, 500, 301, 502];
      if (resStatusCode.includes(resData?.status)) {
        handleDataCallback(dataPriceChangeReportCSV);
      } else {
        saveCSVlink({
          ...dataPriceChangeReportCSV,
          fileName: 'price-change-report.csv',
        });
        enqueueSnackbar('Report generated successfully', {
          variant: 'success',
        });
        resetPostPriceChangeReportCSV();
      }
    }
  }, [
    dataPriceChangeReportCSV,
    isSuccessPriceChangeReportCSV,
    handleDataCallback,
    enqueueSnackbar,
    selectedTagIds,
    resetPostPriceChangeReportCSV,
  ]);

  const handleTagSelectionChange = tags => {
    setSelectedTagIds(tags);
  };

  return (
    <BaseForm onSubmit={handleSubmit(handleSubmitAsReport)}>
      <Grid container spacing={2}>
        <Grid item xs={3}>
          <Controller
            control={control}
            defaultValue={[]}
            name='tagIds'
            render={({ field }) => (
              <SiteTagsAutoComplete
                label='Site Tags'
                maxSelected={MAX_SITE_TAGS}
                {...field}
                onChange={tags => {
                  handleTagSelectionChange(tags);
                  field.onChange(tags);
                }}
              />
            )}
          />
        </Grid>
        <Grid item xs={3}>
          <Controller
            control={control}
            defaultValue={[]}
            name='siteIds'
            render={({ field }) => (
              <SitesAutoCompletePagination
                error={Boolean(errors?.siteIds)}
                helperText={(errors?.siteIds?.message as string) ?? ''}
                label='Sites'
                maxSelected={MAX_SITES}
                siteTags={selectedTagIds}
                {...field}
              />
            )}
          />
        </Grid>
        <Grid item xs={3}>
          <Controller
            control={control}
            defaultValue={[]}
            name='userName'
            render={({ field }) => (
              <UsersAutocomplete label='Users' {...field} />
            )}
          />
        </Grid>
        <Grid item xs={3}>
          <Controller
            control={control}
            defaultValue={[]}
            name='gradeName'
            render={({ field }) => (
              <ProductNameAutoComplete
                maxSelected={50}
                label='Product Name'
                {...field}
              />
            )}
          />
        </Grid>
        <Grid item xs={3}>
          <Controller
            control={control}
            defaultValue={[]}
            name='status'
            render={({ field }) => (
              <CustomAutocomplete
                options={outcomeOptions}
                label='Outcome'
                {...field}
              />
            )}
          />
        </Grid>
        <Grid item xs={3}>
          <Controller
            control={control}
            defaultValue=''
            name='amount'
            render={({ field }) => (
              <BaseTextInput
                {...field}
                label='Amount'
                error={Boolean(errors?.amount)}
                helperText={(errors?.amount?.message as string) ?? ''}
              />
            )}
          />
        </Grid>
        <Grid item xs={3}>
          <Controller
            control={control}
            defaultValue={null}
            name='dateTimeRange'
            render={({ field }) => (
              <DateRangePickerWithPresets
                {...field}
                label='Date Range'
                onChange={dateRange => {
                  field.onChange(dateRange);
                }}
                dateRangePickerMax={90}
                timeRequired={false}
                helperText='Date Range is required'
                error={Boolean(errors?.dateTimeRange)}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} display='flex' justifyContent='flex-end'>
          <PopupState variant='popover' popupId='actions-popup-menu'>
            {popupState => (
              <div>
                <Box
                  display='flex'
                  flexDirection='row'
                  justifyContent='flex-end'
                  sx={{ mt: 2 }}
                >
                  <Button
                    variant='contained'
                    onClick={() => {
                      popupState.close();
                      handleSubmit(handleSubmitAsCSV)();
                    }}
                    disabled={isPendingPriceChangeReportCSV}
                    sx={{ mr: '10px' }}
                  >
                    {isPendingPriceChangeReportCSV ? (
                      <CircularProgressWithGradient size={24} />
                    ) : (
                      <FileDownloadOutlinedIcon />
                    )}
                    Export CSV
                  </Button>
                  <Button
                    variant='contained'
                    onClick={() => {
                      popupState.close();
                      handleSubmit(handleSubmitAsReport)();
                    }}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <CircularProgressWithGradient size={20} />
                    ) : (
                      <>
                        <AssessmentOutlinedIcon sx={{ mr: 1 }} />{' '}
                        {/* Add the Assessment icon */}
                        Run Report
                      </>
                    )}
                  </Button>
                </Box>
              </div>
            )}
          </PopupState>
        </Grid>
      </Grid>
    </BaseForm>
  );
};

export default memo(
  PriceChangeReport,
  (prevProps, nextProps) => prevProps.isLoading === nextProps.isLoading
);
