import React from 'react';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';

export const ConditionalTooltipCell: React.FC<{ value?: string }> = ({
  value = '',
}) => {
  const textRef = React.useRef<HTMLSpanElement>(null);
  const [overflow, setOverflow] = React.useState(false);

  React.useLayoutEffect(() => {
    const el = textRef.current;
    if (el) {
      setOverflow(el.scrollWidth > el.clientWidth);
    }
  }, [value]);

  return (
    <Tooltip
      title={value}
      arrow
      disableHoverListener={!overflow}
      disableFocusListener={!overflow}
      disableTouchListener={!overflow}
    >
      <Typography
        component='span'
        noWrap
        sx={{ width: '100%', display: 'inline-block' }}
        ref={textRef}
      >
        {value}
      </Typography>
    </Tooltip>
  );
};
