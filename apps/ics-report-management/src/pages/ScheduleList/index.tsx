import React, { useMemo, useState } from 'react';
import {
  Box,
  InputAdornment,
  Paper,
  Popover,
  TextField,
  Typography,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { DataGrid, GridColDef } from '@mui/x-data-grid';

import {
  mapSchedulersToRows,
  useGetSchedular,
} from '../../services/useGetSchedular';
import { ERROR_MESSAGE, PAGE_SIZE, PAGE_TITLE } from './constants/constants';
import { ConditionalTooltipCell } from './utils/helper';

type ScheduleRow = ReturnType<typeof mapSchedulersToRows>[number];

const ScheduleList: React.FC = () => {
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(PAGE_SIZE);

  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [openList, setOpenList] = useState<string[]>([]);
  const handleOpen = (e: React.MouseEvent<HTMLElement>, list: string[]) => {
    setAnchorEl(e.currentTarget);
    setOpenList(list);
  };

  const handleClose = () => setAnchorEl(null);

  const { data, isLoading, isError, isFetching } = useGetSchedular(
    page,
    pageSize
  );

  const rows = useMemo<ScheduleRow[]>(() => {
    const mapped = data ? mapSchedulersToRows(data.results) : [];

    if (!search.trim()) return mapped;
    const q = search.toLowerCase();

    return mapped.filter(row =>
      Object.values(row).some(val => String(val).toLowerCase().includes(q))
    );
  }, [data, search]);

  const total = data?.resultsMetadata.totalResults ?? 0;

  if (isError) {
    return <Typography color='error'>{ERROR_MESSAGE}</Typography>;
  }

  const columns: GridColDef[] = [
    {
      field: 'schedule',
      headerName: 'Schedule',
      minWidth: 180,
      flex: 1,
      sortable: false,
      renderCell: params => <ConditionalTooltipCell value={params.value} />,
    },
    {
      field: 'reportType',
      headerName: 'Report Type',
      minWidth: 300,
      flex: 1,
      sortable: false,
      renderCell: params => <ConditionalTooltipCell value={params.value} />,
    },
    {
      field: 'createdBy',
      headerName: 'Created By',
      minWidth: 300,
      flex: 1,
      sortable: false,
      renderCell: params => <ConditionalTooltipCell value={params.value} />,
    },
    {
      field: 'startDateTime',
      headerName: 'Start Date/Time',
      minWidth: 200,
      flex: 1,
      sortable: false,
      renderCell: params => <ConditionalTooltipCell value={params.value} />,
    },
    {
      field: 'endDateTime',
      headerName: 'End Date/Time',
      minWidth: 200,
      flex: 1,
      sortable: false,
      renderCell: params => <ConditionalTooltipCell value={params.value} />,
    },
    {
      field: 'frequency',
      headerName: 'Frequency',
      minWidth: 130,
      flex: 0.6,
      sortable: false,
      renderCell: params => <ConditionalTooltipCell value={params.value} />,
    },
    {
      field: 'lastRun',
      headerName: 'Last Run',
      minWidth: 160,
      flex: 1,
      sortable: false,
      renderCell: params => <ConditionalTooltipCell value={params.value} />,
    },
    {
      field: 'status',
      headerName: 'Status',
      minWidth: 160,
      flex: 0.6,
      sortable: false,
      renderCell: params => <ConditionalTooltipCell value={params.value} />,
    },
    {
      field: 'invitedPeople',
      headerName: 'Invited People',
      minWidth: 300,
      flex: 1,
      sortable: false,

      renderCell: params => {
        const { invitedPeopleFirst, invitedPeopleExtra, invitedPeopleRest } =
          params.row;

        return (
          <>
            {invitedPeopleFirst}
            {invitedPeopleExtra > 0 && (
              <Box
                component='span'
                sx={{ ml: 0.5, fontWeight: 700 }}
                onClick={e => handleOpen(e, invitedPeopleRest)}
              >
                {`+${invitedPeopleExtra} more`}
              </Box>
            )}
          </>
        );
      },
    },
  ];

  return (
    <Paper elevation={1} sx={{ p: 3 }}>
      {/* Header */}
      <Box
        display='flex'
        alignItems='center'
        justifyContent='space-between'
        mb={2}
      >
        <Typography variant='h6' fontWeight={600} data-testid='heading'>
          {PAGE_TITLE}
        </Typography>
      </Box>

      <Popover
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'left' }}
      >
        <Box sx={{ p: 1.5, maxWidth: 280 }}>
          {openList.map(addr => (
            <Typography
              key={addr}
              variant='body2'
              sx={{ wordBreak: 'break-all' }}
            >
              {addr}
            </Typography>
          ))}
        </Box>
      </Popover>

      {/* Search */}
      <Box mb={2} maxWidth={260}>
        <TextField
          size='small'
          fullWidth
          placeholder='Search'
          value={search}
          onChange={e => setSearch(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position='start'>
                <SearchIcon fontSize='small' />
              </InputAdornment>
            ),
            inputProps: { 'aria-label': 'search schedules' },
          }}
        />
      </Box>

      {/* Table */}
      <Box sx={{ width: '100%', overflowX: 'auto' }}>
        <DataGrid
          autoHeight
          rows={rows}
          columns={columns}
          rowCount={total}
          disableSelectionOnClick
          disableColumnSelector
          disableColumnFilter
          disableColumnMenu
          paginationMode='server'
          page={page}
          pageSize={pageSize}
          rowsPerPageOptions={[20]}
          onPageChange={newPage => setPage(newPage)}
          onPageSizeChange={newSize => {
            setPageSize(newSize);
            setPage(0);
          }}
          loading={isLoading || isFetching}
          sx={{
            '& .MuiDataGrid-row:nth-of-type(odd)': {
              backgroundColor: theme => theme.palette.action.hover,
            },
            '& .MuiDataGrid-cell:focus': {
              outline: 'none',
            },
            '& .MuiDataGrid-sortIcon': {
              display: 'none',
            },
          }}
        />
      </Box>
    </Paper>
  );
};

export default ScheduleList;
