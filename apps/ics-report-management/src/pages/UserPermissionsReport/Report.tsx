import { memo, useCallback, Suspense } from 'react';
import Pagination from '@mui/material/Pagination';

import ReportContainer from '../../components/layout/ReportContainer';
import AGgridContainer from '../../components/layout/AGgridContainer';
import PaginationContainer from '../../components/layout/PaginationContainer';
import Form from './Form';
import useFormSubmit from './hooks/useFormSubmit';
import lazyWithPreload from '../../utils/lazyWithPreload';
import useGetValuesFromParams from './hooks/useGetValuesFromParams';
import encodeQueryParams from '../../utils/encodeQueryParams';
import useMutateDataForRender from './hooks/useMutateDataForRender';
import extractSubmitValues from './utils/extractSubmitValues';

const Grid = lazyWithPreload(() => import('./Grid'));
Grid.preload();

const Report = () => {
  const { valuesFromParams, setParams } = useGetValuesFromParams();

  const {
    isPendingPermissionsReport,
    dataUserPermissionsReport,
    isSuccessPermissionsReport,
  } = useFormSubmit();

  const { page, pageCount, rowData } = useMutateDataForRender({
    data: dataUserPermissionsReport,
    isDataSuccess: isSuccessPermissionsReport,
  });

  const setPage = useCallback(
    (_, value: number) => {
      const newPage = value - 1;
      if (newPage === page) return;

      const { pageIndex, ...currentParams } = {
        ...valuesFromParams,
        page: newPage,
      };

      const newParams = encodeQueryParams(currentParams);
      setParams(newParams);
    },
    [page, setParams, valuesFromParams]
  );

  const handleRunReportClick = useCallback(() => {
    const newParams = extractSubmitValues(valuesFromParams);
    const encoded = encodeQueryParams(newParams);
    setParams(encoded);
  }, [valuesFromParams, setParams]);

  return (
    <ReportContainer>
      <Form
        isLoading={isPendingPermissionsReport}
        onRunReportClick={handleRunReportClick}
      />
      <AGgridContainer style={{ minHeight: '440px' }}>
        <Suspense fallback={<div />}>
          {isSuccessPermissionsReport && <Grid results={rowData} />}
        </Suspense>
      </AGgridContainer>
      <PaginationContainer>
        <Pagination
          count={pageCount}
          disabled={isPendingPermissionsReport}
          onChange={setPage}
          page={page + 1}
          showFirstButton
          showLastButton
          variant='outlined'
        />
      </PaginationContainer>
    </ReportContainer>
  );
};

export default memo(Report);
