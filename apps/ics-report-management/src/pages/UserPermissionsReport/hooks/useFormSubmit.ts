import { useEffect } from 'react';

import usePostUserPermissionsReport from '../../../services/usePostUserPermissionsReport';
import useGetValuesFromParams from './useGetValuesFromParams';
import extractSubmitValues from '../utils/extractSubmitValues';

const useFormSubmit = () => {
  const { valuesFromParams } = useGetValuesFromParams();

  const {
    data: dataUserPermissionsReport,
    mutateAsync: postUserPermissionsReport,
    isPending: isPendingPermissionsReport,
    isSuccess: isSuccessPermissionsReport,
  } = usePostUserPermissionsReport({
    mutationKey: ['userPermissionsReport'],
  });

  /**
   * This report is special as it is the
   * only one which runs automatically
   * on page load.
   * Instead of a `useState`, we use
   * the page's query params to handle
   * component state.
   */
  useEffect(() => {
    const params = extractSubmitValues(valuesFromParams);
    if (!params) return;
    postUserPermissionsReport(params);
  }, [valuesFromParams, postUserPermissionsReport]);

  return {
    dataUserPermissionsReport,
    isPendingPermissionsReport,
    isSuccessPermissionsReport,
  };
};

export default useFormSubmit;
