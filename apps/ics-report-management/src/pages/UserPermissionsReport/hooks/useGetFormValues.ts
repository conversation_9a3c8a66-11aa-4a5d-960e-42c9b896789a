import { useCallback } from 'react';

import extractSubmitValues from '../utils/extractSubmitValues';

const useGetFormValues = ({ getValues, trigger }) => {
  const getFormValues = useCallback(async () => {
    const isValid = await trigger();
    if (!isValid) {
      return null;
    }

    const values = getValues();
    if (values?.roles) {
      const rolesData = values.roles.map(per => per.name) ?? [];
      values.roles = rolesData;
    }

    return extractSubmitValues(values);
  }, [getValues, trigger]);

  return getFormValues;
};

export default useGetFormValues;
