import { useCallback, useRef, useState } from 'react';
import { useSnackbar } from 'notistack';

import PageContent from './constants/pageContent';
import ReportPermissionWrapper from '../../components/layout/ReportPermissionWrapper';
import usePageTitle from '../../hooks/usePageTitle';
import useFormSubmit from './hooks/useFormSubmit';
import Form, { DynamicFormHandle } from './DynamicForm';
import usePostScheduler from '../../services/usePostSchedular';

const PAGE_TITLE = `${PageContent.REPORT_NAME}`;

const PaymentTransactionReport = () => {
  usePageTitle(PAGE_TITLE);
  const [isFormValid, setIsFormValid] = useState(false);
  const formRef = useRef<DynamicFormHandle>(null);
  const { mutate: postSchedule } = usePostScheduler();
  const { enqueueSnackbar } = useSnackbar();

  const {
    isPendingPaymentTransactionReport,
    onSubmitCSVFile,
    dataPostPaymentTransactionReport,
  } = useFormSubmit();

  const handleSchedule = useCallback(
    async ({ scheduleName, startDateTime, endDateTime, recipientsList }) => {
      const filters = await formRef.current?.getFilters();
      if (!filters) {
        enqueueSnackbar('Unable to read form filters', { variant: 'error' });
        return;
      }

      const { siteTags, ...restData } = filters;

      const payload = [
        {
          entityTypeId: 7,
          condition: { report_name: PageContent.SCHEDULE_REPORT_NAME },
          scheduleStartAt: startDateTime.toISOString(),
          ...(endDateTime && { scheduleEndAt: endDateTime.toISOString() }),
          recurrentTypeId: 2,
          scheduledValue: { scheduledName: scheduleName, filters: restData },
          emailRecipients: recipientsList,
        },
      ];

      postSchedule(payload, {
        onSuccess: () =>
          enqueueSnackbar('Schedule Created', {
            variant: 'success',
            autoHideDuration: 3000,
            onExited: () => window.location.reload(),
          }),
        onError: () =>
          enqueueSnackbar('Unable to schedule report', { variant: 'error' }),
      });
    },
    [postSchedule, enqueueSnackbar]
  );

  return (
    <ReportPermissionWrapper
      pageName={PAGE_TITLE}
      reportShortName={PageContent.REPORT_SHORT_NAME}
      isFormValid={isFormValid}
      onSchedule={handleSchedule}
    >
      <Form
        ref={formRef}
        isLoading={isPendingPaymentTransactionReport}
        onSubmitCSVFile={onSubmitCSVFile}
        queryParam='paymentTransaction'
        responseData={dataPostPaymentTransactionReport}
        onFormValidityChange={setIsFormValid}
      />
    </ReportPermissionWrapper>
  );
};

export default PaymentTransactionReport;
