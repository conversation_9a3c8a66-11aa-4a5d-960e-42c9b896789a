import { useCallback } from 'react';
import dayjs from 'dayjs';

import {
  extractIds,
  extractNames,
  extractValues,
} from '../../../utils/getValuesFromArray';

const useGetDynamicValues = ({ getValues, queryParam }) => {
  const getDynamicValues = useCallback(async () => {
    const values = getValues();
    Object.keys(values).forEach(key => {
      if (values[key] && values[key] instanceof dayjs) {
        if (queryParam !== 'settlementReport') {
          values[key] = values[key].valueOf();
          const dateValue = new Date(
            values[key] - new Date().getTimezoneOffset() * 60000
          ).toISOString();
          values[key] = dateValue.substring(0, 10);
        } else {
          const formattedDate = (values[key] as dayjs.Dayjs).format(
            'YYYY-MM-DD HH:mm:ss'
          );
          values[key] = formattedDate;
        }
      }
      const keyValueMapping = {
        siteIds: 'siteIds',
        sites: 'siteId',
        siteTags: 'tagId',
        vendorMerchantId: 'attributeValue',
      };

      if (Array.isArray(values[key])) {
        if (key === 'siteIds') {
          values[key] = extractIds(values[key]);
        } else if (keyValueMapping[key]) {
          values[key] = extractValues(values[key], keyValueMapping[key]);
        } else {
          values[key] = extractNames(values[key]);
        }
      }
    });

    return values;
  }, [getValues, queryParam]);

  return getDynamicValues;
};

export default useGetDynamicValues;
