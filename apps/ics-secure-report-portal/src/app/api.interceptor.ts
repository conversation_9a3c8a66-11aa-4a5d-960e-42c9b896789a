import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { TOKEN } from './constants/api';
import { logout } from './services/logout.service';
import { UNAUTHORIZED_ERROR } from './constants/error-message';

@Injectable()
export class ApiInterceptor implements HttpInterceptor {
  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    if (request.url.includes('.s3.')) {
      return next.handle(request);
    }

    const clonedRequest = request.clone({
      headers: request.headers.set(
        'Authorization',
        `Bearer ${localStorage.getItem(TOKEN)}`
      ),
    });

    return next.handle(clonedRequest).pipe(
      catchError(err => {
        const error = err;
        error.message = null;
        if (error.status === 401) {
          error.type = 'warning';
          error.message = UNAUTHORIZED_ERROR;
          logout();
        }
        console.error('Global error handling:', error);
        throw error;
      })
    );
  }
}
