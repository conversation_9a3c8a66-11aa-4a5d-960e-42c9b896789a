<div class="row">
  <div class="col-lg-10">
    <div class="row">
      <div class="card col-lg-10 col-lg-offset-1 m-auto px-0">
        <div class="card-content">
          <mat-stepper
            orientation="vertical"
            [linear]="true"
            #stepper
            class="mat-stepper-vertical"
            (selectionChange)="onStepChange($event)"
          >
            <ng-template matStepperIcon="edit">
              <mat-icon class="check-circle">check_circle</mat-icon>
            </ng-template>
            <!-- Step 1: Choose a Sales Report -->
            <mat-step
              [stepControl]="stepOneForm"
              [label]="labels.step1"
              [completed]="stepOneForm.valid"
            >
              <div class="step-content">
                <form [formGroup]="stepOneForm">
                  <div class="card-container">
                    <!-- Search box in card -->
                    <mat-form-field class="full-width">
                      <input
                        matInput
                        placeholder="Search"
                        formControlName="searchControl"
                        maxlength="20"
                      />
                    </mat-form-field>

                    <!-- Table headers and content in same card -->
                    <div class="list-header">
                      <div class="name-column">Name</div>
                      <div
                        class="date-column sort-header"
                        (click)="toggleSort()"
                      >
                        Date Created
                        <mat-icon class="sort-icon">{{
                          sortOrder === 'asc'
                            ? 'arrow_upward'
                            : 'arrow_downward'
                        }}</mat-icon>
                      </div>
                    </div>

                    <mat-radio-group
                      formControlName="selectedReport"
                      class="report-list"
                    >
                      <mat-list>
                        <mat-list-item *ngFor="let report of secureReports">
                          <div
                            class="list-item"
                            (click)="onReportSelect(report)"
                          >
                            <mat-radio-button
                              [value]="report"
                              [checked]="
                                selectedReport?.fileName === report.fileName
                              "
                            ></mat-radio-button>
                            <div class="name-column">{{ report.fileName }}</div>
                            <div class="date-column date-data">
                              {{ report.isAvailableForDownloadAt }}
                            </div>
                          </div>
                        </mat-list-item>
                      </mat-list>
                    </mat-radio-group>

                    <mat-paginator
                      [length]="pagination.totalItems"
                      [pageSize]="pagination.pageSize"
                      [pageIndex]="pagination.pageIndex - 1"
                      (page)="onPageChange($event)"
                      showFirstLastButtons
                    >
                    </mat-paginator>
                  </div>

                  <div class="actions step-btn-container">
                    <button
                      type="button"
                      class="btn btn-primary btn-box-shadow"
                      [disabled]="!stepOneForm.valid"
                      (click)="generatePreSignedUrl()"
                    >
                      Continue
                    </button>
                  </div>
                </form>
              </div>
            </mat-step>

            <!-- Step 2: Provide your decryption key -->

            <mat-step
              [stepControl]="stepTwoForm"
              [label]="labels.step2"
              [aria-labelledby]="
                stepOneForm.valid ? 'cursor-pointer' : 'cursor-not-allowed'
              "
              [completed]="stepTwoForm.valid"
            >
              <div class="step-content">
                <form [formGroup]="stepTwoForm">
                  <div class="file-upload-field">
                    <div class="file-upload-label-container">
                      <label class="file-upload-label"
                        >Provide Your Decryption Key</label
                      >
                      <div class="info-icon-container">
                        <svg
                          class="info-icon"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <circle
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            stroke-width="2"
                          />
                          <path
                            d="M12 16v-4"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                          />
                          <path
                            d="M12 8h.01"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                          />
                        </svg>
                        <div class="tooltip">Only PEM files are allowed</div>
                      </div>
                    </div>
                    <div class="file-upload-container">
                      <span class="file-upload-info">{{
                        selectedFileName
                      }}</span>
                      <button
                        type="button"
                        class="file-upload-button"
                        (click)="triggerFileInput($event)"
                      >
                        Browse
                      </button>
                      <input
                        #fileInput
                        type="file"
                        accept=".pem"
                        class="hidden-file-input"
                        id="file-upload"
                        (change)="handleFileChange($event)"
                      />
                    </div>
                  </div>
                  <mat-form-field appearance="outline" class="half-width">
                    <mat-label>Enter Your Passphrase</mat-label>
                    <input
                      matInput
                      formControlName="passPhrase"
                      type="password"
                      aria-invalid="false"
                      aria-required="false"
                      autocomplete="new-password"
                    />
                  </mat-form-field>
                </form>

                <div class="actions step-btn-container">
                  <button
                    class="btn btn-primary btn-box-shadow"
                    matStepperPrevious
                  >
                    Back
                  </button>
                  <button
                    class="btn btn-primary btn-box-shadow"
                    [disabled]="!stepTwoForm.valid"
                    (click)="decryptFile()"
                  >
                    Continue
                  </button>
                </div>
              </div>
            </mat-step>

            <!-- Step 3-: Download File -->
            <mat-step
              [stepControl]="stepThreeForm"
              [label]="labels.step3"
              [aria-labelledby]="
                stepTwoForm.valid ? 'cursor-pointer' : 'cursor-not-allowed'
              "
              [completed]="stepThreeForm.valid"
            >
              <div class="step-content">
                <p>Your file is ready for download.</p>

                <div class="actions step-btn-container">
                  <button
                    class="btn btn-primary btn-box-shadow"
                    matStepperPrevious
                  >
                    Back
                  </button>
                  <button
                    class="btn btn-primary btn-box-shadow"
                    (click)="downloadDecryptedFile()"
                  >
                    Download
                  </button>
                </div>
              </div>
            </mat-step>
          </mat-stepper>
        </div>
      </div>
    </div>
  </div>
</div>
<div
  class="error-toaster"
  [class.show]="show"
  [class.hide]="hide"
  [ngClass]="{
    warning: errorType === 'warning',
    error: errorType !== 'warning',
  }"
>
  {{ errorMessage }}
</div>

<app-ics-loader *ngIf="isLoading"></app-ics-loader>
