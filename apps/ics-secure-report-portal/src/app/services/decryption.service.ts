import { Injectable } from '@angular/core';
import * as forge from 'node-forge';
import {
  INVALID_DECRYPTION_KEY_ERROR,
  INVALID_ENCRYPTION_ERROR,
  INVALID_PASSPHRASE_PRIVATE_KEY_ERROR,
  INVALID_PRIVATE_KEY_ERROR,
  KEY_NOT_FOUND,
} from '../constants/error-message';

@Injectable({
  providedIn: 'root',
})
export class DecryptionService {
  private privateKey: forge.pki.PrivateKey | null = null;

  loadPrivateKey(privateKeyPem: string, passphrase?: string): void {
    try {
      if (passphrase) {
        const decryptedPem = forge.pki.decryptRsaPrivateKey(
          privateKeyPem,
          passphrase
        );

        if (!decryptedPem)
          throw new Error(INVALID_PASSPHRASE_PRIVATE_KEY_ERROR);
        this.privateKey = decryptedPem;
      } else {
        this.privateKey = forge.pki.privateKeyFromPem(privateKeyPem);
      }
    } catch (error) {
      console.error('Invalid private key PEM:', error);
      this.privateKey = null;
      if (passphrase) {
        throw new Error(INVALID_PASSPHRASE_PRIVATE_KEY_ERROR);
      } else {
        throw new Error(INVALID_PRIVATE_KEY_ERROR);
      }
    }
  }

  decryptPkcs7(encryptedPem: string): string {
    if (!this.privateKey) {
      throw new Error(KEY_NOT_FOUND);
    }

    const p7Msg: unknown = forge.pkcs7.messageFromPem(encryptedPem);
    const p7 = p7Msg as {
      type: string;
      recipients: string[];
      content: string;
      decrypt: (recipient: string, key: forge.pki.PrivateKey | null) => void;
    };

    if (p7.type !== forge.pki.oids['envelopedData']) {
      throw new Error(INVALID_ENCRYPTION_ERROR);
    }

    let decryptedContent: string | null = null;

    const success = p7.recipients.some(recipient => {
      try {
        p7.decrypt(recipient, this.privateKey);
        decryptedContent = p7.content.toString();
        return true;
      } catch {
        return false;
      }
    });

    if (!success || decryptedContent === null) {
      throw new Error(INVALID_DECRYPTION_KEY_ERROR);
    }

    return decryptedContent;
  }
}
