import { jwtDecode } from 'jwt-decode';

interface UserToken {
  roles: string[];
  company: {
    id: string;
    name: string;
    featureFlags: string[];
  };
  sub: string;
  // other properties as needed
}

export const decodeToken = (): UserToken | null => {
  try {
    return jwtDecode<UserToken>(localStorage.getItem('token') as string);
  } catch (err) {
    console.error(err);
  }
  return null;
};

export const isBankUser = () => {
  const decodedToken = decodeToken();
  return decodedToken?.roles[0] === 'BANK_USER';
};
