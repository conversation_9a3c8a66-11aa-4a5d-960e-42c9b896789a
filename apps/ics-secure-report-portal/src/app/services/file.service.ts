import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { lastValueFrom, Observable } from 'rxjs';
import { BASE_URL } from '../constants/api';
import { SecureReportApi } from '../types/secureReport.type';
import { DEFAULT_QUERY_PARAMS } from '../constants/secure-report';

@Injectable({
  providedIn: 'root',
})
export class FileService {
  private dailyReportUrl: string;

  private preSignedUrl: string;

  constructor(private http: HttpClient) {
    this.dailyReportUrl = `${BASE_URL}/daily-report`;
    this.preSignedUrl = `${BASE_URL}/presignedUrl`;
  }

  getFiles({
    search,
    pageIndex,
    pageSize,
    sortOrder,
  }: {
    search?: string;
    pageIndex: number;
    pageSize: number;
    sortOrder: string;
  } = DEFAULT_QUERY_PARAMS): Observable<SecureReportApi> {
    let params = new HttpParams()
      .set('pageIndex', pageIndex.toString())
      .set('pageSize', pageSize.toString())
      .set('sortOrder', sortOrder);
    if (search) {
      params = params.set('search', search);
    }
    return this.http.get<SecureReportApi>(this.dailyReportUrl, { params });
  }

  getPresignedUrl(id: string): Observable<{ s3Url: string; success: boolean }> {
    return this.http.get<{ s3Url: string; success: boolean }>(
      `${this.preSignedUrl}/${id}`
    );
  }

  downloadZip(presignedUrl: string): Promise<Blob> {
    return lastValueFrom(this.http.get(presignedUrl, { responseType: 'blob' }));
  }
}
