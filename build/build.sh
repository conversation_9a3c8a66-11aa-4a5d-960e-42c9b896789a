#!/usr/bin/env bash
set -e

if [[ -z "$1" ]]; then
    echo "Build number argument is required"
    echo "$0 <build number>"
    exit 1
fi

build_number="$1"
echo -e "\n-----\nBeginning build: $build_number\n-----\n"

echo "Loading build.properties"
current_dir=$(dirname $0)
source $current_dir/build.properties

echo "Remove old zips"
rm -v -f *.zip

for brand_path in target/*; do
    brand=${brand_path##*/}
    echo -e "\n--- $brand ---"
    
    echo "Write version file"
    echo "Build ${build_number//-build} -- $brand" > $brand_path/version.txt
    date -u >> $brand_path/version.txt
    echo "-- $(git show --pretty=format:'%h') --"
    
    echo "Create a zip package"
    pushd $brand_path >/dev/null
    package_name="${package_name_prefix}_${brand}-${build_number}-$(date +%s)-local.zip"
    zip -rq "$package_name" .
    popd >/dev/null

    echo "Move zip to the current location ./$package_name"
    mv "$brand_path/$package_name" "./$package_name"

done

echo -e "\n-----\nBuild complete\n-----\n"
