import { registerApplication, start } from 'single-spa';

const showWhenAnyOf = routes => location =>
  routes.some(route => {
    if (route instanceof RegExp) {
      return route.test(location.pathname);
    }
    return location.pathname.startsWith(route);
  });

/**
 * Get Local Storage values generated by TMS-WEB
 * @returns {Object} Local Storage
 */
const getBrowserConfigs = () => ({
  api: localStorage.getItem('api'),
  appFeatureFlags: localStorage.getItem('appFeatureFlags'),
  flags: localStorage.getItem('flags'),
  token: localStorage.getItem('token'),
});

// TO-DO : Handle routes from index.ejs for the whole ICS
const whitelistedURLs = [
  /^\/$/,
  /^\/sessions\/two-factor$/,
  /^\/signup$/,
  /^\/resetpassword$/,
  /^\/resend-password$/,
  /^\/resend-password\/reset-email-sent$/,
  /^\/legal\/privacy$/,
  /^\/legal\/terms$/,
  /^\/remote\/packages$/,
  /^\/remote\/packages\/create$/,
  /^\/remote\/downloads$/,
  /^\/remote\/downloads\/create$/,
  /^\/remote\/downloads\/[^/]+\/copy$/,
  /^\/remote\/downloads\/[^/]+$/,
  /^\/settings$/,
  /^\/settings\/account$/,
  /^\/settings\/reset-password$/,
  /^\/settings\/reset-password\/two-factor$/,
  /^\/remote\/library$/,
  /^\/[^/]+\/settings(?:\/.*)?$/,
  /^\/remote\/bulk-operations$/,
  /^\/remote\/bulk-operations\/two-factor$/,
  /^\/legal\/licenses$/,
  /^\/release-notes$/,
  /^\/notifications$/,
  /^\/rki$/,
  /^\/rki\/[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-4[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/,
  /^\/rki\/create$/,
  /^\/sites\/add$/,
  /^\/media\/library$/,
];
registerApplication({
  name: '@ics-tms-web/angular-project',
  app: () => System.import('@ics-tms-web/angular-project'),
  activeWhen: location =>
    whitelistedURLs.some(pattern => pattern.test(location.pathname)),
  customProps: getBrowserConfigs(),
});

registerApplication({
  name: '@ics/secure-report-portal',
  app: () => System.import('@ics/secure-report-portal'),
  activeWhen: showWhenAnyOf(['/secure-reports']),
  customProps: getBrowserConfigs(),
});

registerApplication({
  name: '@ics/playlist',
  app: () => System.import('@ics/playlist'),
  activeWhen: showWhenAnyOf([
    '/playlist',
    '/playlist/content',
    '/playlist/coupon',
    /\/playlist\/builder\/.*.{36,36}/,
  ]),
  customProps: getBrowserConfigs(),
});

registerApplication({
  name: '@ics/dashboard',
  app: () => System.import('@ics/dashboard'),
  activeWhen: showWhenAnyOf(['/dashboard']),
  customProps: getBrowserConfigs(),
});

// TO-DO : Enable this later
// registerApplication({
//   name: "@ics/prompt-set",
//   app: () => System.import("@ics/prompt-set"),
//   activeWhen: "/media/promptsets/:id",
//   customProps: getBrowserConfigs(),
// });

registerApplication({
  name: '@ics/config-management',
  app: () => System.import('@ics/config-management'),
  activeWhen: showWhenAnyOf([
    /\/config-management/,
    /^\/config-management.*\b/,
  ]),
  customProps: getBrowserConfigs(),
});

registerApplication({
  name: '@ics/asset-management',
  app: () => System.import('@ics/asset-management'),
  activeWhen: showWhenAnyOf([
    /\/asset-management/,
    /^\/asset-management.*\b/,
    /^\/sites\/.*.{36,36}\/payments\b/,
  ]),
  customProps: getBrowserConfigs(),
});

registerApplication({
  name: '@ics/report-management',
  app: () => System.import('@ics/report-management'),
  activeWhen: showWhenAnyOf([
    /\/report-management/,
    /^\/report-management.*\b/,
    /^\/schedule-list.*\b/,
  ]),
  customProps: getBrowserConfigs(),
});

registerApplication({
  name: '@ics/fuel-price-management',
  app: () => System.import('@ics/fuel-price-management'),
  activeWhen: showWhenAnyOf([
    /\/fuel-price-management/,
    /^\/fuel-price-management.*\b/,
  ]),
  customProps: getBrowserConfigs(),
});

start({
  urlRerouteOnly: true,
});

// if (process.env.NODE_ENV === 'development') {
/* eslint-disable no-useless-escape */
//   console.log(
//     `%c    _______________       _   _________  ________
//    /  _/ ____/ ___/      / | / / ____/ |/ /_  __/
//    / // /    \__ \______/  |/ / __/  |   / / /
//  _/ // /___ ___/ /_____/ /|  / /___ /   | / /
// /___/\____//____/     /_/ |_/_____//_/|_|/_/
//                                                  \n VERSION 1.0 - THE NEXT GENERATION UI FRAMEWORK`,
//     `color: #3f51b5;`
//   );
/* eslint-enable */
// }
